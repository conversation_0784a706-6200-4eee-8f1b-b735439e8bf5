{"version": 3, "sources": ["../../../src/primitives/attachment/AttachmentThumb.tsx"], "sourcesContent": ["\"use client\";\n\nimport { ComponentPropsWithoutRef, forwardRef, type ComponentRef } from \"react\";\nimport { useAttachment } from \"../../context/react/AttachmentContext\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\n\ntype PrimitiveDivProps = ComponentPropsWithoutRef<typeof Primitive.div>;\n\nexport namespace AttachmentPrimitiveThumb {\n  export type Element = ComponentRef<typeof Primitive.div>;\n  export type Props = PrimitiveDivProps;\n}\n\nexport const AttachmentPrimitiveThumb = forwardRef<\n  AttachmentPrimitiveThumb.Element,\n  AttachmentPrimitiveThumb.Props\n>((props, ref) => {\n  const ext = useAttachment((a) => a.name.split(\".\").pop());\n  return (\n    <Primitive.div {...props} ref={ref}>\n      .{ext}\n    </Primitive.div>\n  );\n});\n\nAttachmentPrimitiveThumb.displayName = \"AttachmentPrimitive.Thumb\";\n"], "mappings": ";;;AAEA,SAAmC,kBAAqC;AACxE,SAAS,qBAAqB;AAC9B,SAAS,iBAAiB;AAetB;AANG,IAAM,2BAA2B,WAGtC,CAAC,OAAO,QAAQ;AAChB,QAAM,MAAM,cAAc,CAAC,MAAM,EAAE,KAAK,MAAM,GAAG,EAAE,IAAI,CAAC;AACxD,SACE,qBAAC,UAAU,KAAV,EAAe,GAAG,OAAO,KAAU;AAAA;AAAA,IAChC;AAAA,KACJ;AAEJ,CAAC;AAED,yBAAyB,cAAc;", "names": []}