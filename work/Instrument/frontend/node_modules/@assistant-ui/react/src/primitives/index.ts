export * as ActionBarPrimitive from "./actionBar";
export * as AssistantModalPrimitive from "./assistantModal";
export * as AttachmentPrimitive from "./attachment";
export * as BranchPickerPrimitive from "./branchPicker";
export * as ComposerPrimitive from "./composer";
export * as MessagePartPrimitive from "./messagePart";
export * as ErrorPrimitive from "./error";
export * as MessagePrimitive from "./message";
export * as ThreadPrimitive from "./thread";
export * as ThreadListPrimitive from "./threadList";
export * as ThreadListItemPrimitive from "./threadListItem";

export { useMessagePartText } from "./messagePart/useMessagePartText";
export { useMessagePartReasoning } from "./messagePart/useMessagePartReasoning";
export { useMessagePartSource } from "./messagePart/useMessagePartSource";
export { useMessagePartFile } from "./messagePart/useMessagePartFile";
export { useMessagePartImage } from "./messagePart/useMessagePartImage";
export { useThreadViewportAutoScroll } from "./thread/useThreadViewportAutoScroll";

// TODO remove in v0.11
export * as ContentPartPrimitive from "./messagePart";
export { useMessagePartText as useContentPartText } from "./messagePart/useMessagePartText";
export { useMessagePartReasoning as useContentPartReasoning } from "./messagePart/useMessagePartReasoning";
export { useMessagePartSource as useContentPartSource } from "./messagePart/useMessagePartSource";
export { useMessagePartFile as useContentPartFile } from "./messagePart/useMessagePartFile";
export { useMessagePartImage as useContentPartImage } from "./messagePart/useMessagePartImage";
