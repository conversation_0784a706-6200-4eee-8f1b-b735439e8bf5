{"version": 3, "sources": ["../../../src/primitives/branchPicker/BranchPickerCount.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { FC } from \"react\";\nimport { useMessage } from \"../../context/react/MessageContext\";\n\nconst useBranchPickerCount = () => {\n  const branchCount = useMessage((s) => s.branchCount);\n  return branchCount;\n};\n\nexport namespace BranchPickerPrimitiveCount {\n  /**\n   * Props for the BranchPickerPrimitive.Count component.\n   * This component takes no props.\n   */\n  export type Props = Record<string, never>;\n}\n\n/**\n * A component that displays the total number of branches for the current message.\n *\n * This component renders the branch count as plain text, useful for showing\n * users how many alternative responses are available.\n *\n * @example\n * ```tsx\n * <div>\n *   Branch <BranchPickerPrimitive.Count /> of {totalBranches}\n * </div>\n * ```\n */\nexport const BranchPickerPrimitiveCount: FC<\n  BranchPickerPrimitiveCount.Props\n> = () => {\n  const branchCount = useBranchPickerCount();\n  return <>{branchCount}</>;\n};\n\nBranchPickerPrimitiveCount.displayName = \"BranchPickerPrimitive.Count\";\n"], "mappings": ";;;AAGA,SAAS,kBAAkB;AAgClB;AA9BT,IAAM,uBAAuB,MAAM;AACjC,QAAM,cAAc,WAAW,CAAC,MAAM,EAAE,WAAW;AACnD,SAAO;AACT;AAuBO,IAAM,6BAET,MAAM;AACR,QAAM,cAAc,qBAAqB;AACzC,SAAO,gCAAG,uBAAY;AACxB;AAEA,2BAA2B,cAAc;", "names": []}