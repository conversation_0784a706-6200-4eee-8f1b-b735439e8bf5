{"version": 3, "sources": ["../../../src/primitives/threadListItem/ThreadListItemRoot.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { type ComponentRef, forwardRef, ComponentPropsWithoutRef } from \"react\";\nimport { useThreadListItem } from \"../../context/react/ThreadListItemContext\";\n\ntype PrimitiveDivProps = ComponentPropsWithoutRef<typeof Primitive.div>;\n\nexport namespace ThreadListItemPrimitiveRoot {\n  export type Element = ComponentRef<typeof Primitive.div>;\n  export type Props = PrimitiveDivProps;\n}\n\nexport const ThreadListItemPrimitiveRoot = forwardRef<\n  ThreadListItemPrimitiveRoot.Element,\n  ThreadListItemPrimitiveRoot.Props\n>((props, ref) => {\n  const isMain = useThreadListItem((t) => t.isMain);\n\n  return (\n    <Primitive.div\n      {...(isMain ? { \"data-active\": \"true\", \"aria-current\": \"true\" } : null)}\n      {...props}\n      ref={ref}\n    />\n  );\n});\n\nThreadListItemPrimitiveRoot.displayName = \"ThreadListItemPrimitive.Root\";\n"], "mappings": ";;;AAEA,SAAS,iBAAiB;AAC1B,SAA4B,kBAA4C;AACxE,SAAS,yBAAyB;AAgB9B;AAPG,IAAM,8BAA8B,WAGzC,CAAC,OAAO,QAAQ;AAChB,QAAM,SAAS,kBAAkB,CAAC,MAAM,EAAE,MAAM;AAEhD,SACE;AAAA,IAAC,UAAU;AAAA,IAAV;AAAA,MACE,GAAI,SAAS,EAAE,eAAe,QAAQ,gBAAgB,OAAO,IAAI;AAAA,MACjE,GAAG;AAAA,MACJ;AAAA;AAAA,EACF;AAEJ,CAAC;AAED,4BAA4B,cAAc;", "names": []}