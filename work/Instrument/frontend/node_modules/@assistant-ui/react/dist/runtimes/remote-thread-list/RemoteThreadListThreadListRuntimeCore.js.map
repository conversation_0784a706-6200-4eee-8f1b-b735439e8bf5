{"version": 3, "sources": ["../../../src/runtimes/remote-thread-list/RemoteThreadListThreadListRuntimeCore.tsx"], "sourcesContent": ["\"use client\";\n\nimport { ThreadListRuntimeCore } from \"../core/ThreadListRuntimeCore\";\nimport { generateId } from \"../../internal\";\nimport {\n  RemoteThreadInitializeResponse,\n  RemoteThreadListOptions,\n} from \"./types\";\nimport { RemoteThreadListHookInstanceManager } from \"./RemoteThreadListHookInstanceManager\";\nimport { BaseSubscribable } from \"./BaseSubscribable\";\nimport { EMPTY_THREAD_CORE } from \"./EMPTY_THREAD_CORE\";\nimport { OptimisticState } from \"./OptimisticState\";\nimport { FC, Fragment, useEffect, useId } from \"react\";\nimport { create } from \"zustand\";\nimport { AssistantMessageStream } from \"assistant-stream\";\nimport { ModelContextProvider } from \"../../model-context\";\nimport { RuntimeAdapterProvider } from \"../adapters/RuntimeAdapterProvider\";\n\ntype RemoteThreadData =\n  | {\n      readonly threadId: string;\n      readonly remoteId?: undefined;\n      readonly externalId?: undefined;\n      readonly status: \"new\";\n      readonly title: undefined;\n    }\n  | {\n      readonly threadId: string;\n      readonly initializeTask: Promise<RemoteThreadInitializeResponse>;\n      readonly remoteId?: undefined;\n      readonly externalId?: undefined;\n      readonly status: \"regular\" | \"archived\";\n      readonly title?: string | undefined;\n    }\n  | {\n      readonly threadId: string;\n      readonly initializeTask: Promise<RemoteThreadInitializeResponse>;\n      readonly remoteId: string;\n      readonly externalId: string | undefined;\n      readonly status: \"regular\" | \"archived\";\n      readonly title?: string | undefined;\n    };\n\ntype THREAD_MAPPING_ID = string & { __brand: \"THREAD_MAPPING_ID\" };\nfunction createThreadMappingId(id: string): THREAD_MAPPING_ID {\n  return id as THREAD_MAPPING_ID;\n}\n\ntype RemoteThreadState = {\n  readonly isLoading: boolean;\n  readonly newThreadId: string | undefined;\n  readonly threadIds: readonly string[];\n  readonly archivedThreadIds: readonly string[];\n  readonly threadIdMap: Readonly<Record<string, THREAD_MAPPING_ID>>;\n  readonly threadData: Readonly<Record<THREAD_MAPPING_ID, RemoteThreadData>>;\n};\n\nconst getThreadData = (\n  state: RemoteThreadState,\n  threadIdOrRemoteId: string,\n) => {\n  const idx = state.threadIdMap[threadIdOrRemoteId];\n  if (idx === undefined) return undefined;\n  return state.threadData[idx];\n};\n\nconst updateStatusReducer = (\n  state: RemoteThreadState,\n  threadIdOrRemoteId: string,\n  newStatus: \"regular\" | \"archived\" | \"deleted\",\n) => {\n  const data = getThreadData(state, threadIdOrRemoteId);\n  if (!data) return state;\n\n  const { threadId, remoteId, status: lastStatus } = data;\n  if (lastStatus === newStatus) return state;\n\n  const newState = { ...state };\n\n  // lastStatus\n  switch (lastStatus) {\n    case \"new\":\n      newState.newThreadId = undefined;\n      break;\n    case \"regular\":\n      newState.threadIds = newState.threadIds.filter((t) => t !== threadId);\n      break;\n    case \"archived\":\n      newState.archivedThreadIds = newState.archivedThreadIds.filter(\n        (t) => t !== threadId,\n      );\n      break;\n\n    default: {\n      const _exhaustiveCheck: never = lastStatus;\n      throw new Error(`Unsupported state: ${_exhaustiveCheck}`);\n    }\n  }\n\n  // newStatus\n  switch (newStatus) {\n    case \"regular\":\n      newState.threadIds = [threadId, ...newState.threadIds];\n      break;\n\n    case \"archived\":\n      newState.archivedThreadIds = [threadId, ...newState.archivedThreadIds];\n      break;\n\n    case \"deleted\":\n      newState.threadData = Object.fromEntries(\n        Object.entries(newState.threadData).filter(([key]) => key !== threadId),\n      );\n      newState.threadIdMap = Object.fromEntries(\n        Object.entries(newState.threadIdMap).filter(\n          ([key]) => key !== threadId && key !== remoteId,\n        ),\n      );\n      break;\n\n    default: {\n      const _exhaustiveCheck: never = newStatus;\n      throw new Error(`Unsupported state: ${_exhaustiveCheck}`);\n    }\n  }\n\n  if (newStatus !== \"deleted\") {\n    newState.threadData = {\n      ...newState.threadData,\n      [threadId]: {\n        ...data,\n        status: newStatus,\n      },\n    };\n  }\n\n  return newState;\n};\n\nexport class RemoteThreadListThreadListRuntimeCore\n  extends BaseSubscribable\n  implements ThreadListRuntimeCore\n{\n  private _options!: RemoteThreadListOptions;\n  private readonly _hookManager: RemoteThreadListHookInstanceManager;\n\n  private _loadThreadsPromise: Promise<void> | undefined;\n\n  private _mainThreadId!: string;\n  private readonly _state = new OptimisticState<RemoteThreadState>({\n    isLoading: false,\n    newThreadId: undefined,\n    threadIds: [],\n    archivedThreadIds: [],\n    threadIdMap: {},\n    threadData: {},\n  });\n\n  public getLoadThreadsPromise() {\n    // TODO this needs to be cached in case this promise is loaded during suspense\n    if (!this._loadThreadsPromise) {\n      this._loadThreadsPromise = this._state\n        .optimisticUpdate({\n          execute: () => this._options.adapter.list(),\n          loading: (state) => {\n            return {\n              ...state,\n              isLoading: true,\n            };\n          },\n          then: (state, l) => {\n            const newThreadIds = [];\n            const newArchivedThreadIds = [];\n            const newThreadIdMap = {} as Record<string, THREAD_MAPPING_ID>;\n            const newThreadData = {} as Record<\n              THREAD_MAPPING_ID,\n              RemoteThreadData\n            >;\n\n            for (const thread of l.threads) {\n              switch (thread.status) {\n                case \"regular\":\n                  newThreadIds.push(thread.remoteId);\n                  break;\n                case \"archived\":\n                  newArchivedThreadIds.push(thread.remoteId);\n                  break;\n                default: {\n                  const _exhaustiveCheck: never = thread.status;\n                  throw new Error(`Unsupported state: ${_exhaustiveCheck}`);\n                }\n              }\n\n              const mappingId = createThreadMappingId(thread.remoteId);\n              newThreadIdMap[thread.remoteId] = mappingId;\n              newThreadData[mappingId] = {\n                threadId: thread.remoteId,\n                remoteId: thread.remoteId,\n                externalId: thread.externalId,\n                status: thread.status,\n                title: thread.title,\n                initializeTask: Promise.resolve({\n                  remoteId: thread.remoteId,\n                  externalId: thread.externalId,\n                }),\n              };\n            }\n\n            return {\n              ...state,\n              threadIds: newThreadIds,\n              archivedThreadIds: newArchivedThreadIds,\n              threadIdMap: {\n                ...state.threadIdMap,\n                ...newThreadIdMap,\n              },\n              threadData: {\n                ...state.threadData,\n                ...newThreadData,\n              },\n            };\n          },\n        })\n        .then(() => {});\n    }\n\n    return this._loadThreadsPromise;\n  }\n\n  constructor(\n    options: RemoteThreadListOptions,\n    private readonly contextProvider: ModelContextProvider,\n  ) {\n    super();\n\n    this._state.subscribe(() => this._notifySubscribers());\n    this._hookManager = new RemoteThreadListHookInstanceManager(\n      options.runtimeHook,\n    );\n    this.useProvider = create(() => ({\n      Provider: options.adapter.unstable_Provider ?? Fragment,\n    }));\n    this.__internal_setOptions(options);\n\n    this.switchToNewThread();\n  }\n\n  private useProvider;\n\n  public __internal_setOptions(options: RemoteThreadListOptions) {\n    if (this._options === options) return;\n\n    this._options = options;\n\n    const Provider = options.adapter.unstable_Provider ?? Fragment;\n    if (Provider !== this.useProvider.getState().Provider) {\n      this.useProvider.setState({ Provider }, true);\n    }\n\n    this._hookManager.setRuntimeHook(options.runtimeHook);\n  }\n\n  public __internal_load() {\n    this.getLoadThreadsPromise(); // begin loading on initial bind\n  }\n\n  public get isLoading() {\n    return this._state.value.isLoading;\n  }\n\n  public get threadIds() {\n    return this._state.value.threadIds;\n  }\n\n  public get archivedThreadIds() {\n    return this._state.value.archivedThreadIds;\n  }\n\n  public get newThreadId() {\n    return this._state.value.newThreadId;\n  }\n\n  public get mainThreadId(): string {\n    return this._mainThreadId;\n  }\n\n  public getMainThreadRuntimeCore() {\n    const result = this._hookManager.getThreadRuntimeCore(this._mainThreadId);\n    if (!result) return EMPTY_THREAD_CORE;\n    return result;\n  }\n\n  public getThreadRuntimeCore(threadIdOrRemoteId: string) {\n    const data = this.getItemById(threadIdOrRemoteId);\n    if (!data) throw new Error(\"Thread not found\");\n\n    const result = this._hookManager.getThreadRuntimeCore(data.threadId);\n    if (!result) throw new Error(\"Thread not found\");\n    return result;\n  }\n\n  public getItemById(threadIdOrRemoteId: string) {\n    return getThreadData(this._state.value, threadIdOrRemoteId);\n  }\n\n  public async switchToThread(threadIdOrRemoteId: string): Promise<void> {\n    const data = this.getItemById(threadIdOrRemoteId);\n    if (!data) throw new Error(\"Thread not found\");\n\n    if (this._mainThreadId === data.threadId) return;\n\n    const task = this._hookManager.startThreadRuntime(data.threadId);\n    if (this.mainThreadId !== undefined) {\n      await task;\n    } else {\n      task.then(() => this._notifySubscribers());\n    }\n\n    if (data.status === \"archived\") await this.unarchive(data.threadId);\n    this._mainThreadId = data.threadId;\n\n    this._notifySubscribers();\n  }\n\n  public async switchToNewThread(): Promise<void> {\n    // an initialization transaction is in progress, wait for it to settle\n    while (\n      this._state.baseValue.newThreadId !== undefined &&\n      this._state.value.newThreadId === undefined\n    ) {\n      await this._state.waitForUpdate();\n    }\n\n    const state = this._state.value;\n    let threadId: string | undefined = this._state.value.newThreadId;\n    if (threadId === undefined) {\n      do {\n        threadId = `__LOCALID_${generateId()}`;\n      } while (state.threadIdMap[threadId]);\n\n      const mappingId = createThreadMappingId(threadId);\n      this._state.update({\n        ...state,\n        newThreadId: threadId,\n        threadIdMap: {\n          ...state.threadIdMap,\n          [threadId]: mappingId,\n        },\n        threadData: {\n          ...state.threadData,\n          [threadId]: {\n            status: \"new\",\n            threadId,\n          },\n        },\n      });\n    }\n\n    return this.switchToThread(threadId);\n  }\n\n  public initialize = async (threadId: string) => {\n    if (this._state.value.newThreadId !== threadId) {\n      const data = this.getItemById(threadId);\n      if (!data) throw new Error(\"Thread not found\");\n      if (data.status === \"new\") throw new Error(\"Unexpected new state\");\n      return data.initializeTask;\n    }\n\n    return this._state.optimisticUpdate({\n      execute: () => {\n        return this._options.adapter.initialize(threadId);\n      },\n      optimistic: (state) => {\n        return updateStatusReducer(state, threadId, \"regular\");\n      },\n      loading: (state, task) => {\n        const mappingId = createThreadMappingId(threadId);\n        return {\n          ...state,\n          threadData: {\n            ...state.threadData,\n            [mappingId]: {\n              ...state.threadData[mappingId],\n              initializeTask: task,\n            },\n          },\n        };\n      },\n      then: (state, { remoteId, externalId }) => {\n        const data = getThreadData(state, threadId);\n        if (!data) return state;\n\n        const mappingId = createThreadMappingId(threadId);\n        return {\n          ...state,\n          threadIdMap: {\n            ...state.threadIdMap,\n            [remoteId]: mappingId,\n          },\n          threadData: {\n            ...state.threadData,\n            [mappingId]: {\n              ...data,\n              initializeTask: Promise.resolve({ remoteId, externalId }),\n              remoteId,\n              externalId,\n            },\n          },\n        };\n      },\n    });\n  };\n\n  public generateTitle = async (threadId: string) => {\n    const data = this.getItemById(threadId);\n    if (!data) throw new Error(\"Thread not found\");\n    if (data.status === \"new\") throw new Error(\"Thread is not yet initialized\");\n\n    const { remoteId } = await data.initializeTask;\n\n    const runtimeCore = this._hookManager.getThreadRuntimeCore(data.threadId);\n    if (!runtimeCore) return; // thread is no longer running\n\n    const messages = runtimeCore.messages;\n    const stream = await this._options.adapter.generateTitle(\n      remoteId,\n      messages,\n    );\n    const messageStream = AssistantMessageStream.fromAssistantStream(stream);\n    for await (const result of messageStream) {\n      const newTitle = result.parts.filter((c) => c.type === \"text\")[0]?.text;\n      const state = this._state.baseValue;\n      this._state.update({\n        ...state,\n        threadData: {\n          ...state.threadData,\n          [data.threadId]: {\n            ...data,\n            title: newTitle,\n          },\n        },\n      });\n    }\n  };\n\n  public rename(threadIdOrRemoteId: string, newTitle: string): Promise<void> {\n    const data = this.getItemById(threadIdOrRemoteId);\n    if (!data) throw new Error(\"Thread not found\");\n    if (data.status === \"new\") throw new Error(\"Thread is not yet initialized\");\n\n    return this._state.optimisticUpdate({\n      execute: async () => {\n        const { remoteId } = await data.initializeTask;\n        return this._options.adapter.rename(remoteId, newTitle);\n      },\n      optimistic: (state) => {\n        const data = getThreadData(state, threadIdOrRemoteId);\n        if (!data) return state;\n\n        return {\n          ...state,\n          threadData: {\n            ...state.threadData,\n            [data.threadId]: {\n              ...data,\n              title: newTitle,\n            },\n          },\n        };\n      },\n    });\n  }\n\n  private async _ensureThreadIsNotMain(threadId: string) {\n    if (threadId === this.newThreadId)\n      throw new Error(\"Cannot ensure new thread is not main\");\n\n    if (threadId === this._mainThreadId) {\n      await this.switchToNewThread();\n    }\n  }\n\n  public async archive(threadIdOrRemoteId: string) {\n    const data = this.getItemById(threadIdOrRemoteId);\n    if (!data) throw new Error(\"Thread not found\");\n    if (data.status !== \"regular\")\n      throw new Error(\"Thread is not yet initialized or already archived\");\n\n    return this._state.optimisticUpdate({\n      execute: async () => {\n        await this._ensureThreadIsNotMain(data.threadId);\n        const { remoteId } = await data.initializeTask;\n        return this._options.adapter.archive(remoteId);\n      },\n      optimistic: (state) => {\n        return updateStatusReducer(state, data.threadId, \"archived\");\n      },\n    });\n  }\n\n  public unarchive(threadIdOrRemoteId: string): Promise<void> {\n    const data = this.getItemById(threadIdOrRemoteId);\n    if (!data) throw new Error(\"Thread not found\");\n    if (data.status !== \"archived\") throw new Error(\"Thread is not archived\");\n\n    return this._state.optimisticUpdate({\n      execute: async () => {\n        try {\n          const { remoteId } = await data.initializeTask;\n          return await this._options.adapter.unarchive(remoteId);\n        } catch (error) {\n          await this._ensureThreadIsNotMain(data.threadId);\n          throw error;\n        }\n      },\n      optimistic: (state) => {\n        return updateStatusReducer(state, data.threadId, \"regular\");\n      },\n    });\n  }\n\n  public async delete(threadIdOrRemoteId: string) {\n    const data = this.getItemById(threadIdOrRemoteId);\n    if (!data) throw new Error(\"Thread not found\");\n    if (data.status !== \"regular\" && data.status !== \"archived\")\n      throw new Error(\"Thread is not yet initialized\");\n\n    return this._state.optimisticUpdate({\n      execute: async () => {\n        await this._ensureThreadIsNotMain(data.threadId);\n        const { remoteId } = await data.initializeTask;\n        return await this._options.adapter.delete(remoteId);\n      },\n      optimistic: (state) => {\n        return updateStatusReducer(state, data.threadId, \"deleted\");\n      },\n    });\n  }\n\n  public async detach(threadIdOrRemoteId: string): Promise<void> {\n    const data = this.getItemById(threadIdOrRemoteId);\n    if (!data) throw new Error(\"Thread not found\");\n    if (data.status !== \"regular\" && data.status !== \"archived\")\n      throw new Error(\"Thread is not yet initialized\");\n\n    await this._ensureThreadIsNotMain(data.threadId);\n    this._hookManager.stopThreadRuntime(data.threadId);\n  }\n\n  private useBoundIds = create<string[]>(() => []);\n\n  public __internal_RenderComponent: FC = () => {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const id = useId();\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(() => {\n      this.useBoundIds.setState((s) => [...s, id], true);\n      return () => {\n        this.useBoundIds.setState((s) => s.filter((i) => i !== id), true);\n      };\n    }, [id]);\n\n    const boundIds = this.useBoundIds();\n    const { Provider } = this.useProvider();\n\n    const adapters = {\n      modelContext: this.contextProvider,\n    };\n\n    return (\n      (boundIds.length === 0 || boundIds[0] === id) && (\n        // only render if the component is the first one mounted\n        <RuntimeAdapterProvider adapters={adapters}>\n          <this._hookManager.__internal_RenderThreadRuntimes\n            provider={Provider}\n          />\n        </RuntimeAdapterProvider>\n      )\n    );\n  };\n}\n"], "mappings": ";;;AAGA,SAAS,kBAAkB;AAK3B,SAAS,2CAA2C;AACpD,SAAS,wBAAwB;AACjC,SAAS,yBAAyB;AAClC,SAAS,uBAAuB;AAChC,SAAa,UAAU,WAAW,aAAa;AAC/C,SAAS,cAAc;AACvB,SAAS,8BAA8B;AAEvC,SAAS,8BAA8B;AA8iB7B;AAlhBV,SAAS,sBAAsB,IAA+B;AAC5D,SAAO;AACT;AAWA,IAAM,gBAAgB,CACpB,OACA,uBACG;AACH,QAAM,MAAM,MAAM,YAAY,kBAAkB;AAChD,MAAI,QAAQ,OAAW,QAAO;AAC9B,SAAO,MAAM,WAAW,GAAG;AAC7B;AAEA,IAAM,sBAAsB,CAC1B,OACA,oBACA,cACG;AACH,QAAM,OAAO,cAAc,OAAO,kBAAkB;AACpD,MAAI,CAAC,KAAM,QAAO;AAElB,QAAM,EAAE,UAAU,UAAU,QAAQ,WAAW,IAAI;AACnD,MAAI,eAAe,UAAW,QAAO;AAErC,QAAM,WAAW,EAAE,GAAG,MAAM;AAG5B,UAAQ,YAAY;AAAA,IAClB,KAAK;AACH,eAAS,cAAc;AACvB;AAAA,IACF,KAAK;AACH,eAAS,YAAY,SAAS,UAAU,OAAO,CAAC,MAAM,MAAM,QAAQ;AACpE;AAAA,IACF,KAAK;AACH,eAAS,oBAAoB,SAAS,kBAAkB;AAAA,QACtD,CAAC,MAAM,MAAM;AAAA,MACf;AACA;AAAA,IAEF,SAAS;AACP,YAAM,mBAA0B;AAChC,YAAM,IAAI,MAAM,sBAAsB,gBAAgB,EAAE;AAAA,IAC1D;AAAA,EACF;AAGA,UAAQ,WAAW;AAAA,IACjB,KAAK;AACH,eAAS,YAAY,CAAC,UAAU,GAAG,SAAS,SAAS;AACrD;AAAA,IAEF,KAAK;AACH,eAAS,oBAAoB,CAAC,UAAU,GAAG,SAAS,iBAAiB;AACrE;AAAA,IAEF,KAAK;AACH,eAAS,aAAa,OAAO;AAAA,QAC3B,OAAO,QAAQ,SAAS,UAAU,EAAE,OAAO,CAAC,CAAC,GAAG,MAAM,QAAQ,QAAQ;AAAA,MACxE;AACA,eAAS,cAAc,OAAO;AAAA,QAC5B,OAAO,QAAQ,SAAS,WAAW,EAAE;AAAA,UACnC,CAAC,CAAC,GAAG,MAAM,QAAQ,YAAY,QAAQ;AAAA,QACzC;AAAA,MACF;AACA;AAAA,IAEF,SAAS;AACP,YAAM,mBAA0B;AAChC,YAAM,IAAI,MAAM,sBAAsB,gBAAgB,EAAE;AAAA,IAC1D;AAAA,EACF;AAEA,MAAI,cAAc,WAAW;AAC3B,aAAS,aAAa;AAAA,MACpB,GAAG,SAAS;AAAA,MACZ,CAAC,QAAQ,GAAG;AAAA,QACV,GAAG;AAAA,QACH,QAAQ;AAAA,MACV;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAEO,IAAM,wCAAN,cACG,iBAEV;AAAA,EAuFE,YACE,SACiB,iBACjB;AACA,UAAM;AAFW;AAIjB,SAAK,OAAO,UAAU,MAAM,KAAK,mBAAmB,CAAC;AACrD,SAAK,eAAe,IAAI;AAAA,MACtB,QAAQ;AAAA,IACV;AACA,SAAK,cAAc,OAAO,OAAO;AAAA,MAC/B,UAAU,QAAQ,QAAQ,qBAAqB;AAAA,IACjD,EAAE;AACF,SAAK,sBAAsB,OAAO;AAElC,SAAK,kBAAkB;AAAA,EACzB;AAAA,EAtGQ;AAAA,EACS;AAAA,EAET;AAAA,EAEA;AAAA,EACS,SAAS,IAAI,gBAAmC;AAAA,IAC/D,WAAW;AAAA,IACX,aAAa;AAAA,IACb,WAAW,CAAC;AAAA,IACZ,mBAAmB,CAAC;AAAA,IACpB,aAAa,CAAC;AAAA,IACd,YAAY,CAAC;AAAA,EACf,CAAC;AAAA,EAEM,wBAAwB;AAE7B,QAAI,CAAC,KAAK,qBAAqB;AAC7B,WAAK,sBAAsB,KAAK,OAC7B,iBAAiB;AAAA,QAChB,SAAS,MAAM,KAAK,SAAS,QAAQ,KAAK;AAAA,QAC1C,SAAS,CAAC,UAAU;AAClB,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,WAAW;AAAA,UACb;AAAA,QACF;AAAA,QACA,MAAM,CAAC,OAAO,MAAM;AAClB,gBAAM,eAAe,CAAC;AACtB,gBAAM,uBAAuB,CAAC;AAC9B,gBAAM,iBAAiB,CAAC;AACxB,gBAAM,gBAAgB,CAAC;AAKvB,qBAAW,UAAU,EAAE,SAAS;AAC9B,oBAAQ,OAAO,QAAQ;AAAA,cACrB,KAAK;AACH,6BAAa,KAAK,OAAO,QAAQ;AACjC;AAAA,cACF,KAAK;AACH,qCAAqB,KAAK,OAAO,QAAQ;AACzC;AAAA,cACF,SAAS;AACP,sBAAM,mBAA0B,OAAO;AACvC,sBAAM,IAAI,MAAM,sBAAsB,gBAAgB,EAAE;AAAA,cAC1D;AAAA,YACF;AAEA,kBAAM,YAAY,sBAAsB,OAAO,QAAQ;AACvD,2BAAe,OAAO,QAAQ,IAAI;AAClC,0BAAc,SAAS,IAAI;AAAA,cACzB,UAAU,OAAO;AAAA,cACjB,UAAU,OAAO;AAAA,cACjB,YAAY,OAAO;AAAA,cACnB,QAAQ,OAAO;AAAA,cACf,OAAO,OAAO;AAAA,cACd,gBAAgB,QAAQ,QAAQ;AAAA,gBAC9B,UAAU,OAAO;AAAA,gBACjB,YAAY,OAAO;AAAA,cACrB,CAAC;AAAA,YACH;AAAA,UACF;AAEA,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,WAAW;AAAA,YACX,mBAAmB;AAAA,YACnB,aAAa;AAAA,cACX,GAAG,MAAM;AAAA,cACT,GAAG;AAAA,YACL;AAAA,YACA,YAAY;AAAA,cACV,GAAG,MAAM;AAAA,cACT,GAAG;AAAA,YACL;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC,EACA,KAAK,MAAM;AAAA,MAAC,CAAC;AAAA,IAClB;AAEA,WAAO,KAAK;AAAA,EACd;AAAA,EAoBQ;AAAA,EAED,sBAAsB,SAAkC;AAC7D,QAAI,KAAK,aAAa,QAAS;AAE/B,SAAK,WAAW;AAEhB,UAAM,WAAW,QAAQ,QAAQ,qBAAqB;AACtD,QAAI,aAAa,KAAK,YAAY,SAAS,EAAE,UAAU;AACrD,WAAK,YAAY,SAAS,EAAE,SAAS,GAAG,IAAI;AAAA,IAC9C;AAEA,SAAK,aAAa,eAAe,QAAQ,WAAW;AAAA,EACtD;AAAA,EAEO,kBAAkB;AACvB,SAAK,sBAAsB;AAAA,EAC7B;AAAA,EAEA,IAAW,YAAY;AACrB,WAAO,KAAK,OAAO,MAAM;AAAA,EAC3B;AAAA,EAEA,IAAW,YAAY;AACrB,WAAO,KAAK,OAAO,MAAM;AAAA,EAC3B;AAAA,EAEA,IAAW,oBAAoB;AAC7B,WAAO,KAAK,OAAO,MAAM;AAAA,EAC3B;AAAA,EAEA,IAAW,cAAc;AACvB,WAAO,KAAK,OAAO,MAAM;AAAA,EAC3B;AAAA,EAEA,IAAW,eAAuB;AAChC,WAAO,KAAK;AAAA,EACd;AAAA,EAEO,2BAA2B;AAChC,UAAM,SAAS,KAAK,aAAa,qBAAqB,KAAK,aAAa;AACxE,QAAI,CAAC,OAAQ,QAAO;AACpB,WAAO;AAAA,EACT;AAAA,EAEO,qBAAqB,oBAA4B;AACtD,UAAM,OAAO,KAAK,YAAY,kBAAkB;AAChD,QAAI,CAAC,KAAM,OAAM,IAAI,MAAM,kBAAkB;AAE7C,UAAM,SAAS,KAAK,aAAa,qBAAqB,KAAK,QAAQ;AACnE,QAAI,CAAC,OAAQ,OAAM,IAAI,MAAM,kBAAkB;AAC/C,WAAO;AAAA,EACT;AAAA,EAEO,YAAY,oBAA4B;AAC7C,WAAO,cAAc,KAAK,OAAO,OAAO,kBAAkB;AAAA,EAC5D;AAAA,EAEA,MAAa,eAAe,oBAA2C;AACrE,UAAM,OAAO,KAAK,YAAY,kBAAkB;AAChD,QAAI,CAAC,KAAM,OAAM,IAAI,MAAM,kBAAkB;AAE7C,QAAI,KAAK,kBAAkB,KAAK,SAAU;AAE1C,UAAM,OAAO,KAAK,aAAa,mBAAmB,KAAK,QAAQ;AAC/D,QAAI,KAAK,iBAAiB,QAAW;AACnC,YAAM;AAAA,IACR,OAAO;AACL,WAAK,KAAK,MAAM,KAAK,mBAAmB,CAAC;AAAA,IAC3C;AAEA,QAAI,KAAK,WAAW,WAAY,OAAM,KAAK,UAAU,KAAK,QAAQ;AAClE,SAAK,gBAAgB,KAAK;AAE1B,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EAEA,MAAa,oBAAmC;AAE9C,WACE,KAAK,OAAO,UAAU,gBAAgB,UACtC,KAAK,OAAO,MAAM,gBAAgB,QAClC;AACA,YAAM,KAAK,OAAO,cAAc;AAAA,IAClC;AAEA,UAAM,QAAQ,KAAK,OAAO;AAC1B,QAAI,WAA+B,KAAK,OAAO,MAAM;AACrD,QAAI,aAAa,QAAW;AAC1B,SAAG;AACD,mBAAW,aAAa,WAAW,CAAC;AAAA,MACtC,SAAS,MAAM,YAAY,QAAQ;AAEnC,YAAM,YAAY,sBAAsB,QAAQ;AAChD,WAAK,OAAO,OAAO;AAAA,QACjB,GAAG;AAAA,QACH,aAAa;AAAA,QACb,aAAa;AAAA,UACX,GAAG,MAAM;AAAA,UACT,CAAC,QAAQ,GAAG;AAAA,QACd;AAAA,QACA,YAAY;AAAA,UACV,GAAG,MAAM;AAAA,UACT,CAAC,QAAQ,GAAG;AAAA,YACV,QAAQ;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAEA,WAAO,KAAK,eAAe,QAAQ;AAAA,EACrC;AAAA,EAEO,aAAa,OAAO,aAAqB;AAC9C,QAAI,KAAK,OAAO,MAAM,gBAAgB,UAAU;AAC9C,YAAM,OAAO,KAAK,YAAY,QAAQ;AACtC,UAAI,CAAC,KAAM,OAAM,IAAI,MAAM,kBAAkB;AAC7C,UAAI,KAAK,WAAW,MAAO,OAAM,IAAI,MAAM,sBAAsB;AACjE,aAAO,KAAK;AAAA,IACd;AAEA,WAAO,KAAK,OAAO,iBAAiB;AAAA,MAClC,SAAS,MAAM;AACb,eAAO,KAAK,SAAS,QAAQ,WAAW,QAAQ;AAAA,MAClD;AAAA,MACA,YAAY,CAAC,UAAU;AACrB,eAAO,oBAAoB,OAAO,UAAU,SAAS;AAAA,MACvD;AAAA,MACA,SAAS,CAAC,OAAO,SAAS;AACxB,cAAM,YAAY,sBAAsB,QAAQ;AAChD,eAAO;AAAA,UACL,GAAG;AAAA,UACH,YAAY;AAAA,YACV,GAAG,MAAM;AAAA,YACT,CAAC,SAAS,GAAG;AAAA,cACX,GAAG,MAAM,WAAW,SAAS;AAAA,cAC7B,gBAAgB;AAAA,YAClB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA,MAAM,CAAC,OAAO,EAAE,UAAU,WAAW,MAAM;AACzC,cAAM,OAAO,cAAc,OAAO,QAAQ;AAC1C,YAAI,CAAC,KAAM,QAAO;AAElB,cAAM,YAAY,sBAAsB,QAAQ;AAChD,eAAO;AAAA,UACL,GAAG;AAAA,UACH,aAAa;AAAA,YACX,GAAG,MAAM;AAAA,YACT,CAAC,QAAQ,GAAG;AAAA,UACd;AAAA,UACA,YAAY;AAAA,YACV,GAAG,MAAM;AAAA,YACT,CAAC,SAAS,GAAG;AAAA,cACX,GAAG;AAAA,cACH,gBAAgB,QAAQ,QAAQ,EAAE,UAAU,WAAW,CAAC;AAAA,cACxD;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEO,gBAAgB,OAAO,aAAqB;AACjD,UAAM,OAAO,KAAK,YAAY,QAAQ;AACtC,QAAI,CAAC,KAAM,OAAM,IAAI,MAAM,kBAAkB;AAC7C,QAAI,KAAK,WAAW,MAAO,OAAM,IAAI,MAAM,+BAA+B;AAE1E,UAAM,EAAE,SAAS,IAAI,MAAM,KAAK;AAEhC,UAAM,cAAc,KAAK,aAAa,qBAAqB,KAAK,QAAQ;AACxE,QAAI,CAAC,YAAa;AAElB,UAAM,WAAW,YAAY;AAC7B,UAAM,SAAS,MAAM,KAAK,SAAS,QAAQ;AAAA,MACzC;AAAA,MACA;AAAA,IACF;AACA,UAAM,gBAAgB,uBAAuB,oBAAoB,MAAM;AACvE,qBAAiB,UAAU,eAAe;AACxC,YAAM,WAAW,OAAO,MAAM,OAAO,CAAC,MAAM,EAAE,SAAS,MAAM,EAAE,CAAC,GAAG;AACnE,YAAM,QAAQ,KAAK,OAAO;AAC1B,WAAK,OAAO,OAAO;AAAA,QACjB,GAAG;AAAA,QACH,YAAY;AAAA,UACV,GAAG,MAAM;AAAA,UACT,CAAC,KAAK,QAAQ,GAAG;AAAA,YACf,GAAG;AAAA,YACH,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEO,OAAO,oBAA4B,UAAiC;AACzE,UAAM,OAAO,KAAK,YAAY,kBAAkB;AAChD,QAAI,CAAC,KAAM,OAAM,IAAI,MAAM,kBAAkB;AAC7C,QAAI,KAAK,WAAW,MAAO,OAAM,IAAI,MAAM,+BAA+B;AAE1E,WAAO,KAAK,OAAO,iBAAiB;AAAA,MAClC,SAAS,YAAY;AACnB,cAAM,EAAE,SAAS,IAAI,MAAM,KAAK;AAChC,eAAO,KAAK,SAAS,QAAQ,OAAO,UAAU,QAAQ;AAAA,MACxD;AAAA,MACA,YAAY,CAAC,UAAU;AACrB,cAAMA,QAAO,cAAc,OAAO,kBAAkB;AACpD,YAAI,CAACA,MAAM,QAAO;AAElB,eAAO;AAAA,UACL,GAAG;AAAA,UACH,YAAY;AAAA,YACV,GAAG,MAAM;AAAA,YACT,CAACA,MAAK,QAAQ,GAAG;AAAA,cACf,GAAGA;AAAA,cACH,OAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAc,uBAAuB,UAAkB;AACrD,QAAI,aAAa,KAAK;AACpB,YAAM,IAAI,MAAM,sCAAsC;AAExD,QAAI,aAAa,KAAK,eAAe;AACnC,YAAM,KAAK,kBAAkB;AAAA,IAC/B;AAAA,EACF;AAAA,EAEA,MAAa,QAAQ,oBAA4B;AAC/C,UAAM,OAAO,KAAK,YAAY,kBAAkB;AAChD,QAAI,CAAC,KAAM,OAAM,IAAI,MAAM,kBAAkB;AAC7C,QAAI,KAAK,WAAW;AAClB,YAAM,IAAI,MAAM,mDAAmD;AAErE,WAAO,KAAK,OAAO,iBAAiB;AAAA,MAClC,SAAS,YAAY;AACnB,cAAM,KAAK,uBAAuB,KAAK,QAAQ;AAC/C,cAAM,EAAE,SAAS,IAAI,MAAM,KAAK;AAChC,eAAO,KAAK,SAAS,QAAQ,QAAQ,QAAQ;AAAA,MAC/C;AAAA,MACA,YAAY,CAAC,UAAU;AACrB,eAAO,oBAAoB,OAAO,KAAK,UAAU,UAAU;AAAA,MAC7D;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEO,UAAU,oBAA2C;AAC1D,UAAM,OAAO,KAAK,YAAY,kBAAkB;AAChD,QAAI,CAAC,KAAM,OAAM,IAAI,MAAM,kBAAkB;AAC7C,QAAI,KAAK,WAAW,WAAY,OAAM,IAAI,MAAM,wBAAwB;AAExE,WAAO,KAAK,OAAO,iBAAiB;AAAA,MAClC,SAAS,YAAY;AACnB,YAAI;AACF,gBAAM,EAAE,SAAS,IAAI,MAAM,KAAK;AAChC,iBAAO,MAAM,KAAK,SAAS,QAAQ,UAAU,QAAQ;AAAA,QACvD,SAAS,OAAO;AACd,gBAAM,KAAK,uBAAuB,KAAK,QAAQ;AAC/C,gBAAM;AAAA,QACR;AAAA,MACF;AAAA,MACA,YAAY,CAAC,UAAU;AACrB,eAAO,oBAAoB,OAAO,KAAK,UAAU,SAAS;AAAA,MAC5D;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,OAAO,oBAA4B;AAC9C,UAAM,OAAO,KAAK,YAAY,kBAAkB;AAChD,QAAI,CAAC,KAAM,OAAM,IAAI,MAAM,kBAAkB;AAC7C,QAAI,KAAK,WAAW,aAAa,KAAK,WAAW;AAC/C,YAAM,IAAI,MAAM,+BAA+B;AAEjD,WAAO,KAAK,OAAO,iBAAiB;AAAA,MAClC,SAAS,YAAY;AACnB,cAAM,KAAK,uBAAuB,KAAK,QAAQ;AAC/C,cAAM,EAAE,SAAS,IAAI,MAAM,KAAK;AAChC,eAAO,MAAM,KAAK,SAAS,QAAQ,OAAO,QAAQ;AAAA,MACpD;AAAA,MACA,YAAY,CAAC,UAAU;AACrB,eAAO,oBAAoB,OAAO,KAAK,UAAU,SAAS;AAAA,MAC5D;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,OAAO,oBAA2C;AAC7D,UAAM,OAAO,KAAK,YAAY,kBAAkB;AAChD,QAAI,CAAC,KAAM,OAAM,IAAI,MAAM,kBAAkB;AAC7C,QAAI,KAAK,WAAW,aAAa,KAAK,WAAW;AAC/C,YAAM,IAAI,MAAM,+BAA+B;AAEjD,UAAM,KAAK,uBAAuB,KAAK,QAAQ;AAC/C,SAAK,aAAa,kBAAkB,KAAK,QAAQ;AAAA,EACnD;AAAA,EAEQ,cAAc,OAAiB,MAAM,CAAC,CAAC;AAAA,EAExC,6BAAiC,MAAM;AAE5C,UAAM,KAAK,MAAM;AAEjB,cAAU,MAAM;AACd,WAAK,YAAY,SAAS,CAAC,MAAM,CAAC,GAAG,GAAG,EAAE,GAAG,IAAI;AACjD,aAAO,MAAM;AACX,aAAK,YAAY,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,MAAM,EAAE,GAAG,IAAI;AAAA,MAClE;AAAA,IACF,GAAG,CAAC,EAAE,CAAC;AAEP,UAAM,WAAW,KAAK,YAAY;AAClC,UAAM,EAAE,SAAS,IAAI,KAAK,YAAY;AAEtC,UAAM,WAAW;AAAA,MACf,cAAc,KAAK;AAAA,IACrB;AAEA,YACG,SAAS,WAAW,KAAK,SAAS,CAAC,MAAM;AAAA,IAExC,oBAAC,0BAAuB,UACtB;AAAA,MAAC,KAAK,aAAa;AAAA,MAAlB;AAAA,QACC,UAAU;AAAA;AAAA,IACZ,GACF;AAAA,EAGN;AACF;", "names": ["data"]}