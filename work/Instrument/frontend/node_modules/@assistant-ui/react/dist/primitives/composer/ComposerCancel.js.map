{"version": 3, "sources": ["../../../src/primitives/composer/ComposerCancel.tsx"], "sourcesContent": ["\"use client\";\n\nimport {\n  ActionButtonElement,\n  ActionButtonProps,\n  createActionButton,\n} from \"../../utils/createActionButton\";\nimport { useCallback } from \"react\";\nimport { useComposer, useComposerRuntime } from \"../../context\";\n\nconst useComposerCancel = () => {\n  const composerRuntime = useComposerRuntime();\n  const disabled = useComposer((c) => !c.canCancel);\n\n  const callback = useCallback(() => {\n    composerRuntime.cancel();\n  }, [composerRuntime]);\n\n  if (disabled) return null;\n  return callback;\n};\n\nexport namespace ComposerPrimitiveCancel {\n  export type Element = ActionButtonElement;\n  /**\n   * Props for the ComposerPrimitive.Cancel component.\n   * Inherits all button element props and action button functionality.\n   */\n  export type Props = ActionButtonProps<typeof useComposerCancel>;\n}\n\n/**\n * A button component that cancels the current message composition.\n *\n * This component automatically handles the cancel functionality and is disabled\n * when canceling is not available.\n *\n * @example\n * ```tsx\n * <ComposerPrimitive.Cancel>\n *   Cancel\n * </ComposerPrimitive.Cancel>\n * ```\n */\nexport const ComposerPrimitiveCancel = createActionButton(\n  \"ComposerPrimitive.Cancel\",\n  useComposerCancel,\n);\n"], "mappings": ";;;AAEA;AAAA,EAGE;AAAA,OACK;AACP,SAAS,mBAAmB;AAC5B,SAAS,aAAa,0BAA0B;AAEhD,IAAM,oBAAoB,MAAM;AAC9B,QAAM,kBAAkB,mBAAmB;AAC3C,QAAM,WAAW,YAAY,CAAC,MAAM,CAAC,EAAE,SAAS;AAEhD,QAAM,WAAW,YAAY,MAAM;AACjC,oBAAgB,OAAO;AAAA,EACzB,GAAG,CAAC,eAAe,CAAC;AAEpB,MAAI,SAAU,QAAO;AACrB,SAAO;AACT;AAwBO,IAAM,0BAA0B;AAAA,EACrC;AAAA,EACA;AACF;", "names": []}