{"version": 3, "sources": ["../../../src/runtimes/composer/DefaultThreadComposerRuntimeCore.tsx"], "sourcesContent": ["import { AppendMessage, PendingAttachment } from \"../../types\";\nimport { AttachmentAdapter } from \"../adapters/attachment\";\nimport { ThreadComposerRuntimeCore } from \"../core/ComposerRuntimeCore\";\nimport { ThreadRuntimeCore } from \"../core/ThreadRuntimeCore\";\nimport { BaseComposerRuntimeCore } from \"./BaseComposerRuntimeCore\";\n\nexport class DefaultThreadComposerRuntimeCore\n  extends BaseComposerRuntimeCore\n  implements ThreadComposerRuntimeCore\n{\n  private _canCancel = false;\n  public get canCancel() {\n    return this._canCancel;\n  }\n\n  public override get attachments(): readonly PendingAttachment[] {\n    return super.attachments as readonly PendingAttachment[];\n  }\n\n  protected getAttachmentAdapter() {\n    return this.runtime.adapters?.attachments;\n  }\n\n  constructor(\n    private runtime: Omit<ThreadRuntimeCore, \"composer\"> & {\n      adapters?: { attachments?: AttachmentAdapter | undefined } | undefined;\n    },\n  ) {\n    super();\n    this.connect();\n  }\n\n  public connect() {\n    return this.runtime.subscribe(() => {\n      if (this.canCancel !== this.runtime.capabilities.cancel) {\n        this._canCancel = this.runtime.capabilities.cancel;\n        this._notifySubscribers();\n      }\n    });\n  }\n\n  public async handleSend(\n    message: Omit<AppendMessage, \"parentId\" | \"sourceId\">,\n  ) {\n    this.runtime.append({\n      ...(message as AppendMessage),\n      parentId: this.runtime.messages.at(-1)?.id ?? null,\n      sourceId: null,\n    });\n  }\n\n  public async handleCancel() {\n    this.runtime.cancelRun();\n  }\n}\n"], "mappings": ";AAIA,SAAS,+BAA+B;AAEjC,IAAM,mCAAN,cACG,wBAEV;AAAA,EAcE,YACU,SAGR;AACA,UAAM;AAJE;AAKR,SAAK,QAAQ;AAAA,EACf;AAAA,EApBQ,aAAa;AAAA,EACrB,IAAW,YAAY;AACrB,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAoB,cAA4C;AAC9D,WAAO,MAAM;AAAA,EACf;AAAA,EAEU,uBAAuB;AAC/B,WAAO,KAAK,QAAQ,UAAU;AAAA,EAChC;AAAA,EAWO,UAAU;AACf,WAAO,KAAK,QAAQ,UAAU,MAAM;AAClC,UAAI,KAAK,cAAc,KAAK,QAAQ,aAAa,QAAQ;AACvD,aAAK,aAAa,KAAK,QAAQ,aAAa;AAC5C,aAAK,mBAAmB;AAAA,MAC1B;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,WACX,SACA;AACA,SAAK,QAAQ,OAAO;AAAA,MAClB,GAAI;AAAA,MACJ,UAAU,KAAK,QAAQ,SAAS,GAAG,EAAE,GAAG,MAAM;AAAA,MAC9C,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,eAAe;AAC1B,SAAK,QAAQ,UAAU;AAAA,EACzB;AACF;", "names": []}