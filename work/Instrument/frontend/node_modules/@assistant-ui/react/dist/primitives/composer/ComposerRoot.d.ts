import { Primitive } from "@radix-ui/react-primitive";
import { type ComponentRef, ComponentPropsWithoutRef } from "react";
export declare namespace ComposerPrimitiveRoot {
    type Element = ComponentRef<typeof Primitive.form>;
    /**
     * Props for the ComposerPrimitive.Root component.
     * Accepts all standard form element props.
     */
    type Props = ComponentPropsWithoutRef<typeof Primitive.form>;
}
/**
 * The root form container for message composition.
 *
 * This component provides a form wrapper that handles message submission when the form
 * is submitted (e.g., via Enter key or submit button). It automatically prevents the
 * default form submission and triggers the composer's send functionality.
 *
 * @example
 * ```tsx
 * <ComposerPrimitive.Root>
 *   <ComposerPrimitive.Input placeholder="Type your message..." />
 *   <ComposerPrimitive.Send>Send</ComposerPrimitive.Send>
 * </ComposerPrimitive.Root>
 * ```
 */
export declare const ComposerPrimitiveRoot: import("react").ForwardRefExoticComponent<Omit<import("react").ClassAttributes<HTMLFormElement> & import("react").FormHTMLAttributes<HTMLFormElement> & {
    asChild?: boolean;
}, "ref"> & import("react").RefAttributes<HTMLFormElement>>;
//# sourceMappingURL=ComposerRoot.d.ts.map