import type { FC, PropsWithChildren } from "react";
import type { RequireAtLeastOne } from "../../utils/RequireAtLeastOne";
type ThreadIfFilters = {
    empty: boolean | undefined;
    running: boolean | undefined;
    disabled: boolean | undefined;
};
type UseThreadIfProps = RequireAtLeastOne<ThreadIfFilters>;
export declare namespace ThreadPrimitiveIf {
    type Props = PropsWithChildren<UseThreadIfProps>;
}
export declare const ThreadPrimitiveIf: FC<ThreadPrimitiveIf.Props>;
export {};
//# sourceMappingURL=ThreadIf.d.ts.map