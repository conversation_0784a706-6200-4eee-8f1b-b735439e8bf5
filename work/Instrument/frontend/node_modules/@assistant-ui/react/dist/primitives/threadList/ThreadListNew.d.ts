import { ActionButtonElement, ActionButtonProps } from "../../utils/createActionButton";
declare const useThreadListNew: () => () => void;
export declare namespace ThreadListPrimitiveNew {
    type Element = ActionButtonElement;
    type Props = ActionButtonProps<typeof useThreadListNew>;
}
export declare const ThreadListPrimitiveNew: import("react").ForwardRefExoticComponent<Omit<import("react").ClassAttributes<HTMLButtonElement> & import("react").ButtonHTMLAttributes<HTMLButtonElement> & {
    asChild?: boolean;
}, "ref"> & import("react").RefAttributes<HTMLButtonElement>>;
export {};
//# sourceMappingURL=ThreadListNew.d.ts.map