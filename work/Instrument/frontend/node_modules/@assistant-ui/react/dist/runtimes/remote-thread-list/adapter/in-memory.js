// src/runtimes/remote-thread-list/adapter/in-memory.tsx
var InMemoryThreadListAdapter = class {
  list() {
    return Promise.resolve({
      threads: []
    });
  }
  rename() {
    return Promise.resolve();
  }
  archive() {
    return Promise.resolve();
  }
  unarchive() {
    return Promise.resolve();
  }
  delete() {
    return Promise.resolve();
  }
  initialize(threadId) {
    return Promise.resolve({ remoteId: threadId, externalId: void 0 });
  }
  generateTitle() {
    return Promise.resolve(new ReadableStream());
  }
};
export {
  InMemoryThreadListAdapter
};
//# sourceMappingURL=in-memory.js.map