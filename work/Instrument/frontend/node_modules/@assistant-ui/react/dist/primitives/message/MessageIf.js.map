{"version": 3, "sources": ["../../../src/primitives/message/MessageIf.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { FC, PropsWithChildren } from \"react\";\nimport {\n  useMessageRuntime,\n  useMessageUtilsStore,\n} from \"../../context/react/MessageContext\";\nimport type { RequireAtLeastOne } from \"../../utils/RequireAtLeastOne\";\nimport { useCombinedStore } from \"../../utils/combined/useCombinedStore\";\n\ntype MessageIfFilters = {\n  user: boolean | undefined;\n  assistant: boolean | undefined;\n  system: boolean | undefined;\n  hasBranches: boolean | undefined;\n  copied: boolean | undefined;\n  lastOrHover: boolean | undefined;\n  last: boolean | undefined;\n  speaking: boolean | undefined;\n  hasAttachments: boolean | undefined;\n  hasContent: boolean | undefined;\n  submittedFeedback: \"positive\" | \"negative\" | null | undefined;\n};\ntype UseMessageIfProps = RequireAtLeastOne<MessageIfFilters>;\n\nconst useMessageIf = (props: UseMessageIfProps) => {\n  const messageRuntime = useMessageRuntime();\n  const messageUtilsStore = useMessageUtilsStore();\n\n  return useCombinedStore(\n    [messageRuntime, messageUtilsStore],\n    (\n      {\n        role,\n        attachments,\n        content,\n        branchCount,\n        isLast,\n        speech,\n        submittedFeedback,\n      },\n      { isCopied, isHovering },\n    ) => {\n      if (props.hasBranches === true && branchCount < 2) return false;\n\n      if (props.user && role !== \"user\") return false;\n      if (props.assistant && role !== \"assistant\") return false;\n      if (props.system && role !== \"system\") return false;\n\n      if (props.lastOrHover === true && !isHovering && !isLast) return false;\n      if (props.last !== undefined && props.last !== isLast) return false;\n\n      if (props.copied === true && !isCopied) return false;\n      if (props.copied === false && isCopied) return false;\n\n      if (props.speaking === true && speech == null) return false;\n      if (props.speaking === false && speech != null) return false;\n\n      if (\n        props.hasAttachments === true &&\n        (role !== \"user\" || !attachments.length)\n      )\n        return false;\n      if (\n        props.hasAttachments === false &&\n        role === \"user\" &&\n        !!attachments.length\n      )\n        return false;\n\n      if (props.hasContent === true && content.length === 0) return false;\n      if (props.hasContent === false && content.length > 0) return false;\n\n      if (\n        props.submittedFeedback !== undefined &&\n        (submittedFeedback?.type ?? null) !== props.submittedFeedback\n      )\n        return false;\n\n      return true;\n    },\n  );\n};\n\nexport namespace MessagePrimitiveIf {\n  export type Props = PropsWithChildren<UseMessageIfProps>;\n}\n\nexport const MessagePrimitiveIf: FC<MessagePrimitiveIf.Props> = ({\n  children,\n  ...query\n}) => {\n  const result = useMessageIf(query);\n  return result ? children : null;\n};\n\nMessagePrimitiveIf.displayName = \"MessagePrimitive.If\";\n"], "mappings": ";;;AAGA;AAAA,EACE;AAAA,EACA;AAAA,OACK;AAEP,SAAS,wBAAwB;AAiBjC,IAAM,eAAe,CAAC,UAA6B;AACjD,QAAM,iBAAiB,kBAAkB;AACzC,QAAM,oBAAoB,qBAAqB;AAE/C,SAAO;AAAA,IACL,CAAC,gBAAgB,iBAAiB;AAAA,IAClC,CACE;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,GACA,EAAE,UAAU,WAAW,MACpB;AACH,UAAI,MAAM,gBAAgB,QAAQ,cAAc,EAAG,QAAO;AAE1D,UAAI,MAAM,QAAQ,SAAS,OAAQ,QAAO;AAC1C,UAAI,MAAM,aAAa,SAAS,YAAa,QAAO;AACpD,UAAI,MAAM,UAAU,SAAS,SAAU,QAAO;AAE9C,UAAI,MAAM,gBAAgB,QAAQ,CAAC,cAAc,CAAC,OAAQ,QAAO;AACjE,UAAI,MAAM,SAAS,UAAa,MAAM,SAAS,OAAQ,QAAO;AAE9D,UAAI,MAAM,WAAW,QAAQ,CAAC,SAAU,QAAO;AAC/C,UAAI,MAAM,WAAW,SAAS,SAAU,QAAO;AAE/C,UAAI,MAAM,aAAa,QAAQ,UAAU,KAAM,QAAO;AACtD,UAAI,MAAM,aAAa,SAAS,UAAU,KAAM,QAAO;AAEvD,UACE,MAAM,mBAAmB,SACxB,SAAS,UAAU,CAAC,YAAY;AAEjC,eAAO;AACT,UACE,MAAM,mBAAmB,SACzB,SAAS,UACT,CAAC,CAAC,YAAY;AAEd,eAAO;AAET,UAAI,MAAM,eAAe,QAAQ,QAAQ,WAAW,EAAG,QAAO;AAC9D,UAAI,MAAM,eAAe,SAAS,QAAQ,SAAS,EAAG,QAAO;AAE7D,UACE,MAAM,sBAAsB,WAC3B,mBAAmB,QAAQ,UAAU,MAAM;AAE5C,eAAO;AAET,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAMO,IAAM,qBAAmD,CAAC;AAAA,EAC/D;AAAA,EACA,GAAG;AACL,MAAM;AACJ,QAAM,SAAS,aAAa,KAAK;AACjC,SAAO,SAAS,WAAW;AAC7B;AAEA,mBAAmB,cAAc;", "names": []}