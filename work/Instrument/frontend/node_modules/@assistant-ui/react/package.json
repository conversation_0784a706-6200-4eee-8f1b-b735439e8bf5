{"name": "@assistant-ui/react", "description": "TypeScript/React library for AI Chat", "keywords": ["radix-ui", "nextjs", "vercel", "ai-sdk", "react", "components", "ui", "frontend", "tailwind", "shadcn", "assistant", "openai", "ai", "chat", "chatbot", "copilot", "ai-chat", "ai-chatbot", "ai-assistant", "ai-copilot", "chatgpt", "gpt4", "gpt-4", "conversational-ui", "conversational-ai"], "version": "0.10.43", "license": "MIT", "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "source": "./src/index.ts", "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist", "src", "internal", "README.md"], "sideEffects": false, "dependencies": {"@radix-ui/primitive": "^1.1.3", "@radix-ui/react-compose-refs": "^1.1.2", "@radix-ui/react-context": "^1.1.2", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-primitive": "^2.1.3", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-use-callback-ref": "^1.1.1", "@radix-ui/react-use-escape-keydown": "^1.1.1", "@standard-schema/spec": "^1.0.0", "assistant-stream": "^0.2.23", "json-schema": "^0.4.0", "nanoid": "5.1.5", "react-textarea-autosize": "^8.5.9", "zod": "^4.0.17", "zustand": "^5.0.7", "assistant-cloud": "0.1.1"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^18 || ^19 || ^19.0.0-rc", "react-dom": "^18 || ^19 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}, "devDependencies": {"@stryker-mutator/core": "^9.0.1", "@stryker-mutator/vitest-runner": "^9.0.1", "@types/json-schema": "^7.0.15", "@types/node": "^24.3.0", "eslint": "^9", "eslint-config-next": "15.4.6", "tsx": "^4.20.4", "vitest": "^3.2.4", "@assistant-ui/x-buildutils": "0.0.1"}, "publishConfig": {"access": "public", "provenance": true}, "homepage": "https://www.assistant-ui.com/", "repository": {"type": "git", "url": "https://github.com/assistant-ui/assistant-ui/tree/main/packages/react"}, "bugs": {"url": "https://github.com/assistant-ui/assistant-ui/issues"}, "scripts": {"build": "tsx scripts/build.mts", "test": "vitest run", "test:watch": "vitest", "test:mutation": "stryker run", "lint": "eslint ."}}