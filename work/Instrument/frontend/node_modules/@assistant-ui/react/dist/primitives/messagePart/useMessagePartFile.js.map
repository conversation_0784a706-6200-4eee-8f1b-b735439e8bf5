{"version": 3, "sources": ["../../../src/primitives/messagePart/useMessagePartFile.tsx"], "sourcesContent": ["\"use client\";\n\nimport { MessagePartState } from \"../../api/MessagePartRuntime\";\nimport { useMessagePart } from \"../../context/react/MessagePartContext\";\nimport { FileMessagePart } from \"../../types\";\n\nexport const useMessagePartFile = () => {\n  const file = useMessagePart((c) => {\n    if (c.type !== \"file\")\n      throw new Error(\n        \"MessagePartFile can only be used inside file message parts.\",\n      );\n\n    return c as MessagePartState & FileMessagePart;\n  });\n\n  return file;\n};\n"], "mappings": ";;;AAGA,SAAS,sBAAsB;AAGxB,IAAM,qBAAqB,MAAM;AACtC,QAAM,OAAO,eAAe,CAAC,MAAM;AACjC,QAAI,EAAE,SAAS;AACb,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAEF,WAAO;AAAA,EACT,CAAC;AAED,SAAO;AACT;", "names": []}