{"version": 3, "sources": ["../../../src/runtimes/remote-thread-list/BaseSubscribable.tsx"], "sourcesContent": ["import { Unsubscribe } from \"../../types\";\n\nexport class BaseSubscribable {\n  private _subscribers = new Set<() => void>();\n\n  public subscribe(callback: () => void): Unsubscribe {\n    this._subscribers.add(callback);\n    return () => this._subscribers.delete(callback);\n  }\n\n  public waitForUpdate() {\n    return new Promise<void>((resolve) => {\n      const unsubscribe = this.subscribe(() => {\n        unsubscribe();\n        resolve();\n      });\n    });\n  }\n\n  protected _notifySubscribers() {\n    const errors = [];\n    for (const callback of this._subscribers) {\n      try {\n        callback();\n      } catch (error) {\n        errors.push(error);\n      }\n    }\n\n    if (errors.length > 0) {\n      if (errors.length === 1) {\n        throw errors[0];\n      } else {\n        throw new AggregateError(errors);\n      }\n    }\n  }\n}\n"], "mappings": ";AAEO,IAAM,mBAAN,MAAuB;AAAA,EACpB,eAAe,oBAAI,IAAgB;AAAA,EAEpC,UAAU,UAAmC;AAClD,SAAK,aAAa,IAAI,QAAQ;AAC9B,WAAO,MAAM,KAAK,aAAa,OAAO,QAAQ;AAAA,EAChD;AAAA,EAEO,gBAAgB;AACrB,WAAO,IAAI,QAAc,CAAC,YAAY;AACpC,YAAM,cAAc,KAAK,UAAU,MAAM;AACvC,oBAAY;AACZ,gBAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EAEU,qBAAqB;AAC7B,UAAM,SAAS,CAAC;AAChB,eAAW,YAAY,KAAK,cAAc;AACxC,UAAI;AACF,iBAAS;AAAA,MACX,SAAS,OAAO;AACd,eAAO,KAAK,KAAK;AAAA,MACnB;AAAA,IACF;AAEA,QAAI,OAAO,SAAS,GAAG;AACrB,UAAI,OAAO,WAAW,GAAG;AACvB,cAAM,OAAO,CAAC;AAAA,MAChB,OAAO;AACL,cAAM,IAAI,eAAe,MAAM;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AACF;", "names": []}