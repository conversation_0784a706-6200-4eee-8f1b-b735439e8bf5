import { ActionButtonElement, ActionButtonProps } from "../../utils/createActionButton";
declare const useThreadListItemUnarchive: () => () => void;
export declare namespace ThreadListItemPrimitiveUnarchive {
    type Element = ActionButtonElement;
    type Props = ActionButtonProps<typeof useThreadListItemUnarchive>;
}
export declare const ThreadListItemPrimitiveUnarchive: import("react").ForwardRefExoticComponent<Omit<import("react").ClassAttributes<HTMLButtonElement> & import("react").ButtonHTMLAttributes<HTMLButtonElement> & {
    asChild?: boolean;
}, "ref"> & import("react").RefAttributes<HTMLButtonElement>>;
export {};
//# sourceMappingURL=ThreadListItemUnarchive.d.ts.map