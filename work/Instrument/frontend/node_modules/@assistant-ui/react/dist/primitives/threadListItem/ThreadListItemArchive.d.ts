import { ActionButtonElement, ActionButtonProps } from "../../utils/createActionButton";
declare const useThreadListItemArchive: () => () => void;
export declare namespace ThreadListItemPrimitiveArchive {
    type Element = ActionButtonElement;
    type Props = ActionButtonProps<typeof useThreadListItemArchive>;
}
export declare const ThreadListItemPrimitiveArchive: import("react").ForwardRefExoticComponent<Omit<import("react").ClassAttributes<HTMLButtonElement> & import("react").ButtonHTMLAttributes<HTMLButtonElement> & {
    asChild?: boolean;
}, "ref"> & import("react").RefAttributes<HTMLButtonElement>>;
export {};
//# sourceMappingURL=ThreadListItemArchive.d.ts.map