{"version": 3, "sources": ["../../../src/primitives/messagePart/useMessagePartSource.tsx"], "sourcesContent": ["\"use client\";\n\nimport { MessagePartState } from \"../../api/MessagePartRuntime\";\nimport { useMessagePart } from \"../../context/react/MessagePartContext\";\nimport { SourceMessagePart } from \"../../types\";\n\nexport const useMessagePartSource = () => {\n  const source = useMessagePart((c) => {\n    if (c.type !== \"source\")\n      throw new Error(\n        \"MessagePartSource can only be used inside source message parts.\",\n      );\n\n    return c as MessagePartState & SourceMessagePart;\n  });\n\n  return source;\n};\n"], "mappings": ";;;AAGA,SAAS,sBAAsB;AAGxB,IAAM,uBAAuB,MAAM;AACxC,QAAM,SAAS,eAAe,CAAC,MAAM;AACnC,QAAI,EAAE,SAAS;AACb,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAEF,WAAO;AAAA,EACT,CAAC;AAED,SAAO;AACT;", "names": []}