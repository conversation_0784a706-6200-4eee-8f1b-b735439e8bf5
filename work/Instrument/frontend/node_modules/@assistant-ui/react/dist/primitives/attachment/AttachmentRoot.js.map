{"version": 3, "sources": ["../../../src/primitives/attachment/AttachmentRoot.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { ComponentPropsWithoutRef, ComponentRef, forwardRef } from \"react\";\n\ntype PrimitiveDivProps = ComponentPropsWithoutRef<typeof Primitive.div>;\n\nexport namespace AttachmentPrimitiveRoot {\n  export type Element = ComponentRef<typeof Primitive.div>;\n  /**\n   * Props for the AttachmentPrimitive.Root component.\n   * Accepts all standard div element props.\n   */\n  export type Props = PrimitiveDivProps;\n}\n\n/**\n * The root container component for an attachment.\n *\n * This component provides the foundational wrapper for attachment-related components\n * and content. It serves as the context provider for attachment state and actions.\n *\n * @example\n * ```tsx\n * <AttachmentPrimitive.Root>\n *   <AttachmentPrimitive.Name />\n *   <AttachmentPrimitive.Remove />\n * </AttachmentPrimitive.Root>\n * ```\n */\nexport const AttachmentPrimitiveRoot = forwardRef<\n  AttachmentPrimitiveRoot.Element,\n  AttachmentPrimitiveRoot.Props\n>((props, ref) => {\n  return <Primitive.div {...props} ref={ref} />;\n});\n\nAttachmentPrimitiveRoot.displayName = \"AttachmentPrimitive.Root\";\n"], "mappings": ";;;AAEA,SAAS,iBAAiB;AAC1B,SAAiD,kBAAkB;AA+B1D;AAJF,IAAM,0BAA0B,WAGrC,CAAC,OAAO,QAAQ;AAChB,SAAO,oBAAC,UAAU,KAAV,EAAe,GAAG,OAAO,KAAU;AAC7C,CAAC;AAED,wBAAwB,cAAc;", "names": []}