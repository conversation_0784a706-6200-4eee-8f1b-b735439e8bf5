import { ComponentPropsWithoutRef, type ComponentRef } from "react";
import { Primitive } from "@radix-ui/react-primitive";
type PrimitiveDivProps = ComponentPropsWithoutRef<typeof Primitive.div>;
export declare namespace AttachmentPrimitiveThumb {
    type Element = ComponentRef<typeof Primitive.div>;
    type Props = PrimitiveDivProps;
}
export declare const AttachmentPrimitiveThumb: import("react").ForwardRefExoticComponent<Omit<import("react").ClassAttributes<HTMLDivElement> & import("react").HTMLAttributes<HTMLDivElement> & {
    asChild?: boolean;
}, "ref"> & import("react").RefAttributes<HTMLDivElement>>;
export {};
//# sourceMappingURL=AttachmentThumb.d.ts.map