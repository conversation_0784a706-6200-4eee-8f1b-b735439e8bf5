{"version": 3, "sources": ["../../../src/runtimes/utils/MessageRepository.tsx"], "sourcesContent": ["import type { ThreadMessage } from \"../../types\";\nimport { generateId, generateOptimisticId } from \"../../utils/idUtils\";\nimport { ThreadMessageLike } from \"../external-store\";\nimport { getAutoStatus } from \"../external-store/auto-status\";\nimport { fromThreadMessageLike } from \"../external-store/ThreadMessageLike\";\n\n/**\n * Represents a parent node in the repository tree structure.\n */\ntype RepositoryParent = {\n  /** IDs of child messages */\n  children: string[];\n  /** Reference to the next message in the active branch */\n  next: RepositoryMessage | null;\n};\n\n/**\n * Represents a message node in the repository tree structure.\n */\ntype RepositoryMessage = RepositoryParent & {\n  /** Reference to the parent message */\n  prev: RepositoryMessage | null;\n  /** The actual message data */\n  current: ThreadMessage;\n  /** The depth level in the tree (0 for root messages) */\n  level: number;\n};\n\n/**\n * Represents a message item that can be exported from the repository.\n */\nexport type ExportedMessageRepositoryItem = {\n  /** The message data */\n  message: ThreadMessage;\n  /** ID of the parent message, or null for root messages */\n  parentId: string | null;\n};\n\n/**\n * Represents the entire repository state for export/import.\n */\nexport type ExportedMessageRepository = {\n  /** ID of the head message, or null/undefined if no head */\n  headId?: string | null;\n  /** Array of all messages with their parent references */\n  messages: Array<{\n    message: ThreadMessage;\n    parentId: string | null;\n  }>;\n};\n\n/**\n * Utility functions for working with exported message repositories.\n */\nexport const ExportedMessageRepository = {\n  /**\n   * Converts an array of messages to an ExportedMessageRepository format.\n   * Creates parent-child relationships based on the order of messages in the array.\n   *\n   * @param messages - Array of message-like objects to convert\n   * @returns ExportedMessageRepository with parent-child relationships established\n   */\n  fromArray: (\n    messages: readonly ThreadMessageLike[],\n  ): ExportedMessageRepository => {\n    const conv = messages.map((m) =>\n      fromThreadMessageLike(\n        m,\n        generateId(),\n        getAutoStatus(false, false, false),\n      ),\n    );\n\n    return {\n      messages: conv.map((m, idx) => ({\n        parentId: idx > 0 ? conv[idx - 1]!.id : null,\n        message: m,\n      })),\n    };\n  },\n};\n\n/**\n * Recursively finds the head (leaf) message in a branch.\n *\n * @param message - The starting message or parent node\n * @returns The leaf message of the branch, or null if not found\n */\nconst findHead = (\n  message: RepositoryMessage | RepositoryParent,\n): RepositoryMessage | null => {\n  if (message.next) return findHead(message.next);\n  if (\"current\" in message) return message;\n  return null;\n};\n\n/**\n * A utility class for caching computed values and invalidating the cache when needed.\n */\nclass CachedValue<T> {\n  private _value: T | null = null;\n\n  /**\n   * @param func - The function that computes the cached value\n   */\n  constructor(private func: () => T) {}\n\n  /**\n   * Gets the cached value, computing it if necessary.\n   */\n  get value() {\n    if (this._value === null) {\n      this._value = this.func();\n    }\n    return this._value;\n  }\n\n  /**\n   * Invalidates the cache, forcing recomputation on next access.\n   */\n  dirty() {\n    this._value = null;\n  }\n}\n\n/**\n * A repository that manages a tree of messages with branching capabilities.\n * Supports operations like adding, updating, and deleting messages, as well as\n * managing multiple conversation branches.\n */\nexport class MessageRepository {\n  /** Map of message IDs to repository message objects */\n  private messages = new Map<string, RepositoryMessage>();\n  /** Reference to the current head (most recent) message in the active branch */\n  private head: RepositoryMessage | null = null;\n  /** Root node of the tree structure */\n  private root: RepositoryParent = {\n    children: [],\n    next: null,\n  };\n\n  /**\n   * Performs link/unlink operations between messages in the tree.\n   *\n   * @param newParent - The new parent message, or null\n   * @param child - The child message to operate on\n   * @param operation - The type of operation to perform:\n   *   - \"cut\": Remove the child from its current parent\n   *   - \"link\": Add the child to a new parent\n   *   - \"relink\": Both cut and link operations\n   */\n  private performOp(\n    newParent: RepositoryMessage | null,\n    child: RepositoryMessage,\n    operation: \"cut\" | \"link\" | \"relink\",\n  ) {\n    const parentOrRoot = child.prev ?? this.root;\n    const newParentOrRoot = newParent ?? this.root;\n\n    if (operation === \"relink\" && parentOrRoot === newParentOrRoot) return;\n\n    // cut\n    if (operation !== \"link\") {\n      // remove from parentOrRoot.children\n      parentOrRoot.children = parentOrRoot.children.filter(\n        (m) => m !== child.current.id,\n      );\n\n      // update parentOrRoot.next\n      if (parentOrRoot.next === child) {\n        const fallbackId = parentOrRoot.children.at(-1);\n        const fallback = fallbackId ? this.messages.get(fallbackId) : null;\n        if (fallback === undefined) {\n          throw new Error(\n            \"MessageRepository(performOp/cut): Fallback sibling message not found. This is likely an internal bug in assistant-ui.\",\n          );\n        }\n        parentOrRoot.next = fallback;\n      }\n    }\n\n    // link\n    if (operation !== \"cut\") {\n      // ensure the child is not part of parent tree\n      for (\n        let current: RepositoryMessage | null = newParent;\n        current;\n        current = current.prev\n      ) {\n        if (current.current.id === child.current.id) {\n          throw new Error(\n            \"MessageRepository(performOp/link): A message with the same id already exists in the parent tree. This error occurs if the same message id is found multiple times. This is likely an internal bug in assistant-ui.\",\n          );\n        }\n      }\n\n      // add to parentOrRoot.children\n      newParentOrRoot.children = [\n        ...newParentOrRoot.children,\n        child.current.id,\n      ];\n\n      // update parentOrRoot.next\n      if (findHead(child) === this.head || newParentOrRoot.next === null) {\n        newParentOrRoot.next = child;\n      }\n\n      child.prev = newParent;\n    }\n  }\n\n  /** Cached array of messages in the current active branch, from root to head */\n  private _messages = new CachedValue<readonly ThreadMessage[]>(() => {\n    const messages = new Array<ThreadMessage>(this.head?.level ?? 0);\n    for (let current = this.head; current; current = current.prev) {\n      messages[current.level] = current.current;\n    }\n    return messages;\n  });\n\n  /**\n   * Gets the ID of the current head message.\n   * @returns The ID of the head message, or null if no messages exist\n   */\n  get headId() {\n    return this.head?.current.id ?? null;\n  }\n\n  /**\n   * Gets all messages in the current active branch, from root to head.\n   * @returns Array of messages in the current branch\n   */\n  getMessages() {\n    return this._messages.value;\n  }\n\n  /**\n   * Adds a new message or updates an existing one in the repository.\n   * If the message ID already exists, the message is updated and potentially relinked to a new parent.\n   * If the message is new, it's added as a child of the specified parent.\n   *\n   * @param parentId - ID of the parent message, or null for root messages\n   * @param message - The message to add or update\n   * @throws Error if the parent message is not found\n   */\n  addOrUpdateMessage(parentId: string | null, message: ThreadMessage) {\n    const existingItem = this.messages.get(message.id);\n    const prev = parentId ? this.messages.get(parentId) : null;\n    if (prev === undefined)\n      throw new Error(\n        \"MessageRepository(addOrUpdateMessage): Parent message not found. This is likely an internal bug in assistant-ui.\",\n      );\n\n    // update existing message\n    if (existingItem) {\n      existingItem.current = message;\n      this.performOp(prev, existingItem, \"relink\");\n      this._messages.dirty();\n      return;\n    }\n\n    // create a new message\n    const newItem: RepositoryMessage = {\n      prev,\n      current: message,\n      next: null,\n      children: [],\n      level: prev ? prev.level + 1 : 0,\n    };\n\n    this.messages.set(message.id, newItem);\n    this.performOp(prev, newItem, \"link\");\n\n    if (this.head === prev) {\n      this.head = newItem;\n    }\n\n    this._messages.dirty();\n  }\n\n  /**\n   * Gets a message and its parent ID by message ID.\n   *\n   * @param messageId - ID of the message to retrieve\n   * @returns Object containing the message and its parent ID\n   * @throws Error if the message is not found\n   */\n  getMessage(messageId: string) {\n    const message = this.messages.get(messageId);\n    if (!message)\n      throw new Error(\n        \"MessageRepository(updateMessage): Message not found. This is likely an internal bug in assistant-ui.\",\n      );\n\n    return {\n      parentId: message.prev?.current.id ?? null,\n      message: message.current,\n    };\n  }\n\n  /**\n   * Adds an optimistic message to the repository.\n   * An optimistic message is a temporary placeholder that will be replaced by a real message later.\n   *\n   * @param parentId - ID of the parent message, or null for root messages\n   * @param message - The core message to convert to an optimistic message\n   * @returns The generated optimistic ID\n   */\n  appendOptimisticMessage(parentId: string | null, message: ThreadMessageLike) {\n    let optimisticId: string;\n    do {\n      optimisticId = generateOptimisticId();\n    } while (this.messages.has(optimisticId));\n\n    this.addOrUpdateMessage(\n      parentId,\n      fromThreadMessageLike(message, optimisticId, { type: \"running\" }),\n    );\n\n    return optimisticId;\n  }\n\n  /**\n   * Deletes a message from the repository and relinks its children.\n   *\n   * @param messageId - ID of the message to delete\n   * @param replacementId - Optional ID of the message to become the new parent of the children,\n   *                       undefined means use the deleted message's parent,\n   *                       null means use the root\n   * @throws Error if the message or replacement is not found\n   */\n  deleteMessage(messageId: string, replacementId?: string | null | undefined) {\n    const message = this.messages.get(messageId);\n\n    if (!message)\n      throw new Error(\n        \"MessageRepository(deleteMessage): Message not found. This is likely an internal bug in assistant-ui.\",\n      );\n\n    const replacement =\n      replacementId === undefined\n        ? message.prev // if no replacementId is provided, use the parent\n        : replacementId === null\n          ? null\n          : this.messages.get(replacementId);\n    if (replacement === undefined)\n      throw new Error(\n        \"MessageRepository(deleteMessage): Replacement not found. This is likely an internal bug in assistant-ui.\",\n      );\n\n    for (const child of message.children) {\n      const childMessage = this.messages.get(child);\n      if (!childMessage)\n        throw new Error(\n          \"MessageRepository(deleteMessage): Child message not found. This is likely an internal bug in assistant-ui.\",\n        );\n      this.performOp(replacement, childMessage, \"relink\");\n    }\n\n    this.performOp(null, message, \"cut\");\n    this.messages.delete(messageId);\n\n    if (this.head === message) {\n      this.head = findHead(replacement ?? this.root);\n    }\n\n    this._messages.dirty();\n  }\n\n  /**\n   * Gets all branch IDs (sibling messages) at the level of a specified message.\n   *\n   * @param messageId - ID of the message to find branches for\n   * @returns Array of message IDs representing branches\n   * @throws Error if the message is not found\n   */\n  getBranches(messageId: string) {\n    const message = this.messages.get(messageId);\n    if (!message)\n      throw new Error(\n        \"MessageRepository(getBranches): Message not found. This is likely an internal bug in assistant-ui.\",\n      );\n\n    const { children } = message.prev ?? this.root;\n    return children;\n  }\n\n  /**\n   * Switches the active branch to the one containing the specified message.\n   *\n   * @param messageId - ID of the message in the branch to switch to\n   * @throws Error if the branch is not found\n   */\n  switchToBranch(messageId: string) {\n    const message = this.messages.get(messageId);\n    if (!message)\n      throw new Error(\n        \"MessageRepository(switchToBranch): Branch not found. This is likely an internal bug in assistant-ui.\",\n      );\n\n    const prevOrRoot = message.prev ?? this.root;\n    prevOrRoot.next = message;\n\n    this.head = findHead(message);\n\n    this._messages.dirty();\n  }\n\n  /**\n   * Resets the head to a specific message or null.\n   *\n   * @param messageId - ID of the message to set as head, or null to clear the head\n   * @throws Error if the message is not found\n   */\n  resetHead(messageId: string | null) {\n    if (messageId === null) {\n      this.head = null;\n      this._messages.dirty();\n      return;\n    }\n\n    const message = this.messages.get(messageId);\n    if (!message)\n      throw new Error(\n        \"MessageRepository(resetHead): Branch not found. This is likely an internal bug in assistant-ui.\",\n      );\n\n    this.head = message;\n    for (\n      let current: RepositoryMessage | null = message;\n      current;\n      current = current.prev\n    ) {\n      if (current.prev) {\n        current.prev.next = current;\n      }\n    }\n\n    this._messages.dirty();\n  }\n\n  /**\n   * Clears all messages from the repository.\n   */\n  clear(): void {\n    this.messages.clear();\n    this.head = null;\n    this.root = {\n      children: [],\n      next: null,\n    };\n    this._messages.dirty();\n  }\n\n  /**\n   * Exports the repository state for persistence.\n   *\n   * @returns Exportable repository state\n   */\n  export(): ExportedMessageRepository {\n    const exportItems: ExportedMessageRepository[\"messages\"] = [];\n\n    // hint: we are relying on the insertion order of the messages\n    // this is important for the import function to properly link the messages\n    for (const [, message] of this.messages) {\n      exportItems.push({\n        message: message.current,\n        parentId: message.prev?.current.id ?? null,\n      });\n    }\n\n    return {\n      headId: this.head?.current.id ?? null,\n      messages: exportItems,\n    };\n  }\n\n  /**\n   * Imports repository state from an exported repository.\n   *\n   * @param repository - The exported repository state to import\n   */\n  import({ headId, messages }: ExportedMessageRepository) {\n    for (const { message, parentId } of messages) {\n      this.addOrUpdateMessage(parentId, message);\n    }\n\n    // switch to the saved head id if it is not the most recent message\n    this.resetHead(headId ?? messages.at(-1)?.message.id ?? null);\n  }\n}\n"], "mappings": ";AACA,SAAS,YAAY,4BAA4B;AAEjD,SAAS,qBAAqB;AAC9B,SAAS,6BAA6B;AAkD/B,IAAM,4BAA4B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQvC,WAAW,CACT,aAC8B;AAC9B,UAAM,OAAO,SAAS;AAAA,MAAI,CAAC,MACzB;AAAA,QACE;AAAA,QACA,WAAW;AAAA,QACX,cAAc,OAAO,OAAO,KAAK;AAAA,MACnC;AAAA,IACF;AAEA,WAAO;AAAA,MACL,UAAU,KAAK,IAAI,CAAC,GAAG,SAAS;AAAA,QAC9B,UAAU,MAAM,IAAI,KAAK,MAAM,CAAC,EAAG,KAAK;AAAA,QACxC,SAAS;AAAA,MACX,EAAE;AAAA,IACJ;AAAA,EACF;AACF;AAQA,IAAM,WAAW,CACf,YAC6B;AAC7B,MAAI,QAAQ,KAAM,QAAO,SAAS,QAAQ,IAAI;AAC9C,MAAI,aAAa,QAAS,QAAO;AACjC,SAAO;AACT;AAKA,IAAM,cAAN,MAAqB;AAAA;AAAA;AAAA;AAAA,EAMnB,YAAoB,MAAe;AAAf;AAAA,EAAgB;AAAA,EAL5B,SAAmB;AAAA;AAAA;AAAA;AAAA,EAU3B,IAAI,QAAQ;AACV,QAAI,KAAK,WAAW,MAAM;AACxB,WAAK,SAAS,KAAK,KAAK;AAAA,IAC1B;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ;AACN,SAAK,SAAS;AAAA,EAChB;AACF;AAOO,IAAM,oBAAN,MAAwB;AAAA;AAAA,EAErB,WAAW,oBAAI,IAA+B;AAAA;AAAA,EAE9C,OAAiC;AAAA;AAAA,EAEjC,OAAyB;AAAA,IAC/B,UAAU,CAAC;AAAA,IACX,MAAM;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYQ,UACN,WACA,OACA,WACA;AACA,UAAM,eAAe,MAAM,QAAQ,KAAK;AACxC,UAAM,kBAAkB,aAAa,KAAK;AAE1C,QAAI,cAAc,YAAY,iBAAiB,gBAAiB;AAGhE,QAAI,cAAc,QAAQ;AAExB,mBAAa,WAAW,aAAa,SAAS;AAAA,QAC5C,CAAC,MAAM,MAAM,MAAM,QAAQ;AAAA,MAC7B;AAGA,UAAI,aAAa,SAAS,OAAO;AAC/B,cAAM,aAAa,aAAa,SAAS,GAAG,EAAE;AAC9C,cAAM,WAAW,aAAa,KAAK,SAAS,IAAI,UAAU,IAAI;AAC9D,YAAI,aAAa,QAAW;AAC1B,gBAAM,IAAI;AAAA,YACR;AAAA,UACF;AAAA,QACF;AACA,qBAAa,OAAO;AAAA,MACtB;AAAA,IACF;AAGA,QAAI,cAAc,OAAO;AAEvB,eACM,UAAoC,WACxC,SACA,UAAU,QAAQ,MAClB;AACA,YAAI,QAAQ,QAAQ,OAAO,MAAM,QAAQ,IAAI;AAC3C,gBAAM,IAAI;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAGA,sBAAgB,WAAW;AAAA,QACzB,GAAG,gBAAgB;AAAA,QACnB,MAAM,QAAQ;AAAA,MAChB;AAGA,UAAI,SAAS,KAAK,MAAM,KAAK,QAAQ,gBAAgB,SAAS,MAAM;AAClE,wBAAgB,OAAO;AAAA,MACzB;AAEA,YAAM,OAAO;AAAA,IACf;AAAA,EACF;AAAA;AAAA,EAGQ,YAAY,IAAI,YAAsC,MAAM;AAClE,UAAM,WAAW,IAAI,MAAqB,KAAK,MAAM,SAAS,CAAC;AAC/D,aAAS,UAAU,KAAK,MAAM,SAAS,UAAU,QAAQ,MAAM;AAC7D,eAAS,QAAQ,KAAK,IAAI,QAAQ;AAAA,IACpC;AACA,WAAO;AAAA,EACT,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,IAAI,SAAS;AACX,WAAO,KAAK,MAAM,QAAQ,MAAM;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc;AACZ,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,mBAAmB,UAAyB,SAAwB;AAClE,UAAM,eAAe,KAAK,SAAS,IAAI,QAAQ,EAAE;AACjD,UAAM,OAAO,WAAW,KAAK,SAAS,IAAI,QAAQ,IAAI;AACtD,QAAI,SAAS;AACX,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAGF,QAAI,cAAc;AAChB,mBAAa,UAAU;AACvB,WAAK,UAAU,MAAM,cAAc,QAAQ;AAC3C,WAAK,UAAU,MAAM;AACrB;AAAA,IACF;AAGA,UAAM,UAA6B;AAAA,MACjC;AAAA,MACA,SAAS;AAAA,MACT,MAAM;AAAA,MACN,UAAU,CAAC;AAAA,MACX,OAAO,OAAO,KAAK,QAAQ,IAAI;AAAA,IACjC;AAEA,SAAK,SAAS,IAAI,QAAQ,IAAI,OAAO;AACrC,SAAK,UAAU,MAAM,SAAS,MAAM;AAEpC,QAAI,KAAK,SAAS,MAAM;AACtB,WAAK,OAAO;AAAA,IACd;AAEA,SAAK,UAAU,MAAM;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,WAAW,WAAmB;AAC5B,UAAM,UAAU,KAAK,SAAS,IAAI,SAAS;AAC3C,QAAI,CAAC;AACH,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAEF,WAAO;AAAA,MACL,UAAU,QAAQ,MAAM,QAAQ,MAAM;AAAA,MACtC,SAAS,QAAQ;AAAA,IACnB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,wBAAwB,UAAyB,SAA4B;AAC3E,QAAI;AACJ,OAAG;AACD,qBAAe,qBAAqB;AAAA,IACtC,SAAS,KAAK,SAAS,IAAI,YAAY;AAEvC,SAAK;AAAA,MACH;AAAA,MACA,sBAAsB,SAAS,cAAc,EAAE,MAAM,UAAU,CAAC;AAAA,IAClE;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,cAAc,WAAmB,eAA2C;AAC1E,UAAM,UAAU,KAAK,SAAS,IAAI,SAAS;AAE3C,QAAI,CAAC;AACH,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAEF,UAAM,cACJ,kBAAkB,SACd,QAAQ,OACR,kBAAkB,OAChB,OACA,KAAK,SAAS,IAAI,aAAa;AACvC,QAAI,gBAAgB;AAClB,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAEF,eAAW,SAAS,QAAQ,UAAU;AACpC,YAAM,eAAe,KAAK,SAAS,IAAI,KAAK;AAC5C,UAAI,CAAC;AACH,cAAM,IAAI;AAAA,UACR;AAAA,QACF;AACF,WAAK,UAAU,aAAa,cAAc,QAAQ;AAAA,IACpD;AAEA,SAAK,UAAU,MAAM,SAAS,KAAK;AACnC,SAAK,SAAS,OAAO,SAAS;AAE9B,QAAI,KAAK,SAAS,SAAS;AACzB,WAAK,OAAO,SAAS,eAAe,KAAK,IAAI;AAAA,IAC/C;AAEA,SAAK,UAAU,MAAM;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,YAAY,WAAmB;AAC7B,UAAM,UAAU,KAAK,SAAS,IAAI,SAAS;AAC3C,QAAI,CAAC;AACH,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAEF,UAAM,EAAE,SAAS,IAAI,QAAQ,QAAQ,KAAK;AAC1C,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,eAAe,WAAmB;AAChC,UAAM,UAAU,KAAK,SAAS,IAAI,SAAS;AAC3C,QAAI,CAAC;AACH,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAEF,UAAM,aAAa,QAAQ,QAAQ,KAAK;AACxC,eAAW,OAAO;AAElB,SAAK,OAAO,SAAS,OAAO;AAE5B,SAAK,UAAU,MAAM;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,WAA0B;AAClC,QAAI,cAAc,MAAM;AACtB,WAAK,OAAO;AACZ,WAAK,UAAU,MAAM;AACrB;AAAA,IACF;AAEA,UAAM,UAAU,KAAK,SAAS,IAAI,SAAS;AAC3C,QAAI,CAAC;AACH,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAEF,SAAK,OAAO;AACZ,aACM,UAAoC,SACxC,SACA,UAAU,QAAQ,MAClB;AACA,UAAI,QAAQ,MAAM;AAChB,gBAAQ,KAAK,OAAO;AAAA,MACtB;AAAA,IACF;AAEA,SAAK,UAAU,MAAM;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAKA,QAAc;AACZ,SAAK,SAAS,MAAM;AACpB,SAAK,OAAO;AACZ,SAAK,OAAO;AAAA,MACV,UAAU,CAAC;AAAA,MACX,MAAM;AAAA,IACR;AACA,SAAK,UAAU,MAAM;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAoC;AAClC,UAAM,cAAqD,CAAC;AAI5D,eAAW,CAAC,EAAE,OAAO,KAAK,KAAK,UAAU;AACvC,kBAAY,KAAK;AAAA,QACf,SAAS,QAAQ;AAAA,QACjB,UAAU,QAAQ,MAAM,QAAQ,MAAM;AAAA,MACxC,CAAC;AAAA,IACH;AAEA,WAAO;AAAA,MACL,QAAQ,KAAK,MAAM,QAAQ,MAAM;AAAA,MACjC,UAAU;AAAA,IACZ;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,EAAE,QAAQ,SAAS,GAA8B;AACtD,eAAW,EAAE,SAAS,SAAS,KAAK,UAAU;AAC5C,WAAK,mBAAmB,UAAU,OAAO;AAAA,IAC3C;AAGA,SAAK,UAAU,UAAU,SAAS,GAAG,EAAE,GAAG,QAAQ,MAAM,IAAI;AAAA,EAC9D;AACF;", "names": []}