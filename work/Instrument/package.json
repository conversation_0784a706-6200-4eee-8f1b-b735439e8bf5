{"name": "instrument-web-app", "version": "1.0.0", "description": "The Instrument - Optical screen capture system with LLM integration", "main": "index.js", "scripts": {"dev": "concurrently \"npm run backend\" \"npm run frontend\"", "backend": "cd backend && npm run dev", "frontend": "cd frontend && npm run dev", "start": "concurrently \"npm run backend:start\" \"npm run frontend:build\"", "backend:start": "cd backend && npm start", "frontend:build": "cd frontend && npm run build", "install-all": "npm install && cd backend && npm install && cd ../frontend && npm install", "test-llm": "node test-llm.js", "setup": "npm run install-all && cp .env.example .env && echo 'Setup complete! Please edit .env with your API key.'"}, "keywords": ["screen-capture", "llm", "claude", "optical-capture"], "author": "", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2", "node-fetch": "^2.7.0"}}