{"version": 3, "sources": ["../../../src/utils/json/is-json.ts"], "sourcesContent": ["import {\n  ReadonlyJSONArray,\n  <PERSON>onlyJSONObject,\n  ReadonlyJSONValue,\n} from \"assistant-stream/utils\";\n\nexport function isJSONValue(\n  value: unknown,\n  currentDepth: number = 0,\n): value is ReadonlyJSONValue {\n  // Protect against too deep recursion\n  if (currentDepth > 100) {\n    return false;\n  }\n\n  if (\n    value === null ||\n    typeof value === \"string\" ||\n    typeof value === \"boolean\"\n  ) {\n    return true;\n  }\n\n  // Handle special number cases\n  if (typeof value === \"number\") {\n    return !Number.isNaN(value) && Number.isFinite(value);\n  }\n\n  if (Array.isArray(value)) {\n    return value.every((item) => isJSONValue(item, currentDepth + 1));\n  }\n\n  if (typeof value === \"object\") {\n    return Object.entries(value).every(\n      ([key, val]) =>\n        typeof key === \"string\" && isJSONValue(val, currentDepth + 1),\n    );\n  }\n\n  return false;\n}\n\nexport function isJSONArray(value: unknown): value is ReadonlyJSONArray {\n  return Array.isArray(value) && value.every(isJSONValue);\n}\n\nexport function isJSONObject(value: unknown): value is ReadonlyJSONObject {\n  return (\n    value != null &&\n    typeof value === \"object\" &&\n    Object.entries(value).every(\n      ([key, val]) => typeof key === \"string\" && isJSONValue(val),\n    )\n  );\n}\n"], "mappings": ";AAMO,SAAS,YACd,OACA,eAAuB,GACK;AAE5B,MAAI,eAAe,KAAK;AACtB,WAAO;AAAA,EACT;AAEA,MACE,UAAU,QACV,OAAO,UAAU,YACjB,OAAO,UAAU,WACjB;AACA,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO,CAAC,OAAO,MAAM,KAAK,KAAK,OAAO,SAAS,KAAK;AAAA,EACtD;AAEA,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,WAAO,MAAM,MAAM,CAAC,SAAS,YAAY,MAAM,eAAe,CAAC,CAAC;AAAA,EAClE;AAEA,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO,OAAO,QAAQ,KAAK,EAAE;AAAA,MAC3B,CAAC,CAAC,KAAK,GAAG,MACR,OAAO,QAAQ,YAAY,YAAY,KAAK,eAAe,CAAC;AAAA,IAChE;AAAA,EACF;AAEA,SAAO;AACT;AAEO,SAAS,YAAY,OAA4C;AACtE,SAAO,MAAM,QAAQ,KAAK,KAAK,MAAM,MAAM,WAAW;AACxD;AAEO,SAAS,aAAa,OAA6C;AACxE,SACE,SAAS,QACT,OAAO,UAAU,YACjB,OAAO,QAAQ,KAAK,EAAE;AAAA,IACpB,CAAC,CAAC,KAAK,GAAG,MAAM,OAAO,QAAQ,YAAY,YAAY,GAAG;AAAA,EAC5D;AAEJ;", "names": []}