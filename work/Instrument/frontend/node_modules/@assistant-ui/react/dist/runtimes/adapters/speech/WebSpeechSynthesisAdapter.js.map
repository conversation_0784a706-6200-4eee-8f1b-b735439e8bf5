{"version": 3, "sources": ["../../../../src/runtimes/adapters/speech/WebSpeechSynthesisAdapter.ts"], "sourcesContent": ["import { SpeechSynthesisAdapter } from \"./SpeechAdapterTypes\";\n\nexport class WebSpeechSynthesisAdapter implements SpeechSynthesisAdapter {\n  speak(text: string): SpeechSynthesisAdapter.Utterance {\n    const utterance = new SpeechSynthesisUtterance(text);\n\n    const subscribers = new Set<() => void>();\n    const handleEnd = (\n      reason: \"finished\" | \"error\" | \"cancelled\",\n      error?: unknown,\n    ) => {\n      if (res.status.type === \"ended\") return;\n\n      res.status = { type: \"ended\", reason, error };\n      subscribers.forEach((handler) => handler());\n    };\n\n    utterance.addEventListener(\"end\", () => handleEnd(\"finished\"));\n    utterance.addEventListener(\"error\", (e) => handleEnd(\"error\", e.error));\n\n    window.speechSynthesis.speak(utterance);\n\n    const res: SpeechSynthesisAdapter.Utterance = {\n      status: { type: \"running\" },\n      cancel: () => {\n        window.speechSynthesis.cancel();\n        handleEnd(\"cancelled\");\n      },\n      subscribe: (callback) => {\n        if (res.status.type === \"ended\") {\n          let cancelled = false;\n          queueMicrotask(() => {\n            if (!cancelled) callback();\n          });\n          return () => {\n            cancelled = true;\n          };\n        } else {\n          subscribers.add(callback);\n          return () => {\n            subscribers.delete(callback);\n          };\n        }\n      },\n    };\n    return res;\n  }\n}\n"], "mappings": ";AAEO,IAAM,4BAAN,MAAkE;AAAA,EACvE,MAAM,MAAgD;AACpD,UAAM,YAAY,IAAI,yBAAyB,IAAI;AAEnD,UAAM,cAAc,oBAAI,IAAgB;AACxC,UAAM,YAAY,CAChB,QACA,UACG;AACH,UAAI,IAAI,OAAO,SAAS,QAAS;AAEjC,UAAI,SAAS,EAAE,MAAM,SAAS,QAAQ,MAAM;AAC5C,kBAAY,QAAQ,CAAC,YAAY,QAAQ,CAAC;AAAA,IAC5C;AAEA,cAAU,iBAAiB,OAAO,MAAM,UAAU,UAAU,CAAC;AAC7D,cAAU,iBAAiB,SAAS,CAAC,MAAM,UAAU,SAAS,EAAE,KAAK,CAAC;AAEtE,WAAO,gBAAgB,MAAM,SAAS;AAEtC,UAAM,MAAwC;AAAA,MAC5C,QAAQ,EAAE,MAAM,UAAU;AAAA,MAC1B,QAAQ,MAAM;AACZ,eAAO,gBAAgB,OAAO;AAC9B,kBAAU,WAAW;AAAA,MACvB;AAAA,MACA,WAAW,CAAC,aAAa;AACvB,YAAI,IAAI,OAAO,SAAS,SAAS;AAC/B,cAAI,YAAY;AAChB,yBAAe,MAAM;AACnB,gBAAI,CAAC,UAAW,UAAS;AAAA,UAC3B,CAAC;AACD,iBAAO,MAAM;AACX,wBAAY;AAAA,UACd;AAAA,QACF,OAAO;AACL,sBAAY,IAAI,QAAQ;AACxB,iBAAO,MAAM;AACX,wBAAY,OAAO,QAAQ;AAAA,UAC7B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;", "names": []}