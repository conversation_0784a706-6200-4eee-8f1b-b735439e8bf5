{"version": 3, "sources": ["../../../src/runtimes/remote-thread-list/OptimisticState.ts"], "sourcesContent": ["import { BaseSubscribable } from \"./BaseSubscribable\";\n\ntype Transform<TState, TResult> = {\n  execute: () => Promise<TResult>;\n\n  /** transform the state after the promise resolves */\n  then?: (state: TState, result: TResult) => TState;\n\n  /** transform the state during resolution and afterwards */\n  optimistic?: (state: TState) => TState;\n\n  /** transform the state only while loading */\n  loading?: (state: TState, task: Promise<TResult>) => TState;\n};\n\ntype PendingTransform<TState, TResult> = Transform<TState, TResult> & {\n  task: Promise<TResult>;\n};\n\nconst pipeTransforms = <TState, TExtra>(\n  initialState: TState,\n  extraParam: TExtra,\n  transforms: (((state: TState, extra: TExtra) => TState) | undefined)[],\n): TState => {\n  return transforms.reduce((state, transform) => {\n    return transform?.(state, extraParam) ?? state;\n  }, initialState);\n};\n\nexport class OptimisticState<TState> extends BaseSubscribable {\n  private readonly _pendingTransforms: Array<PendingTransform<TState, any>> =\n    [];\n  private _baseValue: TState;\n  private _cachedValue: TState;\n\n  public constructor(initialState: TState) {\n    super();\n    this._baseValue = initialState;\n    this._cachedValue = initialState;\n  }\n\n  private _updateState(): void {\n    this._cachedValue = this._pendingTransforms.reduce((state, transform) => {\n      return pipeTransforms(state, transform.task, [\n        transform.loading,\n        transform.optimistic,\n      ]);\n    }, this._baseValue);\n\n    this._notifySubscribers();\n  }\n\n  public get baseValue(): TState {\n    return this._baseValue;\n  }\n\n  public get value(): TState {\n    return this._cachedValue;\n  }\n\n  public update(state: TState): void {\n    this._baseValue = state;\n    this._updateState();\n  }\n\n  public async optimisticUpdate<TResult>(\n    transform: Transform<TState, TResult>,\n  ): Promise<TResult> {\n    const task = transform.execute();\n    const pendingTransform = { ...transform, task };\n    try {\n      this._pendingTransforms.push(pendingTransform);\n      this._updateState();\n\n      const result = await task;\n      this._baseValue = pipeTransforms(this._baseValue, result, [\n        transform.optimistic,\n        transform.then,\n      ]);\n      return result;\n    } finally {\n      const index = this._pendingTransforms.indexOf(pendingTransform);\n      if (index > -1) {\n        this._pendingTransforms.splice(index, 1);\n      }\n      this._updateState();\n    }\n  }\n}\n"], "mappings": ";AAAA,SAAS,wBAAwB;AAmBjC,IAAM,iBAAiB,CACrB,cACA,YACA,eACW;AACX,SAAO,WAAW,OAAO,CAAC,OAAO,cAAc;AAC7C,WAAO,YAAY,OAAO,UAAU,KAAK;AAAA,EAC3C,GAAG,YAAY;AACjB;AAEO,IAAM,kBAAN,cAAsC,iBAAiB;AAAA,EAC3C,qBACf,CAAC;AAAA,EACK;AAAA,EACA;AAAA,EAED,YAAY,cAAsB;AACvC,UAAM;AACN,SAAK,aAAa;AAClB,SAAK,eAAe;AAAA,EACtB;AAAA,EAEQ,eAAqB;AAC3B,SAAK,eAAe,KAAK,mBAAmB,OAAO,CAAC,OAAO,cAAc;AACvE,aAAO,eAAe,OAAO,UAAU,MAAM;AAAA,QAC3C,UAAU;AAAA,QACV,UAAU;AAAA,MACZ,CAAC;AAAA,IACH,GAAG,KAAK,UAAU;AAElB,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EAEA,IAAW,YAAoB;AAC7B,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAW,QAAgB;AACzB,WAAO,KAAK;AAAA,EACd;AAAA,EAEO,OAAO,OAAqB;AACjC,SAAK,aAAa;AAClB,SAAK,aAAa;AAAA,EACpB;AAAA,EAEA,MAAa,iBACX,WACkB;AAClB,UAAM,OAAO,UAAU,QAAQ;AAC/B,UAAM,mBAAmB,EAAE,GAAG,WAAW,KAAK;AAC9C,QAAI;AACF,WAAK,mBAAmB,KAAK,gBAAgB;AAC7C,WAAK,aAAa;AAElB,YAAM,SAAS,MAAM;AACrB,WAAK,aAAa,eAAe,KAAK,YAAY,QAAQ;AAAA,QACxD,UAAU;AAAA,QACV,UAAU;AAAA,MACZ,CAAC;AACD,aAAO;AAAA,IACT,UAAE;AACA,YAAM,QAAQ,KAAK,mBAAmB,QAAQ,gBAAgB;AAC9D,UAAI,QAAQ,IAAI;AACd,aAAK,mBAAmB,OAAO,OAAO,CAAC;AAAA,MACzC;AACA,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AACF;", "names": []}