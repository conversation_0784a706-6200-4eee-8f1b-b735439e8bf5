import { ComponentPropsWithoutRef, ComponentRef } from "react";
import * as PopoverPrimitive from "@radix-ui/react-popover";
export declare namespace AssistantModalPrimitiveAnchor {
    type Element = ComponentRef<typeof PopoverPrimitive.Anchor>;
    type Props = ComponentPropsWithoutRef<typeof PopoverPrimitive.Anchor>;
}
export declare const AssistantModalPrimitiveAnchor: import("react").ForwardRefExoticComponent<Omit<PopoverPrimitive.PopoverAnchorProps & import("react").RefAttributes<HTMLDivElement>, "ref"> & import("react").RefAttributes<HTMLDivElement>>;
//# sourceMappingURL=AssistantModalAnchor.d.ts.map