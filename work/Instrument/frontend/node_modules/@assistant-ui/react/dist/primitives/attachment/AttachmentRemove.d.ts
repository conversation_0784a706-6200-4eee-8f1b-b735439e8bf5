import { ActionButtonElement, ActionButtonProps } from "../../utils/createActionButton";
declare const useAttachmentRemove: () => () => void;
export declare namespace AttachmentPrimitiveRemove {
    type Element = ActionButtonElement;
    type Props = ActionButtonProps<typeof useAttachmentRemove>;
}
export declare const AttachmentPrimitiveRemove: import("react").ForwardRefExoticComponent<Omit<import("react").ClassAttributes<HTMLButtonElement> & import("react").ButtonHTMLAttributes<HTMLButtonElement> & {
    asChild?: boolean;
}, "ref"> & import("react").RefAttributes<HTMLButtonElement>>;
export {};
//# sourceMappingURL=AttachmentRemove.d.ts.map