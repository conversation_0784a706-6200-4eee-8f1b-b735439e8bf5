// Simple test script to verify LLM integration
// Run with: node test-llm.js

require('dotenv').config();
const fetch = require('node-fetch');

async function testLLMEndpoint() {
  console.log('🧪 Testing LLM endpoint...');
  
  // Create a simple test image (1x1 pixel base64 encoded)
  const testImage = '/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8A8A';
  
  try {
    const response = await fetch('http://localhost:3000/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        images: [testImage],
        context: 'This is a test image'
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    console.log('✅ LLM endpoint is responding');
    console.log('📡 Response headers:', response.headers.get('content-type'));
    
    // Read the streaming response
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let fullResponse = '';

    while (true) {
      const { done, value } = await reader.read();
      
      if (done) break;

      const chunk = decoder.decode(value);
      const lines = chunk.split('\n');

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          
          if (data === '[DONE]') {
            console.log('🏁 Stream completed');
            console.log('📝 Full response:', fullResponse);
            return;
          }

          try {
            const parsed = JSON.parse(data);
            if (parsed.text) {
              fullResponse += parsed.text;
              process.stdout.write(parsed.text);
            }
          } catch (parseError) {
            // Ignore parsing errors for partial chunks
          }
        }
      }
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.message.includes('ECONNREFUSED')) {
      console.log('💡 Make sure the backend server is running: npm run backend');
    } else if (error.message.includes('401') || error.message.includes('403')) {
      console.log('💡 Check your ANTHROPIC_API_KEY in the .env file');
    }
  }
}

// Check if API key is configured
if (!process.env.ANTHROPIC_API_KEY || process.env.ANTHROPIC_API_KEY === 'your_anthropic_api_key_here') {
  console.log('⚠️  Please configure your ANTHROPIC_API_KEY in the .env file');
  console.log('📝 Copy .env.example to .env and add your API key');
  process.exit(1);
}

testLLMEndpoint();
