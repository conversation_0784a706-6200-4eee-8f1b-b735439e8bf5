{"version": 3, "sources": ["../../../src/primitives/branchPicker/BranchPickerNext.tsx"], "sourcesContent": ["\"use client\";\n\nimport {\n  ActionButtonElement,\n  ActionButtonProps,\n  createActionButton,\n} from \"../../utils/createActionButton\";\nimport { useCallback } from \"react\";\nimport {\n  useMessage,\n  useMessageRuntime,\n} from \"../../context/react/MessageContext\";\n\nconst useBranchPickerNext = () => {\n  const messageRuntime = useMessageRuntime();\n  const disabled = useMessage((m) => m.branchNumber >= m.branchCount);\n\n  const callback = useCallback(() => {\n    messageRuntime.switchToBranch({ position: \"next\" });\n  }, [messageRuntime]);\n\n  if (disabled) return null;\n  return callback;\n};\n\nexport namespace BranchPickerPrimitiveNext {\n  export type Element = ActionButtonElement;\n  /**\n   * Props for the BranchPickerPrimitive.Next component.\n   * Inherits all button element props and action button functionality.\n   */\n  export type Props = ActionButtonProps<typeof useBranchPickerNext>;\n}\n\n/**\n * A button component that navigates to the next branch in the message tree.\n *\n * This component automatically handles switching to the next available branch\n * and is disabled when there are no more branches to navigate to.\n *\n * @example\n * ```tsx\n * <BranchPickerPrimitive.Next>\n *   Next →\n * </BranchPickerPrimitive.Next>\n * ```\n */\nexport const BranchPickerPrimitiveNext = createActionButton(\n  \"BranchPickerPrimitive.Next\",\n  useBranchPickerNext,\n);\n"], "mappings": ";;;AAEA;AAAA,EAGE;AAAA,OACK;AACP,SAAS,mBAAmB;AAC5B;AAAA,EACE;AAAA,EACA;AAAA,OACK;AAEP,IAAM,sBAAsB,MAAM;AAChC,QAAM,iBAAiB,kBAAkB;AACzC,QAAM,WAAW,WAAW,CAAC,MAAM,EAAE,gBAAgB,EAAE,WAAW;AAElE,QAAM,WAAW,YAAY,MAAM;AACjC,mBAAe,eAAe,EAAE,UAAU,OAAO,CAAC;AAAA,EACpD,GAAG,CAAC,cAAc,CAAC;AAEnB,MAAI,SAAU,QAAO;AACrB,SAAO;AACT;AAwBO,IAAM,4BAA4B;AAAA,EACvC;AAAA,EACA;AACF;", "names": []}