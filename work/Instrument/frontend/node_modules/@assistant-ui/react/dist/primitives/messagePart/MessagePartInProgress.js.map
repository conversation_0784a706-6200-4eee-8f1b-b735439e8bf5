{"version": 3, "sources": ["../../../src/primitives/messagePart/MessagePartInProgress.tsx"], "sourcesContent": ["\"use client\";\n\nimport { FC, PropsWithChildren } from \"react\";\nimport { useMessagePart } from \"../../context\";\n\nexport namespace MessagePartPrimitiveInProgress {\n  export type Props = PropsWithChildren;\n}\n\n// TODO should this be renamed to IsRunning?\nexport const MessagePartPrimitiveInProgress: FC<\n  MessagePartPrimitiveInProgress.Props\n> = ({ children }) => {\n  const isInProgress = useMessagePart((c) => c.status.type === \"running\");\n\n  return isInProgress ? children : null;\n};\n\nMessagePartPrimitiveInProgress.displayName = \"MessagePartPrimitive.InProgress\";\n"], "mappings": ";;;AAGA,SAAS,sBAAsB;AAOxB,IAAM,iCAET,CAAC,EAAE,SAAS,MAAM;AACpB,QAAM,eAAe,eAAe,CAAC,MAAM,EAAE,OAAO,SAAS,SAAS;AAEtE,SAAO,eAAe,WAAW;AACnC;AAEA,+BAA+B,cAAc;", "names": []}