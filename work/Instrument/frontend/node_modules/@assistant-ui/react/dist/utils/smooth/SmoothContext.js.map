{"version": 3, "sources": ["../../../src/utils/smooth/SmoothContext.tsx"], "sourcesContent": ["\"use client\";\n\nimport {\n  ComponentType,\n  createContext,\n  FC,\n  forwardRef,\n  PropsWithChildren,\n  useContext,\n  useState,\n} from \"react\";\nimport { ReadonlyStore } from \"../../context/ReadonlyStore\";\nimport { create, UseBoundStore } from \"zustand\";\nimport {\n  MessagePartStatus,\n  ToolCallMessagePartStatus,\n} from \"../../types/AssistantTypes\";\nimport { useMessagePartRuntime } from \"../../context/react/MessagePartContext\";\nimport { createContextStoreHook } from \"../../context/react/utils/createContextStoreHook\";\n\ntype SmoothContextValue = {\n  useSmoothStatus: UseBoundStore<\n    ReadonlyStore<MessagePartStatus | ToolCallMessagePartStatus>\n  >;\n};\n\nconst SmoothContext = createContext<SmoothContextValue | null>(null);\n\nconst makeSmoothContext = (\n  initialState: MessagePartStatus | ToolCallMessagePartStatus,\n) => {\n  const useSmoothStatus = create(() => initialState);\n  return { useSmoothStatus };\n};\n\nexport const SmoothContextProvider: FC<PropsWithChildren> = ({ children }) => {\n  const outer = useSmoothContext({ optional: true });\n  const MessagePartRuntime = useMessagePartRuntime();\n\n  const [context] = useState(() =>\n    makeSmoothContext(MessagePartRuntime.getState().status),\n  );\n\n  // do not wrap if there is an outer SmoothContextProvider\n  if (outer) return children;\n\n  return (\n    <SmoothContext.Provider value={context}>{children}</SmoothContext.Provider>\n  );\n};\n\nexport const withSmoothContextProvider = <C extends ComponentType<any>>(\n  Component: C,\n): C => {\n  const Wrapped = forwardRef((props, ref) => {\n    return (\n      <SmoothContextProvider>\n        <Component {...(props as any)} ref={ref} />\n      </SmoothContextProvider>\n    );\n  });\n  Wrapped.displayName = Component.displayName;\n  return Wrapped as any;\n};\n\nfunction useSmoothContext(options?: {\n  optional?: false | undefined;\n}): SmoothContextValue;\nfunction useSmoothContext(options?: {\n  optional?: boolean | undefined;\n}): SmoothContextValue | null;\nfunction useSmoothContext(options?: { optional?: boolean | undefined }) {\n  const context = useContext(SmoothContext);\n  if (!options?.optional && !context)\n    throw new Error(\n      \"This component must be used within a SmoothContextProvider.\",\n    );\n  return context;\n}\n\nexport const { useSmoothStatus, useSmoothStatusStore } = createContextStoreHook(\n  useSmoothContext,\n  \"useSmoothStatus\",\n);\n"], "mappings": ";;;AAEA;AAAA,EAEE;AAAA,EAEA;AAAA,EAEA;AAAA,EACA;AAAA,OACK;AAEP,SAAS,cAA6B;AAKtC,SAAS,6BAA6B;AACtC,SAAS,8BAA8B;AA6BnC;AArBJ,IAAM,gBAAgB,cAAyC,IAAI;AAEnE,IAAM,oBAAoB,CACxB,iBACG;AACH,QAAMA,mBAAkB,OAAO,MAAM,YAAY;AACjD,SAAO,EAAE,iBAAAA,iBAAgB;AAC3B;AAEO,IAAM,wBAA+C,CAAC,EAAE,SAAS,MAAM;AAC5E,QAAM,QAAQ,iBAAiB,EAAE,UAAU,KAAK,CAAC;AACjD,QAAM,qBAAqB,sBAAsB;AAEjD,QAAM,CAAC,OAAO,IAAI;AAAA,IAAS,MACzB,kBAAkB,mBAAmB,SAAS,EAAE,MAAM;AAAA,EACxD;AAGA,MAAI,MAAO,QAAO;AAElB,SACE,oBAAC,cAAc,UAAd,EAAuB,OAAO,SAAU,UAAS;AAEtD;AAEO,IAAM,4BAA4B,CACvC,cACM;AACN,QAAM,UAAU,WAAW,CAAC,OAAO,QAAQ;AACzC,WACE,oBAAC,yBACC,8BAAC,aAAW,GAAI,OAAe,KAAU,GAC3C;AAAA,EAEJ,CAAC;AACD,UAAQ,cAAc,UAAU;AAChC,SAAO;AACT;AAQA,SAAS,iBAAiB,SAA8C;AACtE,QAAM,UAAU,WAAW,aAAa;AACxC,MAAI,CAAC,SAAS,YAAY,CAAC;AACzB,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AACF,SAAO;AACT;AAEO,IAAM,EAAE,iBAAiB,qBAAqB,IAAI;AAAA,EACvD;AAAA,EACA;AACF;", "names": ["useSmoothStatus"]}