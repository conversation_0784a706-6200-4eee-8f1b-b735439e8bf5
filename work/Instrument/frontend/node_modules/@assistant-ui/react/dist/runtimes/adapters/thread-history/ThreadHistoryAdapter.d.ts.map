{"version": 3, "file": "ThreadHistoryAdapter.d.ts", "sourceRoot": "", "sources": ["../../../../src/runtimes/adapters/thread-history/ThreadHistoryAdapter.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,kBAAkB,EAAE,MAAM,aAAa,CAAC;AACtE,OAAO,EACL,yBAAyB,EACzB,6BAA6B,EAC9B,MAAM,+BAA+B,CAAC;AACvC,OAAO,EACL,oBAAoB,EACpB,iBAAiB,EACjB,uBAAuB,EACxB,MAAM,wBAAwB,CAAC;AAEhC,MAAM,MAAM,2BAA2B,CAAC,QAAQ,IAAI;IAClD,IAAI,IAAI,OAAO,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC,CAAC;IACnD,MAAM,CAAC,IAAI,EAAE,iBAAiB,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;CAC1D,CAAC;AAEF,MAAM,MAAM,oBAAoB,GAAG;IACjC,IAAI,IAAI,OAAO,CAAC,yBAAyB,GAAG;QAAE,eAAe,CAAC,EAAE,OAAO,CAAA;KAAE,CAAC,CAAC;IAC3E,MAAM,CAAC,CACL,OAAO,EAAE,mBAAmB,GAC3B,cAAc,CAAC,kBAAkB,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IACrD,MAAM,CAAC,IAAI,EAAE,6BAA6B,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAC3D,UAAU,CAAC,CAAC,QAAQ,EAAE,cAAc,EAClC,aAAa,EAAE,oBAAoB,CAAC,QAAQ,EAAE,cAAc,CAAC,GAC5D,2BAA2B,CAAC,QAAQ,CAAC,CAAC;CAC1C,CAAC"}