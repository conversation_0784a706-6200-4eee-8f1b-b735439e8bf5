{"version": 3, "sources": ["../../../src/utils/hooks/useOnResizeContent.tsx"], "sourcesContent": ["import { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport { useCallback } from \"react\";\nimport { useManagedRef } from \"./useManagedRef\";\n\nexport const useOnResizeContent = (callback: () => void) => {\n  const callbackRef = useCallbackRef(callback);\n\n  const refCallback = useCallback(\n    (el: HTMLElement) => {\n      const resizeObserver = new ResizeObserver(() => {\n        callbackRef();\n      });\n\n      const mutationObserver = new MutationObserver(() => {\n        callbackRef();\n      });\n\n      resizeObserver.observe(el);\n      mutationObserver.observe(el, {\n        childList: true,\n        subtree: true,\n        attributes: true,\n        characterData: true,\n      });\n\n      return () => {\n        resizeObserver.disconnect();\n        mutationObserver.disconnect();\n      };\n    },\n    [callbackRef],\n  );\n\n  return useManagedRef(refCallback);\n};\n"], "mappings": ";AAAA,SAAS,sBAAsB;AAC/B,SAAS,mBAAmB;AAC5B,SAAS,qBAAqB;AAEvB,IAAM,qBAAqB,CAAC,aAAyB;AAC1D,QAAM,cAAc,eAAe,QAAQ;AAE3C,QAAM,cAAc;AAAA,IAClB,CAAC,OAAoB;AACnB,YAAM,iBAAiB,IAAI,eAAe,MAAM;AAC9C,oBAAY;AAAA,MACd,CAAC;AAED,YAAM,mBAAmB,IAAI,iBAAiB,MAAM;AAClD,oBAAY;AAAA,MACd,CAAC;AAED,qBAAe,QAAQ,EAAE;AACzB,uBAAiB,QAAQ,IAAI;AAAA,QAC3B,WAAW;AAAA,QACX,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,eAAe;AAAA,MACjB,CAAC;AAED,aAAO,MAAM;AACX,uBAAe,WAAW;AAC1B,yBAAiB,WAAW;AAAA,MAC9B;AAAA,IACF;AAAA,IACA,CAAC,WAAW;AAAA,EACd;AAEA,SAAO,cAAc,WAAW;AAClC;", "names": []}