{"version": 3, "sources": ["../../../src/runtimes/local/useLocalRuntime.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useMemo, useState } from \"react\";\nimport type { ChatModelAdapter } from \"./ChatModelAdapter\";\nimport { LocalRuntimeCore } from \"./LocalRuntimeCore\";\nimport type { LocalRuntimeOptions } from \"./LocalRuntimeOptions\";\nimport { useRuntimeAdapters } from \"../adapters/RuntimeAdapterProvider\";\nimport { useRemoteThreadListRuntime } from \"../remote-thread-list/useRemoteThreadListRuntime\";\nimport { useCloudThreadListAdapter } from \"../remote-thread-list/adapter/cloud\";\nimport { AssistantRuntimeImpl } from \"../../internal\";\n\nexport const useLocalThreadRuntime = (\n  adapter: ChatModelAdapter,\n  { initialMessages, ...options }: LocalRuntimeOptions,\n) => {\n  const { modelContext, ...threadListAdapters } = useRuntimeAdapters() ?? {};\n  const opt = useMemo(\n    () => ({\n      ...options,\n      adapters: {\n        ...threadListAdapters,\n        ...options.adapters,\n        chatModel: adapter,\n      },\n    }),\n    [adapter, options, threadListAdapters],\n  );\n\n  const [runtime] = useState(() => new LocalRuntimeCore(opt, initialMessages));\n\n  useEffect(() => {\n    return () => {\n      runtime.threads.getMainThreadRuntimeCore().detach();\n    };\n  }, [runtime]);\n\n  useEffect(() => {\n    runtime.threads.getMainThreadRuntimeCore().__internal_setOptions(opt);\n    runtime.threads.getMainThreadRuntimeCore().__internal_load();\n  }, [runtime, opt]);\n\n  useEffect(() => {\n    if (!modelContext) return undefined;\n    return runtime.registerModelContextProvider(modelContext);\n  }, [modelContext, runtime]);\n\n  return useMemo(() => new AssistantRuntimeImpl(runtime), [runtime]);\n};\n\nexport const useLocalRuntime = (\n  adapter: ChatModelAdapter,\n  { cloud, ...options }: LocalRuntimeOptions = {},\n) => {\n  const cloudAdapter = useCloudThreadListAdapter({ cloud });\n  return useRemoteThreadListRuntime({\n    runtimeHook: function RuntimeHook() {\n      return useLocalThreadRuntime(adapter, options);\n    },\n    adapter: cloudAdapter,\n  });\n};\n"], "mappings": ";;;AAEA,SAAS,WAAW,SAAS,gBAAgB;AAE7C,SAAS,wBAAwB;AAEjC,SAAS,0BAA0B;AACnC,SAAS,kCAAkC;AAC3C,SAAS,iCAAiC;AAC1C,SAAS,4BAA4B;AAE9B,IAAM,wBAAwB,CACnC,SACA,EAAE,iBAAiB,GAAG,QAAQ,MAC3B;AACH,QAAM,EAAE,cAAc,GAAG,mBAAmB,IAAI,mBAAmB,KAAK,CAAC;AACzE,QAAM,MAAM;AAAA,IACV,OAAO;AAAA,MACL,GAAG;AAAA,MACH,UAAU;AAAA,QACR,GAAG;AAAA,QACH,GAAG,QAAQ;AAAA,QACX,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,CAAC,SAAS,SAAS,kBAAkB;AAAA,EACvC;AAEA,QAAM,CAAC,OAAO,IAAI,SAAS,MAAM,IAAI,iBAAiB,KAAK,eAAe,CAAC;AAE3E,YAAU,MAAM;AACd,WAAO,MAAM;AACX,cAAQ,QAAQ,yBAAyB,EAAE,OAAO;AAAA,IACpD;AAAA,EACF,GAAG,CAAC,OAAO,CAAC;AAEZ,YAAU,MAAM;AACd,YAAQ,QAAQ,yBAAyB,EAAE,sBAAsB,GAAG;AACpE,YAAQ,QAAQ,yBAAyB,EAAE,gBAAgB;AAAA,EAC7D,GAAG,CAAC,SAAS,GAAG,CAAC;AAEjB,YAAU,MAAM;AACd,QAAI,CAAC,aAAc,QAAO;AAC1B,WAAO,QAAQ,6BAA6B,YAAY;AAAA,EAC1D,GAAG,CAAC,cAAc,OAAO,CAAC;AAE1B,SAAO,QAAQ,MAAM,IAAI,qBAAqB,OAAO,GAAG,CAAC,OAAO,CAAC;AACnE;AAEO,IAAM,kBAAkB,CAC7B,SACA,EAAE,OAAO,GAAG,QAAQ,IAAyB,CAAC,MAC3C;AACH,QAAM,eAAe,0BAA0B,EAAE,MAAM,CAAC;AACxD,SAAO,2BAA2B;AAAA,IAChC,aAAa,SAAS,cAAc;AAClC,aAAO,sBAAsB,SAAS,OAAO;AAAA,IAC/C;AAAA,IACA,SAAS;AAAA,EACX,CAAC;AACH;", "names": []}