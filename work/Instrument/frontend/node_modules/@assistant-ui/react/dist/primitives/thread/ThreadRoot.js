"use client";

// src/primitives/thread/ThreadRoot.tsx
import { Primitive } from "@radix-ui/react-primitive";
import { forwardRef } from "react";
import { jsx } from "react/jsx-runtime";
var ThreadPrimitiveRoot = forwardRef((props, ref) => {
  return /* @__PURE__ */ jsx(Primitive.div, { ...props, ref });
});
ThreadPrimitiveRoot.displayName = "ThreadPrimitive.Root";
export {
  ThreadPrimitiveRoot
};
//# sourceMappingURL=ThreadRoot.js.map