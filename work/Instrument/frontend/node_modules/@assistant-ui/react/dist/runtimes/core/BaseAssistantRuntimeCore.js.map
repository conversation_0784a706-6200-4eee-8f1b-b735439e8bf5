{"version": 3, "sources": ["../../../src/runtimes/core/BaseAssistantRuntimeCore.tsx"], "sourcesContent": ["import { type ModelContextProvider } from \"../../model-context/ModelContextTypes\";\nimport type { Unsubscribe } from \"../../types/Unsubscribe\";\nimport type { AssistantRuntimeCore } from \"./AssistantRuntimeCore\";\nimport { CompositeContextProvider } from \"../../utils/CompositeContextProvider\";\nimport { ThreadListRuntimeCore } from \"./ThreadListRuntimeCore\";\n\nexport abstract class BaseAssistantRuntimeCore implements AssistantRuntimeCore {\n  protected readonly _contextProvider = new CompositeContextProvider();\n  public abstract get threads(): ThreadListRuntimeCore;\n\n  public registerModelContextProvider(\n    provider: ModelContextProvider,\n  ): Unsubscribe {\n    return this._contextProvider.registerModelContextProvider(provider);\n  }\n}\n"], "mappings": ";AAGA,SAAS,gCAAgC;AAGlC,IAAe,2BAAf,MAAwE;AAAA,EAC1D,mBAAmB,IAAI,yBAAyB;AAAA,EAG5D,6BACL,UACa;AACb,WAAO,KAAK,iBAAiB,6BAA6B,QAAQ;AAAA,EACpE;AACF;", "names": []}