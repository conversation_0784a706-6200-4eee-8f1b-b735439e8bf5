{"version": 3, "sources": ["../../../src/runtimes/remote-thread-list/RemoteThreadListHookInstanceManager.tsx"], "sourcesContent": ["/* eslint-disable react-hooks/rules-of-hooks */\n\"use client\";\n\nimport {\n  FC,\n  useCallback,\n  useRef,\n  useEffect,\n  memo,\n  useMemo,\n  PropsWithChildren,\n  ComponentType,\n} from \"react\";\nimport { UseBoundStore, StoreApi, create } from \"zustand\";\nimport { useAssistantRuntime } from \"../../context\";\nimport { ThreadListItemRuntimeProvider } from \"../../context/providers/ThreadListItemRuntimeProvider\";\nimport {\n  useThreadListItem,\n  useThreadListItemRuntime,\n} from \"../../context/react/ThreadListItemContext\";\nimport { ThreadRuntimeCore, ThreadRuntimeImpl } from \"../../internal\";\nimport { BaseSubscribable } from \"./BaseSubscribable\";\nimport { AssistantRuntime } from \"../../api\";\n\ntype RemoteThreadListHook = () => AssistantRuntime;\n\ntype RemoteThreadListHookInstance = {\n  runtime?: ThreadRuntimeCore;\n};\nexport class RemoteThreadListHookInstanceManager extends BaseSubscribable {\n  private useRuntimeHook: UseBoundStore<\n    StoreApi<{ useRuntime: RemoteThreadListHook }>\n  >;\n  private instances = new Map<string, RemoteThreadListHookInstance>();\n  private useAliveThreadsKeysChanged = create(() => ({}));\n\n  constructor(runtimeHook: RemoteThreadListHook) {\n    super();\n    this.useRuntimeHook = create(() => ({ useRuntime: runtimeHook }));\n  }\n\n  public startThreadRuntime(threadId: string) {\n    if (!this.instances.has(threadId)) {\n      this.instances.set(threadId, {});\n      this.useAliveThreadsKeysChanged.setState({}, true);\n    }\n\n    return new Promise<ThreadRuntimeCore>((resolve, reject) => {\n      const callback = () => {\n        const instance = this.instances.get(threadId);\n        if (!instance) {\n          dispose();\n          reject(new Error(\"Thread was deleted before runtime was started\"));\n        } else if (!instance.runtime) {\n          return; // misc update\n        } else {\n          dispose();\n          resolve(instance.runtime);\n        }\n      };\n      const dispose = this.subscribe(callback);\n      callback();\n    });\n  }\n\n  public getThreadRuntimeCore(threadId: string) {\n    const instance = this.instances.get(threadId);\n    if (!instance) return undefined;\n    return instance.runtime;\n  }\n\n  public stopThreadRuntime(threadId: string) {\n    this.instances.delete(threadId);\n    this.useAliveThreadsKeysChanged.setState({}, true);\n  }\n\n  public setRuntimeHook(newRuntimeHook: RemoteThreadListHook) {\n    const prevRuntimeHook = this.useRuntimeHook.getState().useRuntime;\n    if (prevRuntimeHook !== newRuntimeHook) {\n      this.useRuntimeHook.setState({ useRuntime: newRuntimeHook }, true);\n    }\n  }\n\n  private _InnerActiveThreadProvider: FC = () => {\n    const { id } = useThreadListItem();\n\n    const { useRuntime } = this.useRuntimeHook();\n    const runtime = useRuntime();\n\n    const threadBinding = (runtime.thread as ThreadRuntimeImpl)\n      .__internal_threadBinding;\n\n    const updateRuntime = useCallback(() => {\n      const aliveThread = this.instances.get(id);\n      if (!aliveThread)\n        throw new Error(\"Thread not found. This is a bug in assistant-ui.\");\n\n      aliveThread.runtime = threadBinding.getState();\n\n      if (isMounted) {\n        this._notifySubscribers();\n      }\n    }, [id, threadBinding]);\n\n    const isMounted = useRef(false);\n    if (!isMounted.current) {\n      updateRuntime();\n    }\n\n    useEffect(() => {\n      isMounted.current = true;\n      updateRuntime();\n      return threadBinding.outerSubscribe(updateRuntime);\n    }, [threadBinding, updateRuntime]);\n\n    // auto initialize thread\n    const threadListItemRuntime = useThreadListItemRuntime();\n    useEffect(() => {\n      return runtime.threads.main.unstable_on(\"initialize\", () => {\n        if (threadListItemRuntime.getState().status === \"new\") {\n          threadListItemRuntime.initialize();\n\n          // auto generate a title after first run\n          const dispose = runtime.thread.unstable_on(\"run-end\", () => {\n            dispose();\n\n            threadListItemRuntime.generateTitle();\n          });\n        }\n      });\n    }, [runtime, threadListItemRuntime]);\n\n    return null;\n  };\n\n  private _OuterActiveThreadProvider: FC<{\n    threadId: string;\n    provider: ComponentType<PropsWithChildren>;\n    // eslint-disable-next-line react/display-name\n  }> = memo(({ threadId, provider: Provider }) => {\n    const assistantRuntime = useAssistantRuntime();\n    const threadListItemRuntime = useMemo(\n      () => assistantRuntime.threads.getItemById(threadId),\n      [assistantRuntime, threadId],\n    );\n\n    return (\n      <ThreadListItemRuntimeProvider runtime={threadListItemRuntime}>\n        <Provider>\n          <this._InnerActiveThreadProvider />\n        </Provider>\n      </ThreadListItemRuntimeProvider>\n    );\n  });\n\n  public __internal_RenderThreadRuntimes: FC<{\n    provider: ComponentType<PropsWithChildren>;\n  }> = ({ provider }) => {\n    this.useAliveThreadsKeysChanged(); // trigger re-render on alive threads change\n\n    return Array.from(this.instances.keys()).map((threadId) => (\n      <this._OuterActiveThreadProvider\n        key={threadId}\n        threadId={threadId}\n        provider={provider}\n      />\n    ));\n  };\n}\n"], "mappings": ";;;AAGA;AAAA,EAEE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OAGK;AACP,SAAkC,cAAc;AAChD,SAAS,2BAA2B;AACpC,SAAS,qCAAqC;AAC9C;AAAA,EACE;AAAA,EACA;AAAA,OACK;AAEP,SAAS,wBAAwB;AAgIvB;AAxHH,IAAM,sCAAN,cAAkD,iBAAiB;AAAA,EAChE;AAAA,EAGA,YAAY,oBAAI,IAA0C;AAAA,EAC1D,6BAA6B,OAAO,OAAO,CAAC,EAAE;AAAA,EAEtD,YAAY,aAAmC;AAC7C,UAAM;AACN,SAAK,iBAAiB,OAAO,OAAO,EAAE,YAAY,YAAY,EAAE;AAAA,EAClE;AAAA,EAEO,mBAAmB,UAAkB;AAC1C,QAAI,CAAC,KAAK,UAAU,IAAI,QAAQ,GAAG;AACjC,WAAK,UAAU,IAAI,UAAU,CAAC,CAAC;AAC/B,WAAK,2BAA2B,SAAS,CAAC,GAAG,IAAI;AAAA,IACnD;AAEA,WAAO,IAAI,QAA2B,CAAC,SAAS,WAAW;AACzD,YAAM,WAAW,MAAM;AACrB,cAAM,WAAW,KAAK,UAAU,IAAI,QAAQ;AAC5C,YAAI,CAAC,UAAU;AACb,kBAAQ;AACR,iBAAO,IAAI,MAAM,+CAA+C,CAAC;AAAA,QACnE,WAAW,CAAC,SAAS,SAAS;AAC5B;AAAA,QACF,OAAO;AACL,kBAAQ;AACR,kBAAQ,SAAS,OAAO;AAAA,QAC1B;AAAA,MACF;AACA,YAAM,UAAU,KAAK,UAAU,QAAQ;AACvC,eAAS;AAAA,IACX,CAAC;AAAA,EACH;AAAA,EAEO,qBAAqB,UAAkB;AAC5C,UAAM,WAAW,KAAK,UAAU,IAAI,QAAQ;AAC5C,QAAI,CAAC,SAAU,QAAO;AACtB,WAAO,SAAS;AAAA,EAClB;AAAA,EAEO,kBAAkB,UAAkB;AACzC,SAAK,UAAU,OAAO,QAAQ;AAC9B,SAAK,2BAA2B,SAAS,CAAC,GAAG,IAAI;AAAA,EACnD;AAAA,EAEO,eAAe,gBAAsC;AAC1D,UAAM,kBAAkB,KAAK,eAAe,SAAS,EAAE;AACvD,QAAI,oBAAoB,gBAAgB;AACtC,WAAK,eAAe,SAAS,EAAE,YAAY,eAAe,GAAG,IAAI;AAAA,IACnE;AAAA,EACF;AAAA,EAEQ,6BAAiC,MAAM;AAC7C,UAAM,EAAE,GAAG,IAAI,kBAAkB;AAEjC,UAAM,EAAE,WAAW,IAAI,KAAK,eAAe;AAC3C,UAAM,UAAU,WAAW;AAE3B,UAAM,gBAAiB,QAAQ,OAC5B;AAEH,UAAM,gBAAgB,YAAY,MAAM;AACtC,YAAM,cAAc,KAAK,UAAU,IAAI,EAAE;AACzC,UAAI,CAAC;AACH,cAAM,IAAI,MAAM,kDAAkD;AAEpE,kBAAY,UAAU,cAAc,SAAS;AAE7C,UAAI,WAAW;AACb,aAAK,mBAAmB;AAAA,MAC1B;AAAA,IACF,GAAG,CAAC,IAAI,aAAa,CAAC;AAEtB,UAAM,YAAY,OAAO,KAAK;AAC9B,QAAI,CAAC,UAAU,SAAS;AACtB,oBAAc;AAAA,IAChB;AAEA,cAAU,MAAM;AACd,gBAAU,UAAU;AACpB,oBAAc;AACd,aAAO,cAAc,eAAe,aAAa;AAAA,IACnD,GAAG,CAAC,eAAe,aAAa,CAAC;AAGjC,UAAM,wBAAwB,yBAAyB;AACvD,cAAU,MAAM;AACd,aAAO,QAAQ,QAAQ,KAAK,YAAY,cAAc,MAAM;AAC1D,YAAI,sBAAsB,SAAS,EAAE,WAAW,OAAO;AACrD,gCAAsB,WAAW;AAGjC,gBAAM,UAAU,QAAQ,OAAO,YAAY,WAAW,MAAM;AAC1D,oBAAQ;AAER,kCAAsB,cAAc;AAAA,UACtC,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH,GAAG,CAAC,SAAS,qBAAqB,CAAC;AAEnC,WAAO;AAAA,EACT;AAAA,EAEQ,6BAIH,KAAK,CAAC,EAAE,UAAU,UAAU,SAAS,MAAM;AAC9C,UAAM,mBAAmB,oBAAoB;AAC7C,UAAM,wBAAwB;AAAA,MAC5B,MAAM,iBAAiB,QAAQ,YAAY,QAAQ;AAAA,MACnD,CAAC,kBAAkB,QAAQ;AAAA,IAC7B;AAEA,WACE,oBAAC,iCAA8B,SAAS,uBACtC,8BAAC,YACC,8BAAC,KAAK,4BAAL,EAAgC,GACnC,GACF;AAAA,EAEJ,CAAC;AAAA,EAEM,kCAEF,CAAC,EAAE,SAAS,MAAM;AACrB,SAAK,2BAA2B;AAEhC,WAAO,MAAM,KAAK,KAAK,UAAU,KAAK,CAAC,EAAE,IAAI,CAAC,aAC5C;AAAA,MAAC,KAAK;AAAA,MAAL;AAAA,QAEC;AAAA,QACA;AAAA;AAAA,MAFK;AAAA,IAGP,CACD;AAAA,EACH;AACF;", "names": []}