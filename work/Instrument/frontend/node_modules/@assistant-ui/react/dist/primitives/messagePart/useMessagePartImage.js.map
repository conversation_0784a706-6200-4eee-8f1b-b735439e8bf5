{"version": 3, "sources": ["../../../src/primitives/messagePart/useMessagePartImage.tsx"], "sourcesContent": ["\"use client\";\n\nimport { MessagePartState } from \"../../api/MessagePartRuntime\";\nimport { useMessagePart } from \"../../context/react/MessagePartContext\";\nimport { ImageMessagePart } from \"../../types\";\n\nexport const useMessagePartImage = () => {\n  const image = useMessagePart((c) => {\n    if (c.type !== \"image\")\n      throw new Error(\n        \"MessagePartImage can only be used inside image message parts.\",\n      );\n\n    return c as MessagePartState & ImageMessagePart;\n  });\n\n  return image;\n};\n"], "mappings": ";;;AAGA,SAAS,sBAAsB;AAGxB,IAAM,sBAAsB,MAAM;AACvC,QAAM,QAAQ,eAAe,CAAC,MAAM;AAClC,QAAI,EAAE,SAAS;AACb,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAEF,WAAO;AAAA,EACT,CAAC;AAED,SAAO;AACT;", "names": []}