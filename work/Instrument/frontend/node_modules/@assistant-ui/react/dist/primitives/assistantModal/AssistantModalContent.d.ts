import { ComponentPropsWithoutRef, ComponentRef } from "react";
import * as PopoverPrimitive from "@radix-ui/react-popover";
export declare namespace AssistantModalPrimitiveContent {
    type Element = ComponentRef<typeof PopoverPrimitive.Content>;
    type Props = ComponentPropsWithoutRef<typeof PopoverPrimitive.Content> & {
        portalProps?: ComponentPropsWithoutRef<typeof PopoverPrimitive.Portal> | undefined;
        dissmissOnInteractOutside?: boolean | undefined;
    };
}
export declare const AssistantModalPrimitiveContent: import("react").ForwardRefExoticComponent<Omit<PopoverPrimitive.PopoverContentProps & import("react").RefAttributes<HTMLDivElement>, "ref"> & {
    portalProps?: ComponentPropsWithoutRef<typeof PopoverPrimitive.Portal> | undefined;
    dissmissOnInteractOutside?: boolean | undefined;
} & import("react").RefAttributes<HTMLDivElement>>;
//# sourceMappingURL=AssistantModalContent.d.ts.map