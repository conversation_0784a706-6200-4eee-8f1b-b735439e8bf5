import { BaseAssistantRuntimeCore } from "../core/BaseAssistantRuntimeCore";
import { LocalRuntimeOptionsBase } from "./LocalRuntimeOptions";
import { LocalThreadListRuntimeCore } from "./LocalThreadListRuntimeCore";
import { ThreadMessageLike } from "../external-store";
export declare class LocalRuntimeCore extends BaseAssistantRuntimeCore {
    readonly threads: LocalThreadListRuntimeCore;
    readonly Provider: undefined;
    private _options;
    constructor(options: LocalRuntimeOptionsBase, initialMessages: readonly ThreadMessageLike[] | undefined);
}
//# sourceMappingURL=LocalRuntimeCore.d.ts.map