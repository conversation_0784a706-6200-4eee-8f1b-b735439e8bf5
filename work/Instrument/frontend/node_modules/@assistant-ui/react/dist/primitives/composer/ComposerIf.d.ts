import type { FC, PropsWithChildren } from "react";
import type { RequireAtLeastOne } from "../../utils/RequireAtLeastOne";
type ComposerIfFilters = {
    editing: boolean | undefined;
};
export type UseComposerIfProps = RequireAtLeastOne<ComposerIfFilters>;
export declare namespace ComposerPrimitiveIf {
    type Props = PropsWithChildren<UseComposerIfProps>;
}
export declare const ComposerPrimitiveIf: FC<ComposerPrimitiveIf.Props>;
export {};
//# sourceMappingURL=ComposerIf.d.ts.map