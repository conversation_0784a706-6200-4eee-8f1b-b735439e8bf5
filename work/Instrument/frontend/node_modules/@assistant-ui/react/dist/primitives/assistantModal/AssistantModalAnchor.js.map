{"version": 3, "sources": ["../../../src/primitives/assistantModal/AssistantModalAnchor.tsx"], "sourcesContent": ["\"use client\";\n\nimport { ComponentPropsWithoutRef, ComponentRef, forwardRef } from \"react\";\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\";\nimport { ScopedProps, usePopoverScope } from \"./scope\";\n\nexport namespace AssistantModalPrimitiveAnchor {\n  export type Element = ComponentRef<typeof PopoverPrimitive.Anchor>;\n  export type Props = ComponentPropsWithoutRef<typeof PopoverPrimitive.Anchor>;\n}\n\nexport const AssistantModalPrimitiveAnchor = forwardRef<\n  AssistantModalPrimitiveAnchor.Element,\n  AssistantModalPrimitiveAnchor.Props\n>(\n  (\n    {\n      __scopeAssistantModal,\n      ...rest\n    }: ScopedProps<AssistantModalPrimitiveAnchor.Props>,\n    ref,\n  ) => {\n    const scope = usePopoverScope(__scopeAssistantModal);\n\n    return <PopoverPrimitive.Anchor {...scope} {...rest} ref={ref} />;\n  },\n);\nAssistantModalPrimitiveAnchor.displayName = \"AssistantModalPrimitive.Anchor\";\n"], "mappings": ";;;AAEA,SAAiD,kBAAkB;AACnE,YAAY,sBAAsB;AAClC,SAAsB,uBAAuB;AAoBlC;AAbJ,IAAM,gCAAgC;AAAA,EAI3C,CACE;AAAA,IACE;AAAA,IACA,GAAG;AAAA,EACL,GACA,QACG;AACH,UAAM,QAAQ,gBAAgB,qBAAqB;AAEnD,WAAO,oBAAkB,yBAAjB,EAAyB,GAAG,OAAQ,GAAG,MAAM,KAAU;AAAA,EACjE;AACF;AACA,8BAA8B,cAAc;", "names": []}