{"version": 3, "sources": ["../../../src/runtimes/external-store/getExternalStoreMessage.tsx"], "sourcesContent": ["import { ThreadState } from \"../../api\";\nimport { ThreadMessage } from \"../../types\";\n\nexport const symbolInnerMessage = Symbol(\"innerMessage\");\nconst symbolInnerMessages = Symbol(\"innerMessages\");\n\ntype WithInnerMessages<T> = {\n  [symbolInnerMessage]?: T | T[];\n  [symbolInnerMessages]?: T[];\n};\n\n/**\n * @deprecated Use `getExternalStoreMessages` (plural) instead. This function will be removed in 0.8.0.\n */\nexport const getExternalStoreMessage = <T,>(input: ThreadMessage) => {\n  const withInnerMessages = input as WithInnerMessages<T>;\n  return withInnerMessages[symbolInnerMessage];\n};\n\nconst EMPTY_ARRAY: never[] = [];\n\nexport const getExternalStoreMessages = <T,>(\n  input: ThreadState | ThreadMessage | ThreadMessage[\"content\"][number],\n) => {\n  // TODO temp until 0.8.0 (migrate useExternalStoreRuntime to always set an array)\n\n  const container = (\n    \"messages\" in input ? input.messages : input\n  ) as WithInnerMessages<T>;\n  const value = container[symbolInnerMessages] || container[symbolInnerMessage];\n  if (!value) return EMPTY_ARRAY;\n  if (Array.isArray(value)) {\n    return value;\n  }\n  container[symbolInnerMessages] = [value];\n  return container[symbolInnerMessages];\n};\n"], "mappings": ";AAGO,IAAM,qBAAqB,OAAO,cAAc;AACvD,IAAM,sBAAsB,OAAO,eAAe;AAU3C,IAAM,0BAA0B,CAAK,UAAyB;AACnE,QAAM,oBAAoB;AAC1B,SAAO,kBAAkB,kBAAkB;AAC7C;AAEA,IAAM,cAAuB,CAAC;AAEvB,IAAM,2BAA2B,CACtC,UACG;AAGH,QAAM,YACJ,cAAc,QAAQ,MAAM,WAAW;AAEzC,QAAM,QAAQ,UAAU,mBAAmB,KAAK,UAAU,kBAAkB;AAC5E,MAAI,CAAC,MAAO,QAAO;AACnB,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,WAAO;AAAA,EACT;AACA,YAAU,mBAAmB,IAAI,CAAC,KAAK;AACvC,SAAO,UAAU,mBAAmB;AACtC;", "names": []}