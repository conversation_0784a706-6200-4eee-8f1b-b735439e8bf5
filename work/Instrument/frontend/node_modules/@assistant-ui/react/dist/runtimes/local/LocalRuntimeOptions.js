// src/runtimes/local/LocalRuntimeOptions.tsx
var splitLocalRuntimeOptions = (options) => {
  const {
    cloud,
    initialMessages,
    maxSteps,
    adapters,
    unstable_humanToolNames,
    ...rest
  } = options;
  return {
    localRuntimeOptions: {
      cloud,
      initialMessages,
      maxSteps,
      adapters,
      unstable_humanToolNames
    },
    otherOptions: rest
  };
};
export {
  splitLocalRuntimeOptions
};
//# sourceMappingURL=LocalRuntimeOptions.js.map