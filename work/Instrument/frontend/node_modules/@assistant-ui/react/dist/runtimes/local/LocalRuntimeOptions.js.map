{"version": 3, "sources": ["../../../src/runtimes/local/LocalRuntimeOptions.tsx"], "sourcesContent": ["import { ThreadHistoryAdapter } from \"../adapters/thread-history/ThreadHistoryAdapter\";\nimport { AttachmentAdapter } from \"../adapters/attachment/AttachmentAdapter\";\nimport { ThreadMessageLike } from \"../external-store\";\nimport { FeedbackAdapter } from \"../adapters/feedback/FeedbackAdapter\";\nimport { SpeechSynthesisAdapter } from \"../adapters/speech/SpeechAdapterTypes\";\nimport { ChatModelAdapter } from \"./ChatModelAdapter\";\nimport { AssistantCloud } from \"assistant-cloud\";\nimport { SuggestionAdapter } from \"../adapters\";\n\nexport type LocalRuntimeOptionsBase = {\n  maxSteps?: number | undefined;\n  adapters: {\n    chatModel: ChatModelAdapter;\n    history?: ThreadHistoryAdapter | undefined;\n    attachments?: AttachmentAdapter | undefined;\n    speech?: SpeechSynthesisAdapter | undefined;\n    feedback?: FeedbackAdapter | undefined;\n    suggestion?: SuggestionAdapter | undefined;\n  };\n\n  /**\n   * Names of tools that are allowed to interrupt the run in order to wait for human/external approval.\n   */\n  unstable_humanToolNames?: string[] | undefined;\n};\n\n// TODO align LocalRuntimeOptions with LocalRuntimeOptionsBase\nexport type LocalRuntimeOptions = Omit<LocalRuntimeOptionsBase, \"adapters\"> & {\n  cloud?: AssistantCloud | undefined;\n  initialMessages?: readonly ThreadMessageLike[] | undefined;\n  adapters?: Omit<LocalRuntimeOptionsBase[\"adapters\"], \"chatModel\"> | undefined;\n};\n\nexport const splitLocalRuntimeOptions = <T extends LocalRuntimeOptions>(\n  options: T,\n) => {\n  const {\n    cloud,\n    initialMessages,\n    maxSteps,\n    adapters,\n    unstable_humanToolNames,\n    ...rest\n  } = options;\n\n  return {\n    localRuntimeOptions: {\n      cloud,\n      initialMessages,\n      maxSteps,\n      adapters,\n      unstable_humanToolNames,\n    },\n    otherOptions: rest,\n  };\n};\n"], "mappings": ";AAiCO,IAAM,2BAA2B,CACtC,YACG;AACH,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AAEJ,SAAO;AAAA,IACL,qBAAqB;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,cAAc;AAAA,EAChB;AACF;", "names": []}