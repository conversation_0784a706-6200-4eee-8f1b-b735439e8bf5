{"version": 3, "sources": ["../../../src/primitives/assistantModal/AssistantModalContent.tsx"], "sourcesContent": ["\"use client\";\n\nimport { ComponentPropsWithoutRef, ComponentRef, forwardRef } from \"react\";\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\";\nimport { ScopedProps, usePopoverScope } from \"./scope\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\n\nexport namespace AssistantModalPrimitiveContent {\n  export type Element = ComponentRef<typeof PopoverPrimitive.Content>;\n  export type Props = ComponentPropsWithoutRef<\n    typeof PopoverPrimitive.Content\n  > & {\n    portalProps?:\n      | ComponentPropsWithoutRef<typeof PopoverPrimitive.Portal>\n      | undefined;\n    dissmissOnInteractOutside?: boolean | undefined;\n  };\n}\n\nexport const AssistantModalPrimitiveContent = forwardRef<\n  AssistantModalPrimitiveContent.Element,\n  AssistantModalPrimitiveContent.Props\n>(\n  (\n    {\n      __scopeAssistantModal,\n      side,\n      align,\n      onInteractOutside,\n      dissmissOnInteractOutside = false,\n      portalProps,\n      ...props\n    }: ScopedProps<AssistantModalPrimitiveContent.Props>,\n    forwardedRef,\n  ) => {\n    const scope = usePopoverScope(__scopeAssistantModal);\n\n    return (\n      <PopoverPrimitive.Portal {...scope} {...portalProps}>\n        <PopoverPrimitive.Content\n          {...scope}\n          {...props}\n          ref={forwardedRef}\n          side={side ?? \"top\"}\n          align={align ?? \"end\"}\n          onInteractOutside={composeEventHandlers(\n            onInteractOutside,\n            dissmissOnInteractOutside ? undefined : (e) => e.preventDefault(),\n          )}\n        />\n      </PopoverPrimitive.Portal>\n    );\n  },\n);\n\nAssistantModalPrimitiveContent.displayName = \"AssistantModalPrimitive.Content\";\n"], "mappings": ";;;AAEA,SAAiD,kBAAkB;AACnE,YAAY,sBAAsB;AAClC,SAAsB,uBAAuB;AAC7C,SAAS,4BAA4B;AAkC7B;AApBD,IAAM,iCAAiC;AAAA,EAI5C,CACE;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,4BAA4B;AAAA,IAC5B;AAAA,IACA,GAAG;AAAA,EACL,GACA,iBACG;AACH,UAAM,QAAQ,gBAAgB,qBAAqB;AAEnD,WACE,oBAAkB,yBAAjB,EAAyB,GAAG,OAAQ,GAAG,aACtC;AAAA,MAAkB;AAAA,MAAjB;AAAA,QACE,GAAG;AAAA,QACH,GAAG;AAAA,QACJ,KAAK;AAAA,QACL,MAAM,QAAQ;AAAA,QACd,OAAO,SAAS;AAAA,QAChB,mBAAmB;AAAA,UACjB;AAAA,UACA,4BAA4B,SAAY,CAAC,MAAM,EAAE,eAAe;AAAA,QAClE;AAAA;AAAA,IACF,GACF;AAAA,EAEJ;AACF;AAEA,+BAA+B,cAAc;", "names": []}