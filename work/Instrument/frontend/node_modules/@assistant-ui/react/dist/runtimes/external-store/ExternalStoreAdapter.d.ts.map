{"version": 3, "file": "ExternalStoreAdapter.d.ts", "sourceRoot": "", "sources": ["../../../src/runtimes/external-store/ExternalStoreAdapter.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,aAAa,EAAE,aAAa,EAAE,MAAM,aAAa,CAAC;AAC3D,OAAO,EAAE,iBAAiB,EAAE,MAAM,wBAAwB,CAAC;AAC3D,OAAO,EACL,oBAAoB,EACpB,cAAc,EACd,gBAAgB,EACjB,MAAM,2BAA2B,CAAC;AACnC,OAAO,EAAE,eAAe,EAAE,MAAM,sCAAsC,CAAC;AACvE,OAAO,EAAE,sBAAsB,EAAE,MAAM,uCAAuC,CAAC;AAC/E,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AACxD,OAAO,EAAE,yBAAyB,EAAE,MAAM,4BAA4B,CAAC;AAEvE,MAAM,MAAM,uBAAuB,CAAC,MAAM,SAAS,SAAS,GAAG,UAAU,IAAI;IAC3E,MAAM,EAAE,MAAM,CAAC;IACf,QAAQ,EAAE,MAAM,CAAC;IACjB,KAAK,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;CAC5B,CAAC;AAEF,MAAM,MAAM,8BAA8B,GAAG;IAC3C;;OAEG;IACH,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC9B,SAAS,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAChC,OAAO,CAAC,EAAE,SAAS,uBAAuB,CAAC,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC;IACpE,eAAe,CAAC,EAAE,SAAS,uBAAuB,CAAC,UAAU,CAAC,EAAE,GAAG,SAAS,CAAC;IAC7E;;OAEG;IACH,mBAAmB,CAAC,EAAE,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,SAAS,CAAC;IAC/D;;OAEG;IACH,gBAAgB,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,MAAM,KAAK,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,SAAS,CAAC;IAC5E,QAAQ,CAAC,EAAE,CACT,QAAQ,EAAE,MAAM,EAChB,QAAQ,EAAE,MAAM,KACb,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,SAAS,CAAC;IACxC,SAAS,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,MAAM,KAAK,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,SAAS,CAAC;IACrE,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,MAAM,KAAK,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,SAAS,CAAC;IACvE,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,MAAM,KAAK,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,SAAS,CAAC;CACrE,CAAC;AAEF,MAAM,MAAM,6BAA6B,CAAC,CAAC,IAAI,CAC7C,OAAO,EAAE,CAAC,EACV,GAAG,EAAE,MAAM,KACR,iBAAiB,CAAC;AAEvB,KAAK,oCAAoC,CAAC,CAAC,IAAI;IAC7C,cAAc,EAAE,6BAA6B,CAAC,CAAC,CAAC,CAAC;CAClD,CAAC;AAEF,KAAK,wBAAwB,CAAC,CAAC,IAAI;IACjC,UAAU,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IACjC,SAAS,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAChC,SAAS,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAChC,QAAQ,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC;IACxB,iBAAiB,CAAC,EAAE,yBAAyB,CAAC;IAC9C,WAAW,CAAC,EAAE,SAAS,gBAAgB,EAAE,GAAG,SAAS,CAAC;IACtD,MAAM,CAAC,EAAE,OAAO,CAAC;IAEjB,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,GAAG,SAAS,CAAC;IACpD,KAAK,EAAE,CAAC,OAAO,EAAE,aAAa,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;IACjD,MAAM,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,aAAa,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,SAAS,CAAC;IACjE,QAAQ,CAAC,EACP,CAAC,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI,EAAE,MAAM,EAAE,cAAc,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC,GAClE,SAAS,CAAC;IACd,QAAQ,CAAC,EAAE,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,SAAS,CAAC;IAC7C,eAAe,CAAC,EACZ,CAAC,CAAC,OAAO,EAAE,oBAAoB,KAAK,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GACzD,SAAS,CAAC;IACd,cAAc,CAAC,EAAE,6BAA6B,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;IAC9D,QAAQ,CAAC,EACL;QACE,WAAW,CAAC,EAAE,iBAAiB,GAAG,SAAS,CAAC;QAC5C,MAAM,CAAC,EAAE,sBAAsB,GAAG,SAAS,CAAC;QAC5C,QAAQ,CAAC,EAAE,eAAe,GAAG,SAAS,CAAC;QACvC;;WAEG;QACH,UAAU,CAAC,EAAE,8BAA8B,GAAG,SAAS,CAAC;KACzD,GACD,SAAS,CAAC;IACd,qBAAqB,CAAC,EAClB;QACE,IAAI,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;KAC5B,GACD,SAAS,CAAC;CACf,CAAC;AAEF,MAAM,MAAM,oBAAoB,CAAC,CAAC,GAAG,aAAa,IAChD,wBAAwB,CAAC,CAAC,CAAC,GACzB,CAAC,CAAC,SAAS,aAAa,GACpB,MAAM,GACN,oCAAoC,CAAC,CAAC,CAAC,CAAC,CAAC"}