{"version": 3, "sources": ["../../../src/primitives/composer/ComposerInput.tsx"], "sourcesContent": ["\"use client\";\n\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport {\n  ClipboardEvent,\n  type KeyboardEvent,\n  forwardRef,\n  useCallback,\n  useEffect,\n  useRef,\n} from \"react\";\nimport TextareaAutosize, {\n  type TextareaAutosizeProps,\n} from \"react-textarea-autosize\";\nimport {\n  useComposer,\n  useComposerRuntime,\n} from \"../../context/react/ComposerContext\";\nimport { useThread, useThreadRuntime } from \"../../context/react/ThreadContext\";\nimport { useEscapeKeydown } from \"@radix-ui/react-use-escape-keydown\";\nimport { useOnScrollToBottom } from \"../../utils/hooks/useOnScrollToBottom\";\nimport { useThreadListItemRuntime } from \"../../context/react/ThreadListItemContext\";\n\nexport namespace ComposerPrimitiveInput {\n  export type Element = HTMLTextAreaElement;\n  export type Props = TextareaAutosizeProps & {\n    /**\n     * Whether to render as a child component using Slot.\n     * When true, the component will merge its props with its child.\n     */\n    asChild?: boolean | undefined;\n    /**\n     * Whether to submit the message when Enter is pressed (without Shift).\n     * @default true\n     */\n    submitOnEnter?: boolean | undefined;\n    /**\n     * Whether to cancel message composition when Escape is pressed.\n     * @default true\n     */\n    cancelOnEscape?: boolean | undefined;\n    /**\n     * Whether to automatically focus the input when a new run starts.\n     * @default true\n     */\n    unstable_focusOnRunStart?: boolean | undefined;\n    /**\n     * Whether to automatically focus the input when scrolling to bottom.\n     * @default true\n     */\n    unstable_focusOnScrollToBottom?: boolean | undefined;\n    /**\n     * Whether to automatically focus the input when switching threads.\n     * @default true\n     */\n    unstable_focusOnThreadSwitched?: boolean | undefined;\n    /**\n     * Whether to automatically add pasted files as attachments.\n     * @default true\n     */\n    addAttachmentOnPaste?: boolean | undefined;\n  };\n}\n\n/**\n * A text input component for composing messages.\n *\n * This component provides a rich text input experience with automatic resizing,\n * keyboard shortcuts, file paste support, and intelligent focus management.\n * It integrates with the composer context to manage message state and submission.\n *\n * @example\n * ```tsx\n * <ComposerPrimitive.Input\n *   placeholder=\"Type your message...\"\n *   submitOnEnter={true}\n *   addAttachmentOnPaste={true}\n * />\n * ```\n */\nexport const ComposerPrimitiveInput = forwardRef<\n  ComposerPrimitiveInput.Element,\n  ComposerPrimitiveInput.Props\n>(\n  (\n    {\n      autoFocus = false,\n      asChild,\n      disabled: disabledProp,\n      onChange,\n      onKeyDown,\n      onPaste,\n      submitOnEnter = true,\n      cancelOnEscape = true,\n      unstable_focusOnRunStart = true,\n      unstable_focusOnScrollToBottom = true,\n      unstable_focusOnThreadSwitched = true,\n      addAttachmentOnPaste = true,\n      ...rest\n    },\n    forwardedRef,\n  ) => {\n    const threadListItemRuntime = useThreadListItemRuntime();\n    const threadRuntime = useThreadRuntime();\n    const composerRuntime = useComposerRuntime();\n\n    const value = useComposer((c) => {\n      if (!c.isEditing) return \"\";\n      return c.text;\n    });\n\n    const Component = asChild ? Slot : TextareaAutosize;\n\n    const isDisabled = Boolean(useThread((t) => t.isDisabled) || disabledProp);\n    const textareaRef = useRef<HTMLTextAreaElement>(null);\n    const ref = useComposedRefs(forwardedRef, textareaRef);\n\n    useEscapeKeydown((e) => {\n      if (!cancelOnEscape) return;\n\n      if (composerRuntime.getState().canCancel) {\n        composerRuntime.cancel();\n        e.preventDefault();\n      }\n    });\n\n    const handleKeyPress = (e: KeyboardEvent) => {\n      if (isDisabled || !submitOnEnter) return;\n\n      // ignore IME composition events\n      if (e.nativeEvent.isComposing) return;\n\n      if (e.key === \"Enter\" && e.shiftKey === false) {\n        const { isRunning } = threadRuntime.getState();\n\n        if (!isRunning) {\n          e.preventDefault();\n\n          textareaRef.current?.closest(\"form\")?.requestSubmit();\n        }\n      }\n    };\n\n    const handlePaste = async (e: ClipboardEvent<HTMLTextAreaElement>) => {\n      if (!addAttachmentOnPaste) return;\n      const threadCapabilities = threadRuntime.getState().capabilities;\n      const files = Array.from(e.clipboardData?.files || []);\n\n      if (threadCapabilities.attachments && files.length > 0) {\n        try {\n          e.preventDefault();\n          await Promise.all(\n            files.map((file) => composerRuntime.addAttachment(file)),\n          );\n        } catch (error) {\n          console.error(\"Error adding attachment:\", error);\n        }\n      }\n    };\n\n    const autoFocusEnabled = autoFocus && !isDisabled;\n    const focus = useCallback(() => {\n      const textarea = textareaRef.current;\n      if (!textarea || !autoFocusEnabled) return;\n\n      textarea.focus({ preventScroll: true });\n      textarea.setSelectionRange(textarea.value.length, textarea.value.length);\n    }, [autoFocusEnabled]);\n\n    useEffect(() => focus(), [focus]);\n\n    useOnScrollToBottom(() => {\n      if (composerRuntime.type === \"thread\" && unstable_focusOnScrollToBottom) {\n        focus();\n      }\n    });\n\n    useEffect(() => {\n      if (composerRuntime.type !== \"thread\" || !unstable_focusOnRunStart)\n        return undefined;\n\n      return threadRuntime.unstable_on(\"run-start\", focus);\n    }, [unstable_focusOnRunStart, focus, composerRuntime, threadRuntime]);\n\n    useEffect(() => {\n      if (composerRuntime.type !== \"thread\" || !unstable_focusOnThreadSwitched)\n        return undefined;\n\n      return threadListItemRuntime.unstable_on(\"switched-to\", focus);\n    }, [\n      unstable_focusOnThreadSwitched,\n      focus,\n      composerRuntime,\n      threadListItemRuntime,\n    ]);\n\n    return (\n      <Component\n        name=\"input\"\n        value={value}\n        {...rest}\n        ref={ref as React.ForwardedRef<HTMLTextAreaElement>}\n        disabled={isDisabled}\n        onChange={composeEventHandlers(onChange, (e) => {\n          if (!composerRuntime.getState().isEditing) return;\n          return composerRuntime.setText(e.target.value);\n        })}\n        onKeyDown={composeEventHandlers(onKeyDown, handleKeyPress)}\n        onPaste={composeEventHandlers(onPaste, handlePaste)}\n      />\n    );\n  },\n);\n\nComposerPrimitiveInput.displayName = \"ComposerPrimitive.Input\";\n"], "mappings": ";;;AAEA,SAAS,4BAA4B;AACrC,SAAS,uBAAuB;AAChC,SAAS,YAAY;AACrB;AAAA,EAGE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP,OAAO,sBAEA;AACP;AAAA,EACE;AAAA,EACA;AAAA,OACK;AACP,SAAS,WAAW,wBAAwB;AAC5C,SAAS,wBAAwB;AACjC,SAAS,2BAA2B;AACpC,SAAS,gCAAgC;AAgLnC;AArHC,IAAM,yBAAyB;AAAA,EAIpC,CACE;AAAA,IACE,YAAY;AAAA,IACZ;AAAA,IACA,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,2BAA2B;AAAA,IAC3B,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,uBAAuB;AAAA,IACvB,GAAG;AAAA,EACL,GACA,iBACG;AACH,UAAM,wBAAwB,yBAAyB;AACvD,UAAM,gBAAgB,iBAAiB;AACvC,UAAM,kBAAkB,mBAAmB;AAE3C,UAAM,QAAQ,YAAY,CAAC,MAAM;AAC/B,UAAI,CAAC,EAAE,UAAW,QAAO;AACzB,aAAO,EAAE;AAAA,IACX,CAAC;AAED,UAAM,YAAY,UAAU,OAAO;AAEnC,UAAM,aAAa,QAAQ,UAAU,CAAC,MAAM,EAAE,UAAU,KAAK,YAAY;AACzE,UAAM,cAAc,OAA4B,IAAI;AACpD,UAAM,MAAM,gBAAgB,cAAc,WAAW;AAErD,qBAAiB,CAAC,MAAM;AACtB,UAAI,CAAC,eAAgB;AAErB,UAAI,gBAAgB,SAAS,EAAE,WAAW;AACxC,wBAAgB,OAAO;AACvB,UAAE,eAAe;AAAA,MACnB;AAAA,IACF,CAAC;AAED,UAAM,iBAAiB,CAAC,MAAqB;AAC3C,UAAI,cAAc,CAAC,cAAe;AAGlC,UAAI,EAAE,YAAY,YAAa;AAE/B,UAAI,EAAE,QAAQ,WAAW,EAAE,aAAa,OAAO;AAC7C,cAAM,EAAE,UAAU,IAAI,cAAc,SAAS;AAE7C,YAAI,CAAC,WAAW;AACd,YAAE,eAAe;AAEjB,sBAAY,SAAS,QAAQ,MAAM,GAAG,cAAc;AAAA,QACtD;AAAA,MACF;AAAA,IACF;AAEA,UAAM,cAAc,OAAO,MAA2C;AACpE,UAAI,CAAC,qBAAsB;AAC3B,YAAM,qBAAqB,cAAc,SAAS,EAAE;AACpD,YAAM,QAAQ,MAAM,KAAK,EAAE,eAAe,SAAS,CAAC,CAAC;AAErD,UAAI,mBAAmB,eAAe,MAAM,SAAS,GAAG;AACtD,YAAI;AACF,YAAE,eAAe;AACjB,gBAAM,QAAQ;AAAA,YACZ,MAAM,IAAI,CAAC,SAAS,gBAAgB,cAAc,IAAI,CAAC;AAAA,UACzD;AAAA,QACF,SAAS,OAAO;AACd,kBAAQ,MAAM,4BAA4B,KAAK;AAAA,QACjD;AAAA,MACF;AAAA,IACF;AAEA,UAAM,mBAAmB,aAAa,CAAC;AACvC,UAAM,QAAQ,YAAY,MAAM;AAC9B,YAAM,WAAW,YAAY;AAC7B,UAAI,CAAC,YAAY,CAAC,iBAAkB;AAEpC,eAAS,MAAM,EAAE,eAAe,KAAK,CAAC;AACtC,eAAS,kBAAkB,SAAS,MAAM,QAAQ,SAAS,MAAM,MAAM;AAAA,IACzE,GAAG,CAAC,gBAAgB,CAAC;AAErB,cAAU,MAAM,MAAM,GAAG,CAAC,KAAK,CAAC;AAEhC,wBAAoB,MAAM;AACxB,UAAI,gBAAgB,SAAS,YAAY,gCAAgC;AACvE,cAAM;AAAA,MACR;AAAA,IACF,CAAC;AAED,cAAU,MAAM;AACd,UAAI,gBAAgB,SAAS,YAAY,CAAC;AACxC,eAAO;AAET,aAAO,cAAc,YAAY,aAAa,KAAK;AAAA,IACrD,GAAG,CAAC,0BAA0B,OAAO,iBAAiB,aAAa,CAAC;AAEpE,cAAU,MAAM;AACd,UAAI,gBAAgB,SAAS,YAAY,CAAC;AACxC,eAAO;AAET,aAAO,sBAAsB,YAAY,eAAe,KAAK;AAAA,IAC/D,GAAG;AAAA,MACD;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAED,WACE;AAAA,MAAC;AAAA;AAAA,QACC,MAAK;AAAA,QACL;AAAA,QACC,GAAG;AAAA,QACJ;AAAA,QACA,UAAU;AAAA,QACV,UAAU,qBAAqB,UAAU,CAAC,MAAM;AAC9C,cAAI,CAAC,gBAAgB,SAAS,EAAE,UAAW;AAC3C,iBAAO,gBAAgB,QAAQ,EAAE,OAAO,KAAK;AAAA,QAC/C,CAAC;AAAA,QACD,WAAW,qBAAqB,WAAW,cAAc;AAAA,QACzD,SAAS,qBAAqB,SAAS,WAAW;AAAA;AAAA,IACpD;AAAA,EAEJ;AACF;AAEA,uBAAuB,cAAc;", "names": []}