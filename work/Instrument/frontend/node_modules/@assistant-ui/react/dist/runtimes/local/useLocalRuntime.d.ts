import type { ChatModelAdapter } from "./ChatModelAdapter";
import type { LocalRuntimeOptions } from "./LocalRuntimeOptions";
import { AssistantRuntimeImpl } from "../../internal";
export declare const useLocalThreadRuntime: (adapter: ChatModelAdapter, { initialMessages, ...options }: LocalRuntimeOptions) => AssistantRuntimeImpl;
export declare const useLocalRuntime: (adapter: ChatModelAdapter, { cloud, ...options }?: LocalRuntimeOptions) => AssistantRuntimeImpl;
//# sourceMappingURL=useLocalRuntime.d.ts.map