{"version": 3, "sources": ["../../../src/runtimes/composer/DefaultEditComposerRuntimeCore.tsx"], "sourcesContent": ["import { AppendMessage, ThreadMessage } from \"../../types\";\nimport { getThreadMessageText } from \"../../utils/getThreadMessageText\";\nimport { AttachmentAdapter } from \"../adapters/attachment\";\nimport { ThreadRuntimeCore } from \"../core/ThreadRuntimeCore\";\nimport { BaseComposerRuntimeCore } from \"./BaseComposerRuntimeCore\";\n\nexport class DefaultEditComposerRuntimeCore extends BaseComposerRuntimeCore {\n  public get canCancel() {\n    return true;\n  }\n\n  protected getAttachmentAdapter() {\n    return this.runtime.adapters?.attachments;\n  }\n\n  private _nonTextParts;\n  private _previousText;\n  private _parentId;\n  private _sourceId;\n  constructor(\n    private runtime: ThreadRuntimeCore & {\n      adapters?: { attachments?: AttachmentAdapter | undefined } | undefined;\n    },\n    private endEditCallback: () => void,\n    { parentId, message }: { parentId: string | null; message: ThreadMessage },\n  ) {\n    super();\n    this._parentId = parentId;\n    this._sourceId = message.id;\n    this._previousText = getThreadMessageText(message);\n    this.setText(this._previousText);\n\n    this.setRole(message.role);\n    this.setAttachments(message.attachments ?? []);\n\n    this._nonTextParts = message.content.filter((part) => part.type !== \"text\");\n\n    // Use the runConfig from the regular (non-edit) composer as the initial runConfig for the edit composer\n    this.setRunConfig({ ...runtime.composer.runConfig });\n  }\n\n  public async handleSend(\n    message: Omit<AppendMessage, \"parentId\" | \"sourceId\">,\n  ) {\n    const text = getThreadMessageText(message as AppendMessage);\n    if (text !== this._previousText) {\n      this.runtime.append({\n        ...message,\n        content: [...message.content, ...this._nonTextParts] as any,\n        parentId: this._parentId,\n        sourceId: this._sourceId,\n      });\n    }\n\n    this.handleCancel();\n  }\n\n  public handleCancel() {\n    this.endEditCallback();\n    this._notifySubscribers();\n  }\n}\n"], "mappings": ";AACA,SAAS,4BAA4B;AAGrC,SAAS,+BAA+B;AAEjC,IAAM,iCAAN,cAA6C,wBAAwB;AAAA,EAa1E,YACU,SAGA,iBACR,EAAE,UAAU,QAAQ,GACpB;AACA,UAAM;AANE;AAGA;AAIR,SAAK,YAAY;AACjB,SAAK,YAAY,QAAQ;AACzB,SAAK,gBAAgB,qBAAqB,OAAO;AACjD,SAAK,QAAQ,KAAK,aAAa;AAE/B,SAAK,QAAQ,QAAQ,IAAI;AACzB,SAAK,eAAe,QAAQ,eAAe,CAAC,CAAC;AAE7C,SAAK,gBAAgB,QAAQ,QAAQ,OAAO,CAAC,SAAS,KAAK,SAAS,MAAM;AAG1E,SAAK,aAAa,EAAE,GAAG,QAAQ,SAAS,UAAU,CAAC;AAAA,EACrD;AAAA,EAhCA,IAAW,YAAY;AACrB,WAAO;AAAA,EACT;AAAA,EAEU,uBAAuB;AAC/B,WAAO,KAAK,QAAQ,UAAU;AAAA,EAChC;AAAA,EAEQ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EAuBR,MAAa,WACX,SACA;AACA,UAAM,OAAO,qBAAqB,OAAwB;AAC1D,QAAI,SAAS,KAAK,eAAe;AAC/B,WAAK,QAAQ,OAAO;AAAA,QAClB,GAAG;AAAA,QACH,SAAS,CAAC,GAAG,QAAQ,SAAS,GAAG,KAAK,aAAa;AAAA,QACnD,UAAU,KAAK;AAAA,QACf,UAAU,KAAK;AAAA,MACjB,CAAC;AAAA,IACH;AAEA,SAAK,aAAa;AAAA,EACpB;AAAA,EAEO,eAAe;AACpB,SAAK,gBAAgB;AACrB,SAAK,mBAAmB;AAAA,EAC1B;AACF;", "names": []}