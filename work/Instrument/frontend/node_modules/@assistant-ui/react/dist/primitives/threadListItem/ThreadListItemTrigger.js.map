{"version": 3, "sources": ["../../../src/primitives/threadListItem/ThreadListItemTrigger.ts"], "sourcesContent": ["\"use client\";\n\nimport {\n  ActionButtonElement,\n  ActionButtonProps,\n  createActionButton,\n} from \"../../utils/createActionButton\";\nimport { useThreadListItemRuntime } from \"../../context/react/ThreadListItemContext\";\n\nconst useThreadListItemTrigger = () => {\n  const runtime = useThreadListItemRuntime();\n  return () => {\n    runtime.switchTo();\n  };\n};\n\nexport namespace ThreadListItemPrimitiveTrigger {\n  export type Element = ActionButtonElement;\n  export type Props = ActionButtonProps<typeof useThreadListItemTrigger>;\n}\n\nexport const ThreadListItemPrimitiveTrigger = createActionButton(\n  \"ThreadListItemPrimitive.Trigger\",\n  useThreadListItemTrigger,\n);\n"], "mappings": ";;;AAEA;AAAA,EAGE;AAAA,OACK;AACP,SAAS,gCAAgC;AAEzC,IAAM,2BAA2B,MAAM;AACrC,QAAM,UAAU,yBAAyB;AACzC,SAAO,MAAM;AACX,YAAQ,SAAS;AAAA,EACnB;AACF;AAOO,IAAM,iCAAiC;AAAA,EAC5C;AAAA,EACA;AACF;", "names": []}