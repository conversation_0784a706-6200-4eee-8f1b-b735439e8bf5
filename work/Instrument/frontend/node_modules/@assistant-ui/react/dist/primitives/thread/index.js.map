{"version": 3, "sources": ["../../../src/primitives/thread/index.ts"], "sourcesContent": ["export { ThreadPrimitiveRoot as Root } from \"./ThreadRoot\";\nexport { ThreadPrimitiveEmpty as Empty } from \"./ThreadEmpty\";\nexport { ThreadPrimitiveIf as If } from \"./ThreadIf\";\nexport { ThreadPrimitiveViewport as Viewport } from \"./ThreadViewport\";\nexport { ThreadPrimitiveMessages as Messages } from \"./ThreadMessages\";\nexport { ThreadPrimitiveMessageByIndex as MessageByIndex } from \"./ThreadMessages\";\nexport { ThreadPrimitiveScrollToBottom as ScrollToBottom } from \"./ThreadScrollToBottom\";\nexport { ThreadPrimitiveSuggestion as Suggestion } from \"./ThreadSuggestion\";\n"], "mappings": ";AAAA,SAAgC,2BAAY;AAC5C,SAAiC,4BAAa;AAC9C,SAA8B,yBAAU;AACxC,SAAoC,+BAAgB;AACpD,SAAoC,+BAAgB;AACpD,SAA0C,qCAAsB;AAChE,SAA0C,qCAAsB;AAChE,SAAsC,iCAAkB;", "names": []}