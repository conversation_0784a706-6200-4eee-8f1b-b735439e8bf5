{"version": 3, "sources": ["../../../src/runtimes/adapters/RuntimeAdapterProvider.tsx"], "sourcesContent": ["\"use client\";\n\nimport { createContext, FC, ReactNode, useContext } from \"react\";\nimport { ThreadHistoryAdapter } from \"./thread-history/ThreadHistoryAdapter\";\nimport { ModelContextProvider } from \"../../model-context\";\n\nexport type RuntimeAdapters = {\n  modelContext?: ModelContextProvider;\n  history?: ThreadHistoryAdapter;\n};\n\nconst RuntimeAdaptersContext = createContext<RuntimeAdapters | null>(null);\n\nnamespace RuntimeAdapterProvider {\n  export type Props = {\n    adapters: RuntimeAdapters;\n    children: ReactNode;\n  };\n}\n\nexport const RuntimeAdapterProvider: FC<RuntimeAdapterProvider.Props> = ({\n  adapters,\n  children,\n}) => {\n  const context = useContext(RuntimeAdaptersContext);\n  return (\n    <RuntimeAdaptersContext.Provider\n      value={{\n        ...context,\n        ...adapters,\n      }}\n    >\n      {children}\n    </RuntimeAdaptersContext.Provider>\n  );\n};\n\nexport const useRuntimeAdapters = () => {\n  const adapters = useContext(RuntimeAdaptersContext);\n  return adapters;\n};\n"], "mappings": ";;;AAEA,SAAS,eAA8B,kBAAkB;AAwBrD;AAfJ,IAAM,yBAAyB,cAAsC,IAAI;AASlE,IAAM,yBAA2D,CAAC;AAAA,EACvE;AAAA,EACA;AACF,MAAM;AACJ,QAAM,UAAU,WAAW,sBAAsB;AACjD,SACE;AAAA,IAAC,uBAAuB;AAAA,IAAvB;AAAA,MACC,OAAO;AAAA,QACL,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AAAA,MAEC;AAAA;AAAA,EACH;AAEJ;AAEO,IAAM,qBAAqB,MAAM;AACtC,QAAM,WAAW,WAAW,sBAAsB;AAClD,SAAO;AACT;", "names": []}