{"version": 3, "sources": ["../../../src/runtimes/external-store/ExternalStoreRuntimeCore.tsx"], "sourcesContent": ["import { BaseAssistantRuntimeCore } from \"../core/BaseAssistantRuntimeCore\";\nimport { ExternalStoreThreadListRuntimeCore } from \"./ExternalStoreThreadListRuntimeCore\";\nimport { ExternalStoreAdapter } from \"./ExternalStoreAdapter\";\nimport { ExternalStoreThreadRuntimeCore } from \"./ExternalStoreThreadRuntimeCore\";\n\nconst getThreadListAdapter = (store: ExternalStoreAdapter<any>) => {\n  return store.adapters?.threadList ?? {};\n};\n\nexport class ExternalStoreRuntimeCore extends BaseAssistantRuntimeCore {\n  public readonly threads;\n\n  constructor(adapter: ExternalStoreAdapter<any>) {\n    super();\n    this.threads = new ExternalStoreThreadListRuntimeCore(\n      getThreadListAdapter(adapter),\n      () => new ExternalStoreThreadRuntimeCore(this._contextProvider, adapter),\n    );\n  }\n\n  public setAdapter(adapter: ExternalStoreAdapter<any>) {\n    // Update the thread list adapter and propagate store changes to the main thread\n    this.threads.__internal_setAdapter(getThreadListAdapter(adapter));\n    this.threads.getMainThreadRuntimeCore().__internal_setAdapter(adapter);\n  }\n}\n"], "mappings": ";AAAA,SAAS,gCAAgC;AACzC,SAAS,0CAA0C;AAEnD,SAAS,sCAAsC;AAE/C,IAAM,uBAAuB,CAAC,UAAqC;AACjE,SAAO,MAAM,UAAU,cAAc,CAAC;AACxC;AAEO,IAAM,2BAAN,cAAuC,yBAAyB;AAAA,EACrD;AAAA,EAEhB,YAAY,SAAoC;AAC9C,UAAM;AACN,SAAK,UAAU,IAAI;AAAA,MACjB,qBAAqB,OAAO;AAAA,MAC5B,MAAM,IAAI,+BAA+B,KAAK,kBAAkB,OAAO;AAAA,IACzE;AAAA,EACF;AAAA,EAEO,WAAW,SAAoC;AAEpD,SAAK,QAAQ,sBAAsB,qBAAqB,OAAO,CAAC;AAChE,SAAK,QAAQ,yBAAyB,EAAE,sBAAsB,OAAO;AAAA,EACvE;AACF;", "names": []}