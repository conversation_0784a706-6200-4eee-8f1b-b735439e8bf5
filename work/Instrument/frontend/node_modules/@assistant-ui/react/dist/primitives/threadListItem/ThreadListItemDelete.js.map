{"version": 3, "sources": ["../../../src/primitives/threadListItem/ThreadListItemDelete.ts"], "sourcesContent": ["\"use client\";\n\nimport {\n  ActionButtonElement,\n  ActionButtonProps,\n  createActionButton,\n} from \"../../utils/createActionButton\";\nimport { useThreadListItemRuntime } from \"../../context/react/ThreadListItemContext\";\n\nconst useThreadListItemDelete = () => {\n  const runtime = useThreadListItemRuntime();\n  return () => {\n    runtime.delete();\n  };\n};\n\nexport namespace ThreadListItemPrimitiveDelete {\n  export type Element = ActionButtonElement;\n  export type Props = ActionButtonProps<typeof useThreadListItemDelete>;\n}\n\nexport const ThreadListItemPrimitiveDelete = createActionButton(\n  \"ThreadListItemPrimitive.Delete\",\n  useThreadListItemDelete,\n);\n"], "mappings": ";;;AAEA;AAAA,EAGE;AAAA,OACK;AACP,SAAS,gCAAgC;AAEzC,IAAM,0BAA0B,MAAM;AACpC,QAAM,UAAU,yBAAyB;AACzC,SAAO,MAAM;AACX,YAAQ,OAAO;AAAA,EACjB;AACF;AAOO,IAAM,gCAAgC;AAAA,EAC3C;AAAA,EACA;AACF;", "names": []}