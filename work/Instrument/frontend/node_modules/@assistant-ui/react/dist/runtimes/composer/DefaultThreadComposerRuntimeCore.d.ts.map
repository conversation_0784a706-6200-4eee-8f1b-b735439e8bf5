{"version": 3, "file": "DefaultThreadComposerRuntimeCore.d.ts", "sourceRoot": "", "sources": ["../../../src/runtimes/composer/DefaultThreadComposerRuntimeCore.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,aAAa,EAAE,iBAAiB,EAAE,MAAM,aAAa,CAAC;AAC/D,OAAO,EAAE,iBAAiB,EAAE,MAAM,wBAAwB,CAAC;AAC3D,OAAO,EAAE,yBAAyB,EAAE,MAAM,6BAA6B,CAAC;AACxE,OAAO,EAAE,iBAAiB,EAAE,MAAM,2BAA2B,CAAC;AAC9D,OAAO,EAAE,uBAAuB,EAAE,MAAM,2BAA2B,CAAC;AAEpE,qBAAa,gCACX,SAAQ,uBACR,YAAW,yBAAyB;IAgBlC,OAAO,CAAC,OAAO;IAdjB,OAAO,CAAC,UAAU,CAAS;IAC3B,IAAW,SAAS,YAEnB;IAED,IAAoB,WAAW,IAAI,SAAS,iBAAiB,EAAE,CAE9D;IAED,SAAS,CAAC,oBAAoB;gBAKpB,OAAO,EAAE,IAAI,CAAC,iBAAiB,EAAE,UAAU,CAAC,GAAG;QACrD,QAAQ,CAAC,EAAE;YAAE,WAAW,CAAC,EAAE,iBAAiB,GAAG,SAAS,CAAA;SAAE,GAAG,SAAS,CAAC;KACxE;IAMI,OAAO;IASD,UAAU,CACrB,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,UAAU,GAAG,UAAU,CAAC;IAS1C,YAAY;CAG1B"}