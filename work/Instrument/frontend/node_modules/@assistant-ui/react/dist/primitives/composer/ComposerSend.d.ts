import { ActionButtonElement, ActionButtonProps } from "../../utils/createActionButton";
export declare const useComposerSend: () => (() => void) | null;
export declare namespace ComposerPrimitiveSend {
    type Element = ActionButtonElement;
    /**
     * Props for the ComposerPrimitive.Send component.
     * Inherits all button element props and action button functionality.
     */
    type Props = ActionButtonProps<typeof useComposerSend>;
}
/**
 * A button component that sends the current message in the composer.
 *
 * This component automatically handles the send functionality and is disabled
 * when sending is not available (e.g., when the thread is running, the composer
 * is empty, or not in editing mode).
 *
 * @example
 * ```tsx
 * <ComposerPrimitive.Send>
 *   Send Message
 * </ComposerPrimitive.Send>
 * ```
 */
export declare const ComposerPrimitiveSend: import("react").ForwardRefExoticComponent<Omit<import("react").ClassAttributes<HTMLButtonElement> & import("react").ButtonHTMLAttributes<HTMLButtonElement> & {
    asChild?: boolean;
}, "ref"> & import("react").RefAttributes<HTMLButtonElement>>;
//# sourceMappingURL=ComposerSend.d.ts.map