{"version": 3, "sources": ["../../../src/primitives/message/MessageRoot.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport {\n  type ComponentRef,\n  forwardRef,\n  ComponentPropsWithoutRef,\n  useCallback,\n} from \"react\";\nimport { useMessageUtilsStore } from \"../../context/react/MessageContext\";\nimport { useManagedRef } from \"../../utils/hooks/useManagedRef\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\n\nconst useIsHoveringRef = () => {\n  const messageUtilsStore = useMessageUtilsStore();\n  const callbackRef = useCallback(\n    (el: HTMLElement) => {\n      const setIsHovering = messageUtilsStore.getState().setIsHovering;\n\n      const handleMouseEnter = () => {\n        setIsHovering(true);\n      };\n      const handleMouseLeave = () => {\n        setIsHovering(false);\n      };\n\n      el.addEventListener(\"mouseenter\", handleMouseEnter);\n      el.addEventListener(\"mouseleave\", handleMouseLeave);\n\n      return () => {\n        el.removeEventListener(\"mouseenter\", handleMouseEnter);\n        el.removeEventListener(\"mouseleave\", handleMouseLeave);\n        setIsHovering(false);\n      };\n    },\n    [messageUtilsStore],\n  );\n\n  return useManagedRef(callbackRef);\n};\n\nexport namespace MessagePrimitiveRoot {\n  export type Element = ComponentRef<typeof Primitive.div>;\n  /**\n   * Props for the MessagePrimitive.Root component.\n   * Accepts all standard div element props.\n   */\n  export type Props = ComponentPropsWithoutRef<typeof Primitive.div>;\n}\n\n/**\n * The root container component for a message.\n *\n * This component provides the foundational wrapper for message content and handles\n * hover state management for the message. It automatically tracks when the user\n * is hovering over the message, which can be used by child components like action bars.\n *\n * @example\n * ```tsx\n * <MessagePrimitive.Root>\n *   <MessagePrimitive.Content />\n *   <ActionBarPrimitive.Root>\n *     <ActionBarPrimitive.Copy />\n *     <ActionBarPrimitive.Edit />\n *   </ActionBarPrimitive.Root>\n * </MessagePrimitive.Root>\n * ```\n */\nexport const MessagePrimitiveRoot = forwardRef<\n  MessagePrimitiveRoot.Element,\n  MessagePrimitiveRoot.Props\n>((props, forwardRef) => {\n  const isHoveringRef = useIsHoveringRef();\n  const ref = useComposedRefs<HTMLDivElement>(forwardRef, isHoveringRef);\n\n  return <Primitive.div {...props} ref={ref} />;\n});\n\nMessagePrimitiveRoot.displayName = \"MessagePrimitive.Root\";\n"], "mappings": ";;;AAEA,SAAS,iBAAiB;AAC1B;AAAA,EAEE;AAAA,EAEA;AAAA,OACK;AACP,SAAS,4BAA4B;AACrC,SAAS,qBAAqB;AAC9B,SAAS,uBAAuB;AAgEvB;AA9DT,IAAM,mBAAmB,MAAM;AAC7B,QAAM,oBAAoB,qBAAqB;AAC/C,QAAM,cAAc;AAAA,IAClB,CAAC,OAAoB;AACnB,YAAM,gBAAgB,kBAAkB,SAAS,EAAE;AAEnD,YAAM,mBAAmB,MAAM;AAC7B,sBAAc,IAAI;AAAA,MACpB;AACA,YAAM,mBAAmB,MAAM;AAC7B,sBAAc,KAAK;AAAA,MACrB;AAEA,SAAG,iBAAiB,cAAc,gBAAgB;AAClD,SAAG,iBAAiB,cAAc,gBAAgB;AAElD,aAAO,MAAM;AACX,WAAG,oBAAoB,cAAc,gBAAgB;AACrD,WAAG,oBAAoB,cAAc,gBAAgB;AACrD,sBAAc,KAAK;AAAA,MACrB;AAAA,IACF;AAAA,IACA,CAAC,iBAAiB;AAAA,EACpB;AAEA,SAAO,cAAc,WAAW;AAClC;AA6BO,IAAM,uBAAuB,WAGlC,CAAC,OAAOA,gBAAe;AACvB,QAAM,gBAAgB,iBAAiB;AACvC,QAAM,MAAM,gBAAgCA,aAAY,aAAa;AAErE,SAAO,oBAAC,UAAU,KAAV,EAAe,GAAG,OAAO,KAAU;AAC7C,CAAC;AAED,qBAAqB,cAAc;", "names": ["forwardRef"]}