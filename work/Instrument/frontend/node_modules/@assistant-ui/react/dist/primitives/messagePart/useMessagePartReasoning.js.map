{"version": 3, "sources": ["../../../src/primitives/messagePart/useMessagePartReasoning.tsx"], "sourcesContent": ["\"use client\";\n\nimport { MessagePartState } from \"../../api/MessagePartRuntime\";\nimport { useMessagePart } from \"../../context/react/MessagePartContext\";\nimport { ReasoningMessagePart } from \"../../types\";\n\nexport const useMessagePartReasoning = () => {\n  const text = useMessagePart((c) => {\n    if (c.type !== \"reasoning\")\n      throw new Error(\n        \"MessagePartReasoning can only be used inside reasoning message parts.\",\n      );\n\n    return c as MessagePartState & ReasoningMessagePart;\n  });\n\n  return text;\n};\n"], "mappings": ";;;AAGA,SAAS,sBAAsB;AAGxB,IAAM,0BAA0B,MAAM;AAC3C,QAAM,OAAO,eAAe,CAAC,MAAM;AACjC,QAAI,EAAE,SAAS;AACb,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAEF,WAAO;AAAA,EACT,CAAC;AAED,SAAO;AACT;", "names": []}