"use client";

// src/primitives/attachment/AttachmentRoot.tsx
import { Primitive } from "@radix-ui/react-primitive";
import { forwardRef } from "react";
import { jsx } from "react/jsx-runtime";
var AttachmentPrimitiveRoot = forwardRef((props, ref) => {
  return /* @__PURE__ */ jsx(Primitive.div, { ...props, ref });
});
AttachmentPrimitiveRoot.displayName = "AttachmentPrimitive.Root";
export {
  AttachmentPrimitiveRoot
};
//# sourceMappingURL=AttachmentRoot.js.map