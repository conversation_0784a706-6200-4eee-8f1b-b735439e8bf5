{"version": 3, "sources": ["../../../src/primitives/actionBar/useActionBarFloatStatus.tsx"], "sourcesContent": ["\"use client\";\n\nimport {\n  useMessageRuntime,\n  useMessageUtilsStore,\n} from \"../../context/react/MessageContext\";\nimport { useThreadRuntime } from \"../../context/react/ThreadContext\";\nimport { useCombinedStore } from \"../../utils/combined/useCombinedStore\";\n\nexport enum HideAndFloatStatus {\n  Hidden = \"hidden\",\n  Floating = \"floating\",\n  Normal = \"normal\",\n}\n\nexport type UseActionBarFloatStatusProps = {\n  hideWhenRunning?: boolean | undefined;\n  autohide?: \"always\" | \"not-last\" | \"never\" | undefined;\n  autohideFloat?: \"always\" | \"single-branch\" | \"never\" | undefined;\n};\n\nexport const useActionBarFloatStatus = ({\n  hideWhenRunning,\n  autohide,\n  autohideFloat,\n}: UseActionBarFloatStatusProps) => {\n  const threadRuntime = useThreadRuntime();\n  const messageRuntime = useMessageRuntime();\n  const messageUtilsStore = useMessageUtilsStore();\n\n  return useCombinedStore(\n    [threadRuntime, messageRuntime, messageUtilsStore],\n    (t, m, mu) => {\n      if (hideWhenRunning && t.isRunning) return HideAndFloatStatus.Hidden;\n\n      const autohideEnabled =\n        autohide === \"always\" || (autohide === \"not-last\" && !m.isLast);\n\n      // normal status\n      if (!autohideEnabled) return HideAndFloatStatus.Normal;\n\n      // hidden status\n      if (!mu.isHovering) return HideAndFloatStatus.Hidden;\n\n      // floating status\n      if (\n        autohideFloat === \"always\" ||\n        (autohideFloat === \"single-branch\" && m.branchCount <= 1)\n      )\n        return HideAndFloatStatus.Floating;\n\n      return HideAndFloatStatus.Normal;\n    },\n  );\n};\n"], "mappings": ";;;AAEA;AAAA,EACE;AAAA,EACA;AAAA,OACK;AACP,SAAS,wBAAwB;AACjC,SAAS,wBAAwB;AAE1B,IAAK,qBAAL,kBAAKA,wBAAL;AACL,EAAAA,oBAAA,YAAS;AACT,EAAAA,oBAAA,cAAW;AACX,EAAAA,oBAAA,YAAS;AAHC,SAAAA;AAAA,GAAA;AAYL,IAAM,0BAA0B,CAAC;AAAA,EACtC;AAAA,EACA;AAAA,EACA;AACF,MAAoC;AAClC,QAAM,gBAAgB,iBAAiB;AACvC,QAAM,iBAAiB,kBAAkB;AACzC,QAAM,oBAAoB,qBAAqB;AAE/C,SAAO;AAAA,IACL,CAAC,eAAe,gBAAgB,iBAAiB;AAAA,IACjD,CAAC,GAAG,GAAG,OAAO;AACZ,UAAI,mBAAmB,EAAE,UAAW,QAAO;AAE3C,YAAM,kBACJ,aAAa,YAAa,aAAa,cAAc,CAAC,EAAE;AAG1D,UAAI,CAAC,gBAAiB,QAAO;AAG7B,UAAI,CAAC,GAAG,WAAY,QAAO;AAG3B,UACE,kBAAkB,YACjB,kBAAkB,mBAAmB,EAAE,eAAe;AAEvD,eAAO;AAET,aAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": ["HideAndFloatStatus"]}