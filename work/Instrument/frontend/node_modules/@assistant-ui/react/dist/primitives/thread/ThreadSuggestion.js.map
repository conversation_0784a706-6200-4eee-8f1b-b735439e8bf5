{"version": 3, "sources": ["../../../src/primitives/thread/ThreadSuggestion.tsx"], "sourcesContent": ["\"use client\";\n\nimport {\n  ActionButtonElement,\n  ActionButtonProps,\n  createActionButton,\n} from \"../../utils/createActionButton\";\nimport { useCallback } from \"react\";\nimport { useThread } from \"../../context\";\nimport { useThreadRuntime } from \"../../context/react/ThreadContext\";\n\nconst useThreadSuggestion = ({\n  prompt,\n  autoSend,\n}: {\n  prompt: string;\n  method?: \"replace\";\n  autoSend?: boolean | undefined;\n}) => {\n  const threadRuntime = useThreadRuntime();\n\n  const disabled = useThread((t) => t.isDisabled);\n  const callback = useCallback(() => {\n    if (autoSend && !threadRuntime.getState().isRunning) {\n      threadRuntime.append(prompt);\n    } else {\n      threadRuntime.composer.setText(prompt);\n    }\n  }, [threadRuntime, autoSend, prompt]);\n\n  if (disabled) return null;\n  return callback;\n};\n\nexport namespace ThreadPrimitiveSuggestion {\n  export type Element = ActionButtonElement;\n  export type Props = ActionButtonProps<typeof useThreadSuggestion>;\n}\n\nexport const ThreadPrimitiveSuggestion = createActionButton(\n  \"ThreadPrimitive.Suggestion\",\n  useThreadSuggestion,\n  [\"prompt\", \"autoSend\", \"method\"],\n);\n"], "mappings": ";;;AAEA;AAAA,EAGE;AAAA,OACK;AACP,SAAS,mBAAmB;AAC5B,SAAS,iBAAiB;AAC1B,SAAS,wBAAwB;AAEjC,IAAM,sBAAsB,CAAC;AAAA,EAC3B;AAAA,EACA;AACF,MAIM;AACJ,QAAM,gBAAgB,iBAAiB;AAEvC,QAAM,WAAW,UAAU,CAAC,MAAM,EAAE,UAAU;AAC9C,QAAM,WAAW,YAAY,MAAM;AACjC,QAAI,YAAY,CAAC,cAAc,SAAS,EAAE,WAAW;AACnD,oBAAc,OAAO,MAAM;AAAA,IAC7B,OAAO;AACL,oBAAc,SAAS,QAAQ,MAAM;AAAA,IACvC;AAAA,EACF,GAAG,CAAC,eAAe,UAAU,MAAM,CAAC;AAEpC,MAAI,SAAU,QAAO;AACrB,SAAO;AACT;AAOO,IAAM,4BAA4B;AAAA,EACvC;AAAA,EACA;AAAA,EACA,CAAC,UAAU,YAAY,QAAQ;AACjC;", "names": []}