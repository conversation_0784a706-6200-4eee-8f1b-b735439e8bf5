import { ActionButtonElement, ActionButtonProps } from "../../utils/createActionButton";
declare const useActionBarSpeak: () => (() => Promise<void>) | null;
export declare namespace ActionBarPrimitiveSpeak {
    type Element = ActionButtonElement;
    type Props = ActionButtonProps<typeof useActionBarSpeak>;
}
export declare const ActionBarPrimitiveSpeak: import("react").ForwardRefExoticComponent<Omit<import("react").ClassAttributes<HTMLButtonElement> & import("react").ButtonHTMLAttributes<HTMLButtonElement> & {
    asChild?: boolean;
}, "ref"> & import("react").RefAttributes<HTMLButtonElement>>;
export {};
//# sourceMappingURL=ActionBarSpeak.d.ts.map