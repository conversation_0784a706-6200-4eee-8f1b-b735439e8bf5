{"version": 3, "sources": ["../../../src/primitives/composer/ComposerAttachments.tsx"], "sourcesContent": ["\"use client\";\n\nimport { ComponentType, type FC, memo, useMemo } from \"react\";\nimport { Attachment } from \"../../types\";\nimport { useComposer, useComposerRuntime } from \"../../context\";\nimport { useThreadComposerAttachment } from \"../../context/react/AttachmentContext\";\nimport { AttachmentRuntimeProvider } from \"../../context/providers/AttachmentRuntimeProvider\";\n\nexport namespace ComposerPrimitiveAttachments {\n  export type Props = {\n    components:\n      | {\n          Image?: ComponentType | undefined;\n          Document?: ComponentType | undefined;\n          File?: ComponentType | undefined;\n          Attachment?: ComponentType | undefined;\n        }\n      | undefined;\n  };\n}\n\nconst getComponent = (\n  components: ComposerPrimitiveAttachments.Props[\"components\"],\n  attachment: Attachment,\n) => {\n  const type = attachment.type;\n  switch (type) {\n    case \"image\":\n      return components?.Image ?? components?.Attachment;\n    case \"document\":\n      return components?.Document ?? components?.Attachment;\n    case \"file\":\n      return components?.File ?? components?.Attachment;\n    default:\n      const _exhaustiveCheck: never = type;\n      throw new Error(`Unknown attachment type: ${_exhaustiveCheck}`);\n  }\n};\n\nconst AttachmentComponent: FC<{\n  components: ComposerPrimitiveAttachments.Props[\"components\"];\n}> = ({ components }) => {\n  const Component = useThreadComposerAttachment((a) =>\n    getComponent(components, a),\n  );\n\n  if (!Component) return null;\n  return <Component />;\n};\n\nexport namespace ComposerPrimitiveAttachmentByIndex {\n  export type Props = {\n    index: number;\n    components?: ComposerPrimitiveAttachments.Props[\"components\"];\n  };\n}\n\n/**\n * Renders a single attachment at the specified index within the composer.\n *\n * This component provides direct access to render a specific attachment\n * from the composer's attachment list using the provided component configuration.\n *\n * @example\n * ```tsx\n * <ComposerPrimitive.AttachmentByIndex\n *   index={0}\n *   components={{\n *     Image: MyImageAttachment,\n *     Document: MyDocumentAttachment\n *   }}\n * />\n * ```\n */\nexport const ComposerPrimitiveAttachmentByIndex: FC<ComposerPrimitiveAttachmentByIndex.Props> =\n  memo(\n    ({ index, components }) => {\n      const composerRuntime = useComposerRuntime();\n      const runtime = useMemo(\n        () => composerRuntime.getAttachmentByIndex(index),\n        [composerRuntime, index],\n      );\n\n      return (\n        <AttachmentRuntimeProvider runtime={runtime}>\n          <AttachmentComponent components={components} />\n        </AttachmentRuntimeProvider>\n      );\n    },\n    (prev, next) =>\n      prev.index === next.index &&\n      prev.components?.Image === next.components?.Image &&\n      prev.components?.Document === next.components?.Document &&\n      prev.components?.File === next.components?.File &&\n      prev.components?.Attachment === next.components?.Attachment,\n  );\n\nComposerPrimitiveAttachmentByIndex.displayName =\n  \"ComposerPrimitive.AttachmentByIndex\";\n\nexport const ComposerPrimitiveAttachments: FC<\n  ComposerPrimitiveAttachments.Props\n> = ({ components }) => {\n  const attachmentsCount = useComposer((s) => s.attachments.length);\n\n  const attachmentElements = useMemo(() => {\n    return Array.from({ length: attachmentsCount }, (_, index) => (\n      <ComposerPrimitiveAttachmentByIndex\n        key={index}\n        index={index}\n        components={components}\n      />\n    ));\n  }, [attachmentsCount, components]);\n\n  return attachmentElements;\n};\n\nComposerPrimitiveAttachments.displayName = \"ComposerPrimitive.Attachments\";\n"], "mappings": ";;;AAEA,SAAiC,MAAM,eAAe;AAEtD,SAAS,aAAa,0BAA0B;AAChD,SAAS,mCAAmC;AAC5C,SAAS,iCAAiC;AAyCjC;AA1BT,IAAM,eAAe,CACnB,YACA,eACG;AACH,QAAM,OAAO,WAAW;AACxB,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,aAAO,YAAY,SAAS,YAAY;AAAA,IAC1C,KAAK;AACH,aAAO,YAAY,YAAY,YAAY;AAAA,IAC7C,KAAK;AACH,aAAO,YAAY,QAAQ,YAAY;AAAA,IACzC;AACE,YAAM,mBAA0B;AAChC,YAAM,IAAI,MAAM,4BAA4B,gBAAgB,EAAE;AAAA,EAClE;AACF;AAEA,IAAM,sBAED,CAAC,EAAE,WAAW,MAAM;AACvB,QAAM,YAAY;AAAA,IAA4B,CAAC,MAC7C,aAAa,YAAY,CAAC;AAAA,EAC5B;AAEA,MAAI,CAAC,UAAW,QAAO;AACvB,SAAO,oBAAC,aAAU;AACpB;AA0BO,IAAM,qCACX;AAAA,EACE,CAAC,EAAE,OAAO,WAAW,MAAM;AACzB,UAAM,kBAAkB,mBAAmB;AAC3C,UAAM,UAAU;AAAA,MACd,MAAM,gBAAgB,qBAAqB,KAAK;AAAA,MAChD,CAAC,iBAAiB,KAAK;AAAA,IACzB;AAEA,WACE,oBAAC,6BAA0B,SACzB,8BAAC,uBAAoB,YAAwB,GAC/C;AAAA,EAEJ;AAAA,EACA,CAAC,MAAM,SACL,KAAK,UAAU,KAAK,SACpB,KAAK,YAAY,UAAU,KAAK,YAAY,SAC5C,KAAK,YAAY,aAAa,KAAK,YAAY,YAC/C,KAAK,YAAY,SAAS,KAAK,YAAY,QAC3C,KAAK,YAAY,eAAe,KAAK,YAAY;AACrD;AAEF,mCAAmC,cACjC;AAEK,IAAM,+BAET,CAAC,EAAE,WAAW,MAAM;AACtB,QAAM,mBAAmB,YAAY,CAAC,MAAM,EAAE,YAAY,MAAM;AAEhE,QAAM,qBAAqB,QAAQ,MAAM;AACvC,WAAO,MAAM,KAAK,EAAE,QAAQ,iBAAiB,GAAG,CAAC,GAAG,UAClD;AAAA,MAAC;AAAA;AAAA,QAEC;AAAA,QACA;AAAA;AAAA,MAFK;AAAA,IAGP,CACD;AAAA,EACH,GAAG,CAAC,kBAAkB,UAAU,CAAC;AAEjC,SAAO;AACT;AAEA,6BAA6B,cAAc;", "names": []}