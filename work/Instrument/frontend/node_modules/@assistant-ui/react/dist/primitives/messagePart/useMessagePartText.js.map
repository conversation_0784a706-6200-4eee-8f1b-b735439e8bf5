{"version": 3, "sources": ["../../../src/primitives/messagePart/useMessagePartText.tsx"], "sourcesContent": ["\"use client\";\n\nimport { MessagePartState } from \"../../api/MessagePartRuntime\";\nimport { useMessagePart } from \"../../context/react/MessagePartContext\";\nimport { TextMessagePart, ReasoningMessagePart } from \"../../types\";\n\nexport const useMessagePartText = () => {\n  const text = useMessagePart((c) => {\n    if (c.type !== \"text\" && c.type !== \"reasoning\")\n      throw new Error(\n        \"MessagePartText can only be used inside text or reasoning message parts.\",\n      );\n\n    return c as MessagePartState & (TextMessagePart | ReasoningMessagePart);\n  });\n\n  return text;\n};\n"], "mappings": ";;;AAGA,SAAS,sBAAsB;AAGxB,IAAM,qBAAqB,MAAM;AACtC,QAAM,OAAO,eAAe,CAAC,MAAM;AACjC,QAAI,EAAE,SAAS,UAAU,EAAE,SAAS;AAClC,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAEF,WAAO;AAAA,EACT,CAAC;AAED,SAAO;AACT;", "names": []}