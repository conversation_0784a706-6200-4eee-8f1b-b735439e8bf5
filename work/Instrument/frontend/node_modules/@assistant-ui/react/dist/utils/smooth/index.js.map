{"version": 3, "sources": ["../../../src/utils/smooth/index.ts"], "sourcesContent": ["\"use client\";\n// TODO createContextStoreHook does not work well with server-side nextjs bundler\n// use client necessary here for now\n\nexport { useSmooth } from \"./useSmooth\";\nexport { useSmoothStatus, withSmoothContextProvider } from \"./SmoothContext\";\n"], "mappings": ";;;AAIA,SAAS,iBAAiB;AAC1B,SAAS,iBAAiB,iCAAiC;", "names": []}