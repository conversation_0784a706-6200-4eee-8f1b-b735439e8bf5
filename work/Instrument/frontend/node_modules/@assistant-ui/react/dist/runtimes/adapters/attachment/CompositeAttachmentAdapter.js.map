{"version": 3, "sources": ["../../../../src/runtimes/adapters/attachment/CompositeAttachmentAdapter.ts"], "sourcesContent": ["import { Attachment, PendingAttachment } from \"../../../types/AttachmentTypes\";\nimport { AttachmentAdapter } from \"./AttachmentAdapter\";\n\nfunction fileMatchesAccept(\n  file: { name: string; type: string },\n  acceptString: string,\n) {\n  // Check if the accept string is \"*\", which allows any file\n  if (acceptString === \"*\") {\n    return true;\n  }\n\n  // Split the accept string into an array of allowed types\n  const allowedTypes = acceptString\n    .split(\",\")\n    .map((type) => type.trim().toLowerCase());\n\n  // Get the file's extension and MIME type\n  const fileExtension = \".\" + file.name.split(\".\").pop()!.toLowerCase();\n  const fileMimeType = file.type.toLowerCase();\n\n  for (const type of allowedTypes) {\n    // Check for file extension match\n    if (type.startsWith(\".\") && type === fileExtension) {\n      return true;\n    }\n\n    // Check for exact MIME type match\n    if (type.includes(\"/\") && type === fileMimeType) {\n      return true;\n    }\n\n    if (type === \"image/*\" || type === \"video/*\" || type === \"audio/*\") {\n      // Check for wildcard MIME type match\n      if (type.endsWith(\"/*\")) {\n        const generalType = type.split(\"/\")[0]!;\n        if (fileMimeType.startsWith(generalType + \"/\")) {\n          return true;\n        }\n      }\n    }\n  }\n\n  return false;\n}\n\nexport class CompositeAttachmentAdapter implements AttachmentAdapter {\n  private _adapters: AttachmentAdapter[];\n\n  public accept: string;\n\n  constructor(adapters: AttachmentAdapter[]) {\n    this._adapters = adapters;\n\n    const wildcardIdx = adapters.findIndex((a) => a.accept === \"*\");\n    if (wildcardIdx !== -1) {\n      if (wildcardIdx !== adapters.length - 1)\n        throw new Error(\n          \"A wildcard adapter (handling all files) can only be specified as the last adapter.\",\n        );\n\n      this.accept = \"*\";\n    } else {\n      this.accept = adapters.map((a) => a.accept).join(\",\");\n    }\n  }\n\n  public add(state: { file: File }) {\n    for (const adapter of this._adapters) {\n      if (fileMatchesAccept(state.file, adapter.accept)) {\n        return adapter.add(state);\n      }\n    }\n    throw new Error(\"No matching adapter found for file\");\n  }\n\n  public async send(attachment: PendingAttachment) {\n    const adapters = this._adapters.slice();\n    for (const adapter of adapters) {\n      if (fileMatchesAccept(attachment.file, adapter.accept)) {\n        return adapter.send(attachment);\n      }\n    }\n    throw new Error(\"No matching adapter found for attachment\");\n  }\n\n  public async remove(attachment: Attachment) {\n    const adapters = this._adapters.slice();\n    for (const adapter of adapters) {\n      if (\n        fileMatchesAccept(\n          {\n            name: attachment.name,\n            type: attachment.contentType,\n          },\n          adapter.accept,\n        )\n      ) {\n        return adapter.remove(attachment);\n      }\n    }\n    throw new Error(\"No matching adapter found for attachment\");\n  }\n}\n"], "mappings": ";AAGA,SAAS,kBACP,MACA,cACA;AAEA,MAAI,iBAAiB,KAAK;AACxB,WAAO;AAAA,EACT;AAGA,QAAM,eAAe,aAClB,MAAM,GAAG,EACT,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE,YAAY,CAAC;AAG1C,QAAM,gBAAgB,MAAM,KAAK,KAAK,MAAM,GAAG,EAAE,IAAI,EAAG,YAAY;AACpE,QAAM,eAAe,KAAK,KAAK,YAAY;AAE3C,aAAW,QAAQ,cAAc;AAE/B,QAAI,KAAK,WAAW,GAAG,KAAK,SAAS,eAAe;AAClD,aAAO;AAAA,IACT;AAGA,QAAI,KAAK,SAAS,GAAG,KAAK,SAAS,cAAc;AAC/C,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,aAAa,SAAS,aAAa,SAAS,WAAW;AAElE,UAAI,KAAK,SAAS,IAAI,GAAG;AACvB,cAAM,cAAc,KAAK,MAAM,GAAG,EAAE,CAAC;AACrC,YAAI,aAAa,WAAW,cAAc,GAAG,GAAG;AAC9C,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAEO,IAAM,6BAAN,MAA8D;AAAA,EAC3D;AAAA,EAED;AAAA,EAEP,YAAY,UAA+B;AACzC,SAAK,YAAY;AAEjB,UAAM,cAAc,SAAS,UAAU,CAAC,MAAM,EAAE,WAAW,GAAG;AAC9D,QAAI,gBAAgB,IAAI;AACtB,UAAI,gBAAgB,SAAS,SAAS;AACpC,cAAM,IAAI;AAAA,UACR;AAAA,QACF;AAEF,WAAK,SAAS;AAAA,IAChB,OAAO;AACL,WAAK,SAAS,SAAS,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,GAAG;AAAA,IACtD;AAAA,EACF;AAAA,EAEO,IAAI,OAAuB;AAChC,eAAW,WAAW,KAAK,WAAW;AACpC,UAAI,kBAAkB,MAAM,MAAM,QAAQ,MAAM,GAAG;AACjD,eAAO,QAAQ,IAAI,KAAK;AAAA,MAC1B;AAAA,IACF;AACA,UAAM,IAAI,MAAM,oCAAoC;AAAA,EACtD;AAAA,EAEA,MAAa,KAAK,YAA+B;AAC/C,UAAM,WAAW,KAAK,UAAU,MAAM;AACtC,eAAW,WAAW,UAAU;AAC9B,UAAI,kBAAkB,WAAW,MAAM,QAAQ,MAAM,GAAG;AACtD,eAAO,QAAQ,KAAK,UAAU;AAAA,MAChC;AAAA,IACF;AACA,UAAM,IAAI,MAAM,0CAA0C;AAAA,EAC5D;AAAA,EAEA,MAAa,OAAO,YAAwB;AAC1C,UAAM,WAAW,KAAK,UAAU,MAAM;AACtC,eAAW,WAAW,UAAU;AAC9B,UACE;AAAA,QACE;AAAA,UACE,MAAM,WAAW;AAAA,UACjB,MAAM,WAAW;AAAA,QACnB;AAAA,QACA,QAAQ;AAAA,MACV,GACA;AACA,eAAO,QAAQ,OAAO,UAAU;AAAA,MAClC;AAAA,IACF;AACA,UAAM,IAAI,MAAM,0CAA0C;AAAA,EAC5D;AACF;", "names": []}