{"version": 3, "sources": ["../../../src/runtimes/external-store/ThreadMessageLike.tsx"], "sourcesContent": ["import { parsePartialJsonObject } from \"assistant-stream/utils\";\nimport { generateId } from \"../../utils/idUtils\";\nimport {\n  MessageStatus,\n  TextMessagePart,\n  ImageMessagePart,\n  ThreadMessage,\n  ThreadAssistantMessagePart,\n  ThreadAssistantMessage,\n  ThreadUserMessagePart,\n  ThreadUserMessage,\n  ThreadSystemMessage,\n  CompleteAttachment,\n  FileMessagePart,\n  Unstable_AudioMessagePart,\n} from \"../../types\";\nimport {\n  ReasoningMessagePart,\n  SourceMessagePart,\n  ThreadStep,\n} from \"../../types/AssistantTypes\";\nimport { ReadonlyJSONObject, ReadonlyJSONValue } from \"assistant-stream/utils\";\n\nexport type ThreadMessageLike = {\n  readonly role: \"assistant\" | \"user\" | \"system\";\n  readonly content:\n    | string\n    | readonly (\n        | TextMessagePart\n        | ReasoningMessagePart\n        | SourceMessagePart\n        | ImageMessagePart\n        | FileMessagePart\n        | Unstable_AudioMessagePart\n        | {\n            readonly type: \"tool-call\";\n            readonly toolCallId?: string;\n            readonly toolName: string;\n            readonly args?: ReadonlyJSONObject;\n            readonly argsText?: string;\n            readonly artifact?: any;\n            readonly result?: any | undefined;\n            readonly isError?: boolean | undefined;\n            readonly parentId?: string | undefined;\n          }\n      )[];\n  readonly id?: string | undefined;\n  readonly createdAt?: Date | undefined;\n  readonly status?: MessageStatus | undefined;\n  readonly attachments?: readonly CompleteAttachment[] | undefined;\n  readonly metadata?:\n    | {\n        readonly unstable_state?: ReadonlyJSONValue;\n        readonly unstable_annotations?:\n          | readonly ReadonlyJSONValue[]\n          | undefined;\n        readonly unstable_data?: readonly ReadonlyJSONValue[] | undefined;\n        readonly steps?: readonly ThreadStep[] | undefined;\n        readonly custom?: Record<string, unknown> | undefined;\n      }\n    | undefined;\n};\n\nexport const fromThreadMessageLike = (\n  like: ThreadMessageLike,\n  fallbackId: string,\n  fallbackStatus: MessageStatus,\n): ThreadMessage => {\n  const { role, id, createdAt, attachments, status, metadata } = like;\n  const common = {\n    id: id ?? fallbackId,\n    createdAt: createdAt ?? new Date(),\n  };\n\n  const content =\n    typeof like.content === \"string\"\n      ? [{ type: \"text\" as const, text: like.content }]\n      : like.content;\n\n  const sanitizeImageContent = ({\n    image,\n    ...rest\n  }: ImageMessagePart): ImageMessagePart | null => {\n    const match = image.match(\n      /^data:image\\/(png|jpeg|jpg|gif|webp);base64,(.*)$/,\n    );\n    if (match) {\n      return { ...rest, image };\n    }\n    console.warn(`Invalid image data format detected`);\n    return null;\n  };\n\n  if (role !== \"user\" && attachments?.length)\n    throw new Error(\"attachments are only supported for user messages\");\n\n  if (role !== \"assistant\" && status)\n    throw new Error(\"status is only supported for assistant messages\");\n\n  if (role !== \"assistant\" && metadata?.steps)\n    throw new Error(\"metadata.steps is only supported for assistant messages\");\n\n  switch (role) {\n    case \"assistant\":\n      return {\n        ...common,\n        role,\n        content: content\n          .map((part): ThreadAssistantMessagePart | null => {\n            const type = part.type;\n            switch (type) {\n              case \"text\":\n              case \"reasoning\":\n                if (part.text.trim().length === 0) return null;\n                return part;\n\n              case \"file\":\n              case \"source\":\n                return part;\n\n              case \"image\":\n                return sanitizeImageContent(part);\n\n              case \"tool-call\": {\n                const { parentId, ...basePart } = part;\n                const commonProps = {\n                  ...basePart,\n                  toolCallId: part.toolCallId ?? \"tool-\" + generateId(),\n                  ...(parentId !== undefined && { parentId }),\n                };\n\n                if (part.args) {\n                  return {\n                    ...commonProps,\n                    args: part.args,\n                    argsText: JSON.stringify(part.args),\n                  };\n                }\n                return {\n                  ...commonProps,\n                  args:\n                    part.args ??\n                    parsePartialJsonObject(part.argsText ?? \"\") ??\n                    {},\n                  argsText: part.argsText ?? \"\",\n                };\n              }\n\n              default: {\n                const unhandledType: \"audio\" = type;\n                throw new Error(\n                  `Unsupported assistant message part type: ${unhandledType}`,\n                );\n              }\n            }\n          })\n          .filter((c) => !!c),\n        status: status ?? fallbackStatus,\n        metadata: {\n          unstable_state: metadata?.unstable_state ?? null,\n          unstable_annotations: metadata?.unstable_annotations ?? [],\n          unstable_data: metadata?.unstable_data ?? [],\n          custom: metadata?.custom ?? {},\n          steps: metadata?.steps ?? [],\n        },\n      } satisfies ThreadAssistantMessage;\n\n    case \"user\":\n      return {\n        ...common,\n        role,\n        content: content.map((part): ThreadUserMessagePart => {\n          const type = part.type;\n          switch (type) {\n            case \"text\":\n            case \"image\":\n            case \"audio\":\n            case \"file\":\n              return part;\n\n            default: {\n              const unhandledType: \"tool-call\" | \"reasoning\" | \"source\" = type;\n              throw new Error(\n                `Unsupported user message part type: ${unhandledType}`,\n              );\n            }\n          }\n        }),\n        attachments: attachments ?? [],\n        metadata: {\n          custom: metadata?.custom ?? {},\n        },\n      } satisfies ThreadUserMessage;\n\n    case \"system\":\n      if (content.length !== 1 || content[0]!.type !== \"text\")\n        throw new Error(\n          \"System messages must have exactly one text message part.\",\n        );\n\n      return {\n        ...common,\n        role,\n        content: content as [TextMessagePart],\n        metadata: {\n          custom: metadata?.custom ?? {},\n        },\n      } satisfies ThreadSystemMessage;\n\n    default: {\n      const unsupportedRole: never = role;\n      throw new Error(`Unknown message role: ${unsupportedRole}`);\n    }\n  }\n};\n"], "mappings": ";AAAA,SAAS,8BAA8B;AACvC,SAAS,kBAAkB;AA8DpB,IAAM,wBAAwB,CACnC,MACA,YACA,mBACkB;AAClB,QAAM,EAAE,MAAM,IAAI,WAAW,aAAa,QAAQ,SAAS,IAAI;AAC/D,QAAM,SAAS;AAAA,IACb,IAAI,MAAM;AAAA,IACV,WAAW,aAAa,oBAAI,KAAK;AAAA,EACnC;AAEA,QAAM,UACJ,OAAO,KAAK,YAAY,WACpB,CAAC,EAAE,MAAM,QAAiB,MAAM,KAAK,QAAQ,CAAC,IAC9C,KAAK;AAEX,QAAM,uBAAuB,CAAC;AAAA,IAC5B;AAAA,IACA,GAAG;AAAA,EACL,MAAiD;AAC/C,UAAM,QAAQ,MAAM;AAAA,MAClB;AAAA,IACF;AACA,QAAI,OAAO;AACT,aAAO,EAAE,GAAG,MAAM,MAAM;AAAA,IAC1B;AACA,YAAQ,KAAK,oCAAoC;AACjD,WAAO;AAAA,EACT;AAEA,MAAI,SAAS,UAAU,aAAa;AAClC,UAAM,IAAI,MAAM,kDAAkD;AAEpE,MAAI,SAAS,eAAe;AAC1B,UAAM,IAAI,MAAM,iDAAiD;AAEnE,MAAI,SAAS,eAAe,UAAU;AACpC,UAAM,IAAI,MAAM,yDAAyD;AAE3E,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,aAAO;AAAA,QACL,GAAG;AAAA,QACH;AAAA,QACA,SAAS,QACN,IAAI,CAAC,SAA4C;AAChD,gBAAM,OAAO,KAAK;AAClB,kBAAQ,MAAM;AAAA,YACZ,KAAK;AAAA,YACL,KAAK;AACH,kBAAI,KAAK,KAAK,KAAK,EAAE,WAAW,EAAG,QAAO;AAC1C,qBAAO;AAAA,YAET,KAAK;AAAA,YACL,KAAK;AACH,qBAAO;AAAA,YAET,KAAK;AACH,qBAAO,qBAAqB,IAAI;AAAA,YAElC,KAAK,aAAa;AAChB,oBAAM,EAAE,UAAU,GAAG,SAAS,IAAI;AAClC,oBAAM,cAAc;AAAA,gBAClB,GAAG;AAAA,gBACH,YAAY,KAAK,cAAc,UAAU,WAAW;AAAA,gBACpD,GAAI,aAAa,UAAa,EAAE,SAAS;AAAA,cAC3C;AAEA,kBAAI,KAAK,MAAM;AACb,uBAAO;AAAA,kBACL,GAAG;AAAA,kBACH,MAAM,KAAK;AAAA,kBACX,UAAU,KAAK,UAAU,KAAK,IAAI;AAAA,gBACpC;AAAA,cACF;AACA,qBAAO;AAAA,gBACL,GAAG;AAAA,gBACH,MACE,KAAK,QACL,uBAAuB,KAAK,YAAY,EAAE,KAC1C,CAAC;AAAA,gBACH,UAAU,KAAK,YAAY;AAAA,cAC7B;AAAA,YACF;AAAA,YAEA,SAAS;AACP,oBAAM,gBAAyB;AAC/B,oBAAM,IAAI;AAAA,gBACR,4CAA4C,aAAa;AAAA,cAC3D;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC,EACA,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;AAAA,QACpB,QAAQ,UAAU;AAAA,QAClB,UAAU;AAAA,UACR,gBAAgB,UAAU,kBAAkB;AAAA,UAC5C,sBAAsB,UAAU,wBAAwB,CAAC;AAAA,UACzD,eAAe,UAAU,iBAAiB,CAAC;AAAA,UAC3C,QAAQ,UAAU,UAAU,CAAC;AAAA,UAC7B,OAAO,UAAU,SAAS,CAAC;AAAA,QAC7B;AAAA,MACF;AAAA,IAEF,KAAK;AACH,aAAO;AAAA,QACL,GAAG;AAAA,QACH;AAAA,QACA,SAAS,QAAQ,IAAI,CAAC,SAAgC;AACpD,gBAAM,OAAO,KAAK;AAClB,kBAAQ,MAAM;AAAA,YACZ,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AACH,qBAAO;AAAA,YAET,SAAS;AACP,oBAAM,gBAAsD;AAC5D,oBAAM,IAAI;AAAA,gBACR,uCAAuC,aAAa;AAAA,cACtD;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AAAA,QACD,aAAa,eAAe,CAAC;AAAA,QAC7B,UAAU;AAAA,UACR,QAAQ,UAAU,UAAU,CAAC;AAAA,QAC/B;AAAA,MACF;AAAA,IAEF,KAAK;AACH,UAAI,QAAQ,WAAW,KAAK,QAAQ,CAAC,EAAG,SAAS;AAC/C,cAAM,IAAI;AAAA,UACR;AAAA,QACF;AAEF,aAAO;AAAA,QACL,GAAG;AAAA,QACH;AAAA,QACA;AAAA,QACA,UAAU;AAAA,UACR,QAAQ,UAAU,UAAU,CAAC;AAAA,QAC/B;AAAA,MACF;AAAA,IAEF,SAAS;AACP,YAAM,kBAAyB;AAC/B,YAAM,IAAI,MAAM,yBAAyB,eAAe,EAAE;AAAA,IAC5D;AAAA,EACF;AACF;", "names": []}