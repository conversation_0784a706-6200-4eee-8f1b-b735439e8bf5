{"version": 3, "sources": ["../../../src/primitives/composer/ComposerAttachmentDropzone.tsx"], "sourcesContent": ["import { forwardRef, useCallback, useState } from \"react\";\n\nimport { Slot } from \"@radix-ui/react-slot\";\nimport React from \"react\";\nimport { useComposerRuntime } from \"../../context\";\n\nexport namespace ComposerAttachmentDropzonePrimitive {\n  export type Element = HTMLDivElement;\n  export type Props = React.HTMLAttributes<HTMLDivElement> & {\n    asChild?: boolean | undefined;\n    disabled?: boolean | undefined;\n  };\n}\n\nexport const ComposerAttachmentDropzone = forwardRef<\n  HTMLDivElement,\n  ComposerAttachmentDropzonePrimitive.Props\n>(({ disabled, asChild = false, children, ...rest }, ref) => {\n  const [isDragging, setIsDragging] = useState(false);\n  const composerRuntime = useComposerRuntime();\n\n  const handleDrag = useCallback(\n    (e: React.DragEvent) => {\n      if (disabled) return;\n      e.preventDefault();\n      e.stopPropagation();\n      setIsDragging(e.type === \"dragenter\" || e.type === \"dragover\");\n    },\n    [disabled],\n  );\n\n  const handleDrop = useCallback(\n    async (e: React.DragEvent) => {\n      if (disabled) return;\n      e.preventDefault();\n      e.stopPropagation();\n      setIsDragging(false);\n      for (const file of e.dataTransfer.files) {\n        try {\n          await composerRuntime.addAttachment(file);\n        } catch (error) {\n          console.error(\"Failed to add attachment:\", error);\n        }\n      }\n    },\n    [disabled, composerRuntime],\n  );\n\n  const dragProps = {\n    onDragEnter: handleDrag,\n    onDragOver: handleDrag,\n    onDragLeave: handleDrag,\n    onDrop: handleDrop,\n  };\n\n  const Comp = asChild ? Slot : \"div\";\n\n  return (\n    <Comp\n      {...(isDragging ? { \"data-dragging\": \"true\" } : null)}\n      ref={ref}\n      {...dragProps}\n      {...rest}\n    >\n      {children}\n    </Comp>\n  );\n});\n\nComposerAttachmentDropzone.displayName = \"ComposerPrimitive.AttachmentDropzone\";\n"], "mappings": ";AAAA,SAAS,YAAY,aAAa,gBAAgB;AAElD,SAAS,YAAY;AAErB,SAAS,0BAA0B;AAsD/B;AA5CG,IAAM,6BAA6B,WAGxC,CAAC,EAAE,UAAU,UAAU,OAAO,UAAU,GAAG,KAAK,GAAG,QAAQ;AAC3D,QAAM,CAAC,YAAY,aAAa,IAAI,SAAS,KAAK;AAClD,QAAM,kBAAkB,mBAAmB;AAE3C,QAAM,aAAa;AAAA,IACjB,CAAC,MAAuB;AACtB,UAAI,SAAU;AACd,QAAE,eAAe;AACjB,QAAE,gBAAgB;AAClB,oBAAc,EAAE,SAAS,eAAe,EAAE,SAAS,UAAU;AAAA,IAC/D;AAAA,IACA,CAAC,QAAQ;AAAA,EACX;AAEA,QAAM,aAAa;AAAA,IACjB,OAAO,MAAuB;AAC5B,UAAI,SAAU;AACd,QAAE,eAAe;AACjB,QAAE,gBAAgB;AAClB,oBAAc,KAAK;AACnB,iBAAW,QAAQ,EAAE,aAAa,OAAO;AACvC,YAAI;AACF,gBAAM,gBAAgB,cAAc,IAAI;AAAA,QAC1C,SAAS,OAAO;AACd,kBAAQ,MAAM,6BAA6B,KAAK;AAAA,QAClD;AAAA,MACF;AAAA,IACF;AAAA,IACA,CAAC,UAAU,eAAe;AAAA,EAC5B;AAEA,QAAM,YAAY;AAAA,IAChB,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAEA,QAAM,OAAO,UAAU,OAAO;AAE9B,SACE;AAAA,IAAC;AAAA;AAAA,MACE,GAAI,aAAa,EAAE,iBAAiB,OAAO,IAAI;AAAA,MAChD;AAAA,MACC,GAAG;AAAA,MACH,GAAG;AAAA,MAEH;AAAA;AAAA,EACH;AAEJ,CAAC;AAED,2BAA2B,cAAc;", "names": []}