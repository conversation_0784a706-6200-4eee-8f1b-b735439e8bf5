{"version": 3, "sources": ["../../../src/primitives/messagePart/MessagePartImage.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { type ComponentRef, forwardRef, ComponentPropsWithoutRef } from \"react\";\nimport { useMessagePartImage } from \"./useMessagePartImage\";\n\nexport namespace MessagePartPrimitiveImage {\n  export type Element = ComponentRef<typeof Primitive.img>;\n  /**\n   * Props for the MessagePartPrimitive.Image component.\n   * Accepts all standard img element props.\n   */\n  export type Props = ComponentPropsWithoutRef<typeof Primitive.img>;\n}\n\n/**\n * Renders an image from the current message part context.\n *\n * This component displays image content from the current message part,\n * automatically setting the src attribute from the message part's image data.\n *\n * @example\n * ```tsx\n * <MessagePartPrimitive.Image\n *   alt=\"Generated image\"\n *   className=\"message-image\"\n *   style={{ maxWidth: '100%' }}\n * />\n * ```\n */\nexport const MessagePartPrimitiveImage = forwardRef<\n  MessagePartPrimitiveImage.Element,\n  MessagePartPrimitiveImage.Props\n>((props, forwardedRef) => {\n  const { image } = useMessagePartImage();\n  return <Primitive.img src={image} {...props} ref={forwardedRef} />;\n});\n\nMessagePartPrimitiveImage.displayName = \"MessagePartPrimitive.Image\";\n"], "mappings": ";;;AAEA,SAAS,iBAAiB;AAC1B,SAA4B,kBAA4C;AACxE,SAAS,2BAA2B;AA+B3B;AALF,IAAM,4BAA4B,WAGvC,CAAC,OAAO,iBAAiB;AACzB,QAAM,EAAE,MAAM,IAAI,oBAAoB;AACtC,SAAO,oBAAC,UAAU,KAAV,EAAc,KAAK,OAAQ,GAAG,OAAO,KAAK,cAAc;AAClE,CAAC;AAED,0BAA0B,cAAc;", "names": []}