{"version": 3, "file": "ExternalStoreThreadRuntimeCore.d.ts", "sourceRoot": "", "sources": ["../../../src/runtimes/external-store/ExternalStoreThreadRuntimeCore.tsx"], "names": [], "mappings": "AAAA,OAAO,EACL,oBAAoB,EACpB,cAAc,EACd,gBAAgB,EACjB,MAAM,2BAA2B,CAAC;AAEnC,OAAO,EAAE,aAAa,EAAE,aAAa,EAAE,MAAM,aAAa,CAAC;AAC3D,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAS9D,OAAO,EACL,mBAAmB,EACnB,iBAAiB,EAClB,MAAM,2BAA2B,CAAC;AACnC,OAAO,EAAE,qBAAqB,EAAE,MAAM,+BAA+B,CAAC;AACtE,OAAO,EAAE,oBAAoB,EAAE,MAAM,qBAAqB,CAAC;AAI3D,eAAO,MAAM,kBAAkB,GAC7B,WAAW,OAAO,EAClB,UAAU,SAAS,aAAa,EAAE,YAGnC,CAAC;AAEF,qBAAa,8BACX,SAAQ,qBACR,YAAW,iBAAiB;IAE5B,OAAO,CAAC,qBAAqB,CAAuB;IAEpD,OAAO,CAAC,aAAa,CASnB;IAEF,IAAW,YAAY,wBAEtB;IAED,OAAO,CAAC,SAAS,CAA4B;IACtC,UAAU,EAAG,OAAO,CAAC;IAC5B,IAAW,SAAS,YAEnB;IAED,IAAoB,QAAQ,6BAE3B;IAED,IAAW,QAAQ;;;;;kBAElB;IAEM,WAAW,EAAE,SAAS,gBAAgB,EAAE,CAAM;IAC9C,MAAM,EAAE,OAAO,CAAa;IAEnC,OAAO,CAAC,UAAU,CAAgC;IAElD,OAAO,CAAC,MAAM,CAA6B;IAE3B,SAAS,CAAC,SAAS,EAAE,MAAM;gBAQzC,eAAe,EAAE,oBAAoB,EACrC,KAAK,EAAE,oBAAoB,CAAC,GAAG,CAAC;IAM3B,qBAAqB,CAAC,KAAK,EAAE,oBAAoB,CAAC,GAAG,CAAC;IAgI7C,cAAc,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI;IAQzC,MAAM,CAAC,OAAO,EAAE,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC;IAU7C,QAAQ,CAAC,MAAM,EAAE,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC;IAO/C,SAAS,IAAI,OAAO,CAAC,IAAI,CAAC;IAIhC,SAAS,IAAI,IAAI;IAiCjB,aAAa,CAAC,OAAO,EAAE,oBAAoB;IAMlD,OAAO,CAAC,cAAc,CAUpB;CACH"}