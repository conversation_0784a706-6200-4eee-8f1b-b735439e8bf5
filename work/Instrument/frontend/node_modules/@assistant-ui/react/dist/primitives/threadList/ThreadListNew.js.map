{"version": 3, "sources": ["../../../src/primitives/threadList/ThreadListNew.tsx"], "sourcesContent": ["\"use client\";\n\nimport {\n  ActionButtonElement,\n  ActionButtonProps,\n} from \"../../utils/createActionButton\";\nimport { useAssistantRuntime, useThreadList } from \"../../context\";\nimport { forwardRef } from \"react\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\n\nconst useThreadListNew = () => {\n  const runtime = useAssistantRuntime();\n  return () => {\n    runtime.switchToNewThread();\n  };\n};\n\nexport namespace ThreadListPrimitiveNew {\n  export type Element = ActionButtonElement;\n  export type Props = ActionButtonProps<typeof useThreadListNew>;\n}\n\nexport const ThreadListPrimitiveNew = forwardRef<\n  ThreadListPrimitiveNew.Element,\n  ThreadListPrimitiveNew.Props\n>(({ onClick, disabled, ...props }, forwardedRef) => {\n  const isMain = useThreadList((t) => t.newThread === t.mainThreadId);\n  const callback = useThreadListNew();\n\n  return (\n    <Primitive.button\n      type=\"button\"\n      {...(isMain ? { \"data-active\": \"true\", \"aria-current\": \"true\" } : null)}\n      {...props}\n      ref={forwardedRef}\n      disabled={disabled || !callback}\n      onClick={composeEventHandlers(onClick, () => {\n        callback?.();\n      })}\n    />\n  );\n});\n\nThreadListPrimitiveNew.displayName = \"ThreadListPrimitive.New\";\n"], "mappings": ";;;AAMA,SAAS,qBAAqB,qBAAqB;AACnD,SAAS,kBAAkB;AAC3B,SAAS,iBAAiB;AAC1B,SAAS,4BAA4B;AAsBjC;AApBJ,IAAM,mBAAmB,MAAM;AAC7B,QAAM,UAAU,oBAAoB;AACpC,SAAO,MAAM;AACX,YAAQ,kBAAkB;AAAA,EAC5B;AACF;AAOO,IAAM,yBAAyB,WAGpC,CAAC,EAAE,SAAS,UAAU,GAAG,MAAM,GAAG,iBAAiB;AACnD,QAAM,SAAS,cAAc,CAAC,MAAM,EAAE,cAAc,EAAE,YAAY;AAClE,QAAM,WAAW,iBAAiB;AAElC,SACE;AAAA,IAAC,UAAU;AAAA,IAAV;AAAA,MACC,MAAK;AAAA,MACJ,GAAI,SAAS,EAAE,eAAe,QAAQ,gBAAgB,OAAO,IAAI;AAAA,MACjE,GAAG;AAAA,MACJ,KAAK;AAAA,MACL,UAAU,YAAY,CAAC;AAAA,MACvB,SAAS,qBAAqB,SAAS,MAAM;AAC3C,mBAAW;AAAA,MACb,CAAC;AAAA;AAAA,EACH;AAEJ,CAAC;AAED,uBAAuB,cAAc;", "names": []}