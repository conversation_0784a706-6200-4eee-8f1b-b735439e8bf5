{"version": 3, "sources": ["../../../../src/runtimes/remote-thread-list/adapter/cloud.tsx"], "sourcesContent": ["\"use client\";\n\nimport {\n  FC,\n  PropsWithChildren,\n  useCallback,\n  useEffect,\n  useMemo,\n  useRef,\n} from \"react\";\nimport { AssistantCloud } from \"assistant-cloud\";\nimport { RemoteThreadListAdapter } from \"../types\";\nimport { useAssistantCloudThreadHistoryAdapter } from \"../../../cloud/AssistantCloudThreadHistoryAdapter\";\nimport { RuntimeAdapterProvider } from \"../../adapters/RuntimeAdapterProvider\";\nimport { InMemoryThreadListAdapter } from \"./in-memory\";\n\ntype ThreadData = {\n  externalId: string;\n};\n\ntype CloudThreadListAdapterOptions = {\n  cloud?: AssistantCloud | undefined;\n\n  create?(): Promise<ThreadData>;\n  delete?(threadId: string): Promise<void>;\n};\n\nconst baseUrl =\n  typeof process !== \"undefined\" &&\n  process?.env?.[\"NEXT_PUBLIC_ASSISTANT_BASE_URL\"];\nconst autoCloud = baseUrl\n  ? new AssistantCloud({ baseUrl, anonymous: true })\n  : undefined;\n\nexport const useCloudThreadListAdapter = (\n  adapter: CloudThreadListAdapterOptions,\n): RemoteThreadListAdapter => {\n  const adapterRef = useRef(adapter);\n  useEffect(() => {\n    adapterRef.current = adapter;\n  }, [adapter]);\n\n  const unstable_Provider = useCallback<FC<PropsWithChildren>>(\n    function Provider({ children }) {\n      const history = useAssistantCloudThreadHistoryAdapter({\n        get current() {\n          return adapterRef.current.cloud ?? autoCloud!;\n        },\n      });\n      const adapters = useMemo(() => ({ history }), [history]);\n\n      return (\n        <RuntimeAdapterProvider adapters={adapters}>\n          {children}\n        </RuntimeAdapterProvider>\n      );\n    },\n    [],\n  );\n\n  const cloud = adapter.cloud ?? autoCloud;\n  if (!cloud) return new InMemoryThreadListAdapter();\n\n  return {\n    list: async () => {\n      const { threads } = await cloud.threads.list();\n      return {\n        threads: threads.map((t) => ({\n          status: t.is_archived ? \"archived\" : \"regular\",\n          remoteId: t.id,\n          title: t.title,\n          externalId: t.external_id ?? undefined,\n        })),\n      };\n    },\n\n    initialize: async () => {\n      const createTask = adapter.create?.() ?? Promise.resolve();\n      const t = await createTask;\n      const external_id = t ? t.externalId : undefined;\n      const { thread_id: remoteId } = await cloud.threads.create({\n        last_message_at: new Date(),\n        external_id,\n      });\n\n      return { externalId: external_id, remoteId: remoteId };\n    },\n\n    rename: async (threadId, newTitle) => {\n      return cloud.threads.update(threadId, { title: newTitle });\n    },\n    archive: async (threadId) => {\n      return cloud.threads.update(threadId, { is_archived: true });\n    },\n    unarchive: async (threadId) => {\n      return cloud.threads.update(threadId, { is_archived: false });\n    },\n    delete: async (threadId) => {\n      await adapter.delete?.(threadId);\n      return cloud.threads.delete(threadId);\n    },\n\n    generateTitle: async (threadId, messages) => {\n      return cloud.runs.stream({\n        thread_id: threadId,\n        assistant_id: \"system/thread_title\",\n        messages: messages, // TODO serialize these to a more efficient format\n      });\n    },\n\n    unstable_Provider,\n  };\n};\n"], "mappings": ";;;AAEA;AAAA,EAGE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP,SAAS,sBAAsB;AAE/B,SAAS,6CAA6C;AACtD,SAAS,8BAA8B;AACvC,SAAS,iCAAiC;AAsClC;AAzBR,IAAM,UACJ,OAAO,YAAY,eACnB,SAAS,MAAM,gCAAgC;AACjD,IAAM,YAAY,UACd,IAAI,eAAe,EAAE,SAAS,WAAW,KAAK,CAAC,IAC/C;AAEG,IAAM,4BAA4B,CACvC,YAC4B;AAC5B,QAAM,aAAa,OAAO,OAAO;AACjC,YAAU,MAAM;AACd,eAAW,UAAU;AAAA,EACvB,GAAG,CAAC,OAAO,CAAC;AAEZ,QAAM,oBAAoB;AAAA,IACxB,SAAS,SAAS,EAAE,SAAS,GAAG;AAC9B,YAAM,UAAU,sCAAsC;AAAA,QACpD,IAAI,UAAU;AACZ,iBAAO,WAAW,QAAQ,SAAS;AAAA,QACrC;AAAA,MACF,CAAC;AACD,YAAM,WAAW,QAAQ,OAAO,EAAE,QAAQ,IAAI,CAAC,OAAO,CAAC;AAEvD,aACE,oBAAC,0BAAuB,UACrB,UACH;AAAA,IAEJ;AAAA,IACA,CAAC;AAAA,EACH;AAEA,QAAM,QAAQ,QAAQ,SAAS;AAC/B,MAAI,CAAC,MAAO,QAAO,IAAI,0BAA0B;AAEjD,SAAO;AAAA,IACL,MAAM,YAAY;AAChB,YAAM,EAAE,QAAQ,IAAI,MAAM,MAAM,QAAQ,KAAK;AAC7C,aAAO;AAAA,QACL,SAAS,QAAQ,IAAI,CAAC,OAAO;AAAA,UAC3B,QAAQ,EAAE,cAAc,aAAa;AAAA,UACrC,UAAU,EAAE;AAAA,UACZ,OAAO,EAAE;AAAA,UACT,YAAY,EAAE,eAAe;AAAA,QAC/B,EAAE;AAAA,MACJ;AAAA,IACF;AAAA,IAEA,YAAY,YAAY;AACtB,YAAM,aAAa,QAAQ,SAAS,KAAK,QAAQ,QAAQ;AACzD,YAAM,IAAI,MAAM;AAChB,YAAM,cAAc,IAAI,EAAE,aAAa;AACvC,YAAM,EAAE,WAAW,SAAS,IAAI,MAAM,MAAM,QAAQ,OAAO;AAAA,QACzD,iBAAiB,oBAAI,KAAK;AAAA,QAC1B;AAAA,MACF,CAAC;AAED,aAAO,EAAE,YAAY,aAAa,SAAmB;AAAA,IACvD;AAAA,IAEA,QAAQ,OAAO,UAAU,aAAa;AACpC,aAAO,MAAM,QAAQ,OAAO,UAAU,EAAE,OAAO,SAAS,CAAC;AAAA,IAC3D;AAAA,IACA,SAAS,OAAO,aAAa;AAC3B,aAAO,MAAM,QAAQ,OAAO,UAAU,EAAE,aAAa,KAAK,CAAC;AAAA,IAC7D;AAAA,IACA,WAAW,OAAO,aAAa;AAC7B,aAAO,MAAM,QAAQ,OAAO,UAAU,EAAE,aAAa,MAAM,CAAC;AAAA,IAC9D;AAAA,IACA,QAAQ,OAAO,aAAa;AAC1B,YAAM,QAAQ,SAAS,QAAQ;AAC/B,aAAO,MAAM,QAAQ,OAAO,QAAQ;AAAA,IACtC;AAAA,IAEA,eAAe,OAAO,UAAU,aAAa;AAC3C,aAAO,MAAM,KAAK,OAAO;AAAA,QACvB,WAAW;AAAA,QACX,cAAc;AAAA,QACd;AAAA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IAEA;AAAA,EACF;AACF;", "names": []}