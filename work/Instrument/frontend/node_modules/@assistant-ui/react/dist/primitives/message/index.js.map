{"version": 3, "sources": ["../../../src/primitives/message/index.ts"], "sourcesContent": ["export { MessagePrimitiveRoot as Root } from \"./MessageRoot\";\nexport { MessagePrimitiveParts as Parts } from \"./MessageParts\";\nexport { MessagePrimitivePartByIndex as PartByIndex } from \"./MessageParts\";\nexport { MessagePrimitiveParts as Content } from \"./MessageParts\";\nexport { MessagePrimitiveIf as If } from \"./MessageIf\";\nexport { MessagePrimitiveAttachments as Attachments } from \"./MessageAttachments\";\nexport { MessagePrimitiveAttachmentByIndex as AttachmentByIndex } from \"./MessageAttachments\";\nexport { MessagePrimitiveError as Error } from \"./MessageError\";\nexport {\n  MessagePrimitiveUnstable_PartsGrouped as Unstable_PartsGrouped,\n  MessagePrimitiveUnstable_PartsGroupedByParentId as Unstable_PartsGroupedByParentId,\n} from \"./MessagePartsGrouped\";\n"], "mappings": ";AAAA,SAAiC,4BAAY;AAC7C,SAAkC,6BAAa;AAC/C,SAAwC,mCAAmB;AAC3D,SAAkC,yBAAzBA,8BAAwC;AACjD,SAA+B,0BAAU;AACzC,SAAwC,mCAAmB;AAC3D,SAA8C,yCAAyB;AACvE,SAAkC,6BAAa;AAC/C;AAAA,EAC2C;AAAA,EACU;AAAA,OAC9C;", "names": ["MessagePrimitiveParts"]}