import { ActionButtonElement, ActionButtonProps } from "../../utils/createActionButton";
declare const useComposerAddAttachment: ({ multiple, }?: {
    /** allow selecting multiple files */
    multiple?: boolean | undefined;
}) => (() => void) | null;
export declare namespace ComposerPrimitiveAddAttachment {
    type Element = ActionButtonElement;
    type Props = ActionButtonProps<typeof useComposerAddAttachment>;
}
export declare const ComposerPrimitiveAddAttachment: import("react").ForwardRefExoticComponent<Omit<import("react").ClassAttributes<HTMLButtonElement> & import("react").ButtonHTMLAttributes<HTMLButtonElement> & {
    asChild?: boolean;
}, "ref"> & {
    /** allow selecting multiple files */
    multiple?: boolean | undefined;
} & import("react").RefAttributes<HTMLButtonElement>>;
export {};
//# sourceMappingURL=ComposerAddAttachment.d.ts.map