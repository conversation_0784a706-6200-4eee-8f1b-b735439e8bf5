{"version": 3, "sources": ["../../../src/runtimes/remote-thread-list/EMPTY_THREAD_CORE.tsx"], "sourcesContent": ["import { ThreadRuntimeCore } from \"../../internal\";\n\nconst EMPTY_THREAD_ERROR = new Error(\n  \"This is the empty thread, a placeholder for the main thread. You cannot perform any actions on this thread instance. This error is probably because you tried to call a thread method in your render function. Call the method inside a `useEffect` hook instead.\",\n);\nexport const EMPTY_THREAD_CORE: ThreadRuntimeCore = {\n  getMessageById() {\n    return undefined;\n  },\n\n  getBranches() {\n    return [];\n  },\n\n  switchToBranch() {\n    throw EMPTY_THREAD_ERROR;\n  },\n\n  append() {\n    throw EMPTY_THREAD_ERROR;\n  },\n\n  startRun() {\n    throw EMPTY_THREAD_ERROR;\n  },\n\n  resumeRun() {\n    throw EMPTY_THREAD_ERROR;\n  },\n\n  cancelRun() {\n    throw EMPTY_THREAD_ERROR;\n  },\n\n  addToolResult() {\n    throw EMPTY_THREAD_ERROR;\n  },\n\n  speak() {\n    throw EMPTY_THREAD_ERROR;\n  },\n\n  stopSpeaking() {\n    throw EMPTY_THREAD_ERROR;\n  },\n\n  getSubmittedFeedback() {\n    return undefined;\n  },\n\n  submitFeedback() {\n    throw EMPTY_THREAD_ERROR;\n  },\n\n  getModelContext() {\n    return {};\n  },\n\n  composer: {\n    attachments: [],\n\n    getAttachmentAccept() {\n      return \"*\";\n    },\n\n    async addAttachment() {\n      throw EMPTY_THREAD_ERROR;\n    },\n\n    async removeAttachment() {\n      throw EMPTY_THREAD_ERROR;\n    },\n\n    isEditing: false,\n\n    canCancel: false,\n    isEmpty: true,\n\n    text: \"\",\n\n    setText() {\n      throw EMPTY_THREAD_ERROR;\n    },\n\n    role: \"user\",\n\n    setRole() {\n      throw EMPTY_THREAD_ERROR;\n    },\n\n    runConfig: {},\n\n    setRunConfig() {\n      throw EMPTY_THREAD_ERROR;\n    },\n\n    async reset() {\n      // noop\n    },\n\n    async clearAttachments() {\n      // noop\n    },\n\n    send() {\n      throw EMPTY_THREAD_ERROR;\n    },\n\n    cancel() {\n      // noop\n    },\n\n    subscribe() {\n      return () => {};\n    },\n\n    unstable_on() {\n      return () => {};\n    },\n  },\n\n  getEditComposer() {\n    return undefined;\n  },\n\n  beginEdit() {\n    throw EMPTY_THREAD_ERROR;\n  },\n\n  speech: undefined,\n\n  capabilities: {\n    switchToBranch: false,\n    edit: false,\n    reload: false,\n    cancel: false,\n    unstable_copy: false,\n    speech: false,\n    attachments: false,\n    feedback: false,\n  },\n\n  isDisabled: true,\n  isLoading: false,\n\n  messages: [],\n\n  state: null,\n\n  suggestions: [],\n\n  extras: undefined,\n\n  subscribe() {\n    return () => {};\n  },\n\n  import() {\n    throw EMPTY_THREAD_ERROR;\n  },\n\n  export() {\n    return { messages: [] };\n  },\n\n  reset() {\n    throw EMPTY_THREAD_ERROR;\n  },\n\n  unstable_on() {\n    return () => {};\n  },\n};\n"], "mappings": ";AAEA,IAAM,qBAAqB,IAAI;AAAA,EAC7B;AACF;AACO,IAAM,oBAAuC;AAAA,EAClD,iBAAiB;AACf,WAAO;AAAA,EACT;AAAA,EAEA,cAAc;AACZ,WAAO,CAAC;AAAA,EACV;AAAA,EAEA,iBAAiB;AACf,UAAM;AAAA,EACR;AAAA,EAEA,SAAS;AACP,UAAM;AAAA,EACR;AAAA,EAEA,WAAW;AACT,UAAM;AAAA,EACR;AAAA,EAEA,YAAY;AACV,UAAM;AAAA,EACR;AAAA,EAEA,YAAY;AACV,UAAM;AAAA,EACR;AAAA,EAEA,gBAAgB;AACd,UAAM;AAAA,EACR;AAAA,EAEA,QAAQ;AACN,UAAM;AAAA,EACR;AAAA,EAEA,eAAe;AACb,UAAM;AAAA,EACR;AAAA,EAEA,uBAAuB;AACrB,WAAO;AAAA,EACT;AAAA,EAEA,iBAAiB;AACf,UAAM;AAAA,EACR;AAAA,EAEA,kBAAkB;AAChB,WAAO,CAAC;AAAA,EACV;AAAA,EAEA,UAAU;AAAA,IACR,aAAa,CAAC;AAAA,IAEd,sBAAsB;AACpB,aAAO;AAAA,IACT;AAAA,IAEA,MAAM,gBAAgB;AACpB,YAAM;AAAA,IACR;AAAA,IAEA,MAAM,mBAAmB;AACvB,YAAM;AAAA,IACR;AAAA,IAEA,WAAW;AAAA,IAEX,WAAW;AAAA,IACX,SAAS;AAAA,IAET,MAAM;AAAA,IAEN,UAAU;AACR,YAAM;AAAA,IACR;AAAA,IAEA,MAAM;AAAA,IAEN,UAAU;AACR,YAAM;AAAA,IACR;AAAA,IAEA,WAAW,CAAC;AAAA,IAEZ,eAAe;AACb,YAAM;AAAA,IACR;AAAA,IAEA,MAAM,QAAQ;AAAA,IAEd;AAAA,IAEA,MAAM,mBAAmB;AAAA,IAEzB;AAAA,IAEA,OAAO;AACL,YAAM;AAAA,IACR;AAAA,IAEA,SAAS;AAAA,IAET;AAAA,IAEA,YAAY;AACV,aAAO,MAAM;AAAA,MAAC;AAAA,IAChB;AAAA,IAEA,cAAc;AACZ,aAAO,MAAM;AAAA,MAAC;AAAA,IAChB;AAAA,EACF;AAAA,EAEA,kBAAkB;AAChB,WAAO;AAAA,EACT;AAAA,EAEA,YAAY;AACV,UAAM;AAAA,EACR;AAAA,EAEA,QAAQ;AAAA,EAER,cAAc;AAAA,IACZ,gBAAgB;AAAA,IAChB,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,UAAU;AAAA,EACZ;AAAA,EAEA,YAAY;AAAA,EACZ,WAAW;AAAA,EAEX,UAAU,CAAC;AAAA,EAEX,OAAO;AAAA,EAEP,aAAa,CAAC;AAAA,EAEd,QAAQ;AAAA,EAER,YAAY;AACV,WAAO,MAAM;AAAA,IAAC;AAAA,EAChB;AAAA,EAEA,SAAS;AACP,UAAM;AAAA,EACR;AAAA,EAEA,SAAS;AACP,WAAO,EAAE,UAAU,CAAC,EAAE;AAAA,EACxB;AAAA,EAEA,QAAQ;AACN,UAAM;AAAA,EACR;AAAA,EAEA,cAAc;AACZ,WAAO,MAAM;AAAA,IAAC;AAAA,EAChB;AACF;", "names": []}