{"version": 3, "sources": ["../../../src/primitives/error/ErrorRoot.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { type ComponentRef, forwardRef, ComponentPropsWithoutRef } from \"react\";\n\nexport namespace ErrorPrimitiveRoot {\n  export type Element = ComponentRef<typeof Primitive.div>;\n  export type Props = ComponentPropsWithoutRef<typeof Primitive.div>;\n}\n\nexport const ErrorPrimitiveRoot = forwardRef<\n  ErrorPrimitiveRoot.Element,\n  ErrorPrimitiveRoot.Props\n>((props, forwardRef) => {\n  return <Primitive.div role=\"alert\" {...props} ref={forwardRef} />;\n});\n\nErrorPrimitiveRoot.displayName = \"ErrorPrimitive.Root\";\n"], "mappings": ";;;AAEA,SAAS,iBAAiB;AAC1B,SAA4B,kBAA4C;AAW/D;AAJF,IAAM,qBAAqB,WAGhC,CAAC,OAAOA,gBAAe;AACvB,SAAO,oBAAC,UAAU,KAAV,EAAc,MAAK,SAAS,GAAG,OAAO,KAAKA,aAAY;AACjE,CAAC;AAED,mBAAmB,cAAc;", "names": ["forwardRef"]}