{"version": 3, "sources": ["../../../src/primitives/branchPicker/BranchPickerNumber.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { FC } from \"react\";\nimport { useMessage } from \"../../context/react/MessageContext\";\n\nconst useBranchPickerNumber = () => {\n  const branchNumber = useMessage((s) => s.branchNumber);\n  return branchNumber;\n};\n\nexport namespace BranchPickerPrimitiveNumber {\n  export type Props = Record<string, never>;\n}\n\nexport const BranchPickerPrimitiveNumber: FC<\n  BranchPickerPrimitiveNumber.Props\n> = () => {\n  const branchNumber = useBranchPickerNumber();\n  return <>{branchNumber}</>;\n};\n\nBranchPickerPrimitiveNumber.displayName = \"BranchPickerPrimitive.Number\";\n"], "mappings": ";;;AAGA,SAAS,kBAAkB;AAelB;AAbT,IAAM,wBAAwB,MAAM;AAClC,QAAM,eAAe,WAAW,CAAC,MAAM,EAAE,YAAY;AACrD,SAAO;AACT;AAMO,IAAM,8BAET,MAAM;AACR,QAAM,eAAe,sBAAsB;AAC3C,SAAO,gCAAG,wBAAa;AACzB;AAEA,4BAA4B,cAAc;", "names": []}