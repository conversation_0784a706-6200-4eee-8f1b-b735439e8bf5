import { useState, useEffect } from 'react'
import { AssistantRuntimeProvider } from "@assistant-ui/react"
import { InstrumentRuntime } from "./lib/instrument-runtime"
import { Thread } from "./components/assistant-ui/thread"
import { ThreadList } from "./components/assistant-ui/thread-list"
import { ImagePreview } from "./components/assistant-ui/image-preview"
import './styles/instrument-ui.css'

function App() {
  // State management
  const [connectionStatus, setConnectionStatus] = useState(false)
  const [deviceInfo, setDeviceInfo] = useState(null)
  const [images, setImages] = useState([])
  const [runtime, setRuntime] = useState(null)

  // Backend configuration
  const BACKEND_URL = 'http://localhost:3000'
  const WS_URL = 'ws://localhost:8081'

  // Initialize runtime
  useEffect(() => {
    const instrumentRuntime = new InstrumentRuntime({
      backendUrl: BACKEND_URL,
      wsUrl: WS_URL,
      onDeviceStatusChange: (connected, deviceInfo) => {
        setConnectionStatus(connected)
        setDeviceInfo(deviceInfo)
      },
      onImagesReceived: (images) => {
        setImages(images)
      }
    })

    setRuntime(instrumentRuntime)

    // Cleanup on unmount
    return () => {
      instrumentRuntime.destroy()
    }
  }, [])

  // Handler functions for UI actions
  const handleCapture = () => {
    if (runtime) {
      runtime.captureImages(1)
    }
  }

  const handleSend = () => {
    if (runtime) {
      runtime.sendToDevice()
    }
  }

  if (!runtime) {
    return (
      <div className="instrument-app">
        <div className="status-bar">
          <span>Loading...</span>
        </div>
      </div>
    )
  }

  return (
    <AssistantRuntimeProvider runtime={runtime}>
      <div className="instrument-app">
        {/* Connection Status */}
        <div className="status-bar">
          <span className={`status ${connectionStatus ? 'connected' : 'disconnected'}`}>
            Device: {connectionStatus ? 'Connected' : 'Disconnected'}
          </span>
          {deviceInfo && (
            <span className="device-info">
              ({deviceInfo.name} - {deviceInfo.host}:{deviceInfo.port})
            </span>
          )}
        </div>

        {/* Main Layout */}
        <div className="instrument-layout">
          <ThreadList />
          <Thread
            onCapture={handleCapture}
            onSendToDevice={handleSend}
            deviceConnected={connectionStatus}
            imageCount={images.length}
          />
        </div>

        {/* Image Preview */}
        <ImagePreview
          images={images}
          className="absolute bottom-4 left-4 max-w-xs"
        />
      </div>
    </AssistantRuntimeProvider>
  )
}

export default App
