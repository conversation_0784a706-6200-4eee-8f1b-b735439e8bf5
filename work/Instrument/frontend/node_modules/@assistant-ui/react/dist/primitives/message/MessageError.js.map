{"version": 3, "sources": ["../../../src/primitives/message/MessageError.tsx"], "sourcesContent": ["\"use client\";\n\nimport { FC, PropsWithChildren } from \"react\";\nimport { useMessage } from \"../../context/react/MessageContext\";\n\nexport const MessagePrimitiveError: FC<PropsWithChildren> = ({ children }) => {\n  const hasError = useMessage(\n    (m) => m.status?.type === \"incomplete\" && m.status.reason === \"error\",\n  );\n  return hasError ? <>{children}</> : null;\n};\n\nMessagePrimitiveError.displayName = \"MessagePrimitive.Error\";\n"], "mappings": ";;;AAGA,SAAS,kBAAkB;AAMP;AAJb,IAAM,wBAA+C,CAAC,EAAE,SAAS,MAAM;AAC5E,QAAM,WAAW;AAAA,IACf,CAAC,MAAM,EAAE,QAAQ,SAAS,gBAAgB,EAAE,OAAO,WAAW;AAAA,EAChE;AACA,SAAO,WAAW,gCAAG,UAAS,IAAM;AACtC;AAEA,sBAAsB,cAAc;", "names": []}