{"version": 3, "sources": ["../../../src/primitives/message/MessageParts.tsx"], "sourcesContent": ["\"use client\";\n\nimport {\n  type ComponentType,\n  type FC,\n  memo,\n  PropsWithChildren,\n  useMemo,\n} from \"react\";\nimport {\n  TextMessagePartProvider,\n  useMessagePart,\n  useMessagePartRuntime,\n  useToolUIs,\n} from \"../../context\";\nimport {\n  useMessage,\n  useMessageRuntime,\n} from \"../../context/react/MessageContext\";\nimport { MessagePartRuntimeProvider } from \"../../context/providers/MessagePartRuntimeProvider\";\nimport { MessagePartPrimitiveText } from \"../messagePart/MessagePartText\";\nimport { MessagePartPrimitiveImage } from \"../messagePart/MessagePartImage\";\nimport type {\n  Unstable_AudioMessagePartComponent,\n  EmptyMessagePartComponent,\n  TextMessagePartComponent,\n  ImageMessagePartComponent,\n  SourceMessagePartComponent,\n  ToolCallMessagePartComponent,\n  ToolCallMessagePartProps,\n  FileMessagePartComponent,\n  ReasoningMessagePartComponent,\n} from \"../../types/MessagePartComponentTypes\";\nimport { MessagePartPrimitiveInProgress } from \"../messagePart/MessagePartInProgress\";\nimport { MessagePartStatus } from \"../../types/AssistantTypes\";\nimport { useShallow } from \"zustand/shallow\";\n\ntype MessagePartRange =\n  | { type: \"single\"; index: number }\n  | { type: \"toolGroup\"; startIndex: number; endIndex: number };\n\n/**\n * Groups consecutive tool-call message parts into ranges.\n * Always groups tool calls, even if there's only one.\n */\nconst groupMessageParts = (\n  messageTypes: readonly string[],\n): MessagePartRange[] => {\n  const ranges: MessagePartRange[] = [];\n  let currentToolGroupStart = -1;\n\n  for (let i = 0; i < messageTypes.length; i++) {\n    const type = messageTypes[i];\n\n    if (type === \"tool-call\") {\n      // Start a new tool group if we haven't started one\n      if (currentToolGroupStart === -1) {\n        currentToolGroupStart = i;\n      }\n    } else {\n      // End current tool group if it exists\n      if (currentToolGroupStart !== -1) {\n        ranges.push({\n          type: \"toolGroup\",\n          startIndex: currentToolGroupStart,\n          endIndex: i - 1,\n        });\n        currentToolGroupStart = -1;\n      }\n\n      // Add non-tool-call part individually\n      ranges.push({ type: \"single\", index: i });\n    }\n  }\n\n  // Handle any remaining tool group at the end\n  if (currentToolGroupStart !== -1) {\n    ranges.push({\n      type: \"toolGroup\",\n      startIndex: currentToolGroupStart,\n      endIndex: messageTypes.length - 1,\n    });\n  }\n\n  return ranges;\n};\n\nconst useMessagePartsGroups = (): MessagePartRange[] => {\n  const messageTypes = useMessage(\n    useShallow((m) => m.content.map((c) => c.type)),\n  );\n\n  return useMemo(() => {\n    if (messageTypes.length === 0) {\n      return [];\n    }\n    return groupMessageParts(messageTypes);\n  }, [messageTypes]);\n};\n\nexport namespace MessagePrimitiveParts {\n  export type Props = {\n    /**\n     * Component configuration for rendering different types of message content.\n     *\n     * You can provide custom components for each content type (text, image, file, etc.)\n     * and configure tool rendering behavior. If not provided, default components will be used.\n     */\n    components?:\n      | {\n          /** Component for rendering empty messages */\n          Empty?: EmptyMessagePartComponent | undefined;\n          /** Component for rendering text content */\n          Text?: TextMessagePartComponent | undefined;\n          /** Component for rendering reasoning content (typically hidden) */\n          Reasoning?: ReasoningMessagePartComponent | undefined;\n          /** Component for rendering source content */\n          Source?: SourceMessagePartComponent | undefined;\n          /** Component for rendering image content */\n          Image?: ImageMessagePartComponent | undefined;\n          /** Component for rendering file content */\n          File?: FileMessagePartComponent | undefined;\n          /** Component for rendering audio content (experimental) */\n          Unstable_Audio?: Unstable_AudioMessagePartComponent | undefined;\n          /** Configuration for tool call rendering */\n          tools?:\n            | {\n                /** Map of tool names to their specific components */\n                by_name?:\n                  | Record<string, ToolCallMessagePartComponent | undefined>\n                  | undefined;\n                /** Fallback component for unregistered tools */\n                Fallback?: ComponentType<ToolCallMessagePartProps> | undefined;\n              }\n            | {\n                /** Override component that handles all tool calls */\n                Override: ComponentType<ToolCallMessagePartProps>;\n              }\n            | undefined;\n\n          /**\n           * Component for rendering grouped consecutive tool calls.\n           *\n           * When provided, this component will automatically wrap consecutive tool-call\n           * message parts, allowing you to create collapsible sections, custom styling,\n           * or other grouped presentations for multiple tool calls.\n           *\n           * The component receives:\n           * - `startIndex`: The index of the first tool call in the group\n           * - `endIndex`: The index of the last tool call in the group\n           * - `children`: The rendered tool call components\n           *\n           * @example\n           * ```tsx\n           * // Collapsible tool group\n           * ToolGroup: ({ startIndex, endIndex, children }) => (\n           *   <details className=\"tool-group\">\n           *     <summary>\n           *       {endIndex - startIndex + 1} tool calls\n           *     </summary>\n           *     <div className=\"tool-group-content\">\n           *       {children}\n           *     </div>\n           *   </details>\n           * )\n           * ```\n           *\n           * @example\n           * ```tsx\n           * // Custom styled tool group with header\n           * ToolGroup: ({ startIndex, endIndex, children }) => (\n           *   <div className=\"border rounded-lg p-4 my-2\">\n           *     <div className=\"text-sm text-gray-600 mb-2\">\n           *       Tool execution #{startIndex + 1}-{endIndex + 1}\n           *     </div>\n           *     <div className=\"space-y-2\">\n           *       {children}\n           *     </div>\n           *   </div>\n           * )\n           * ```\n           *\n           * @param startIndex - Index of the first tool call in the group\n           * @param endIndex - Index of the last tool call in the group\n           * @param children - Rendered tool call components to display within the group\n           *\n           * @deprecated This feature is still experimental and subject to change.\n           */\n          ToolGroup?: ComponentType<\n            PropsWithChildren<{ startIndex: number; endIndex: number }>\n          >;\n        }\n      | undefined;\n  };\n}\n\nconst ToolUIDisplay = ({\n  Fallback,\n  ...props\n}: {\n  Fallback: ToolCallMessagePartComponent | undefined;\n} & ToolCallMessagePartProps) => {\n  const Render = useToolUIs((s) => s.getToolUI(props.toolName)) ?? Fallback;\n  if (!Render) return null;\n  return <Render {...props} />;\n};\n\nconst defaultComponents = {\n  Text: () => (\n    <p style={{ whiteSpace: \"pre-line\" }}>\n      <MessagePartPrimitiveText />\n      <MessagePartPrimitiveInProgress>\n        <span style={{ fontFamily: \"revert\" }}>{\" \\u25CF\"}</span>\n      </MessagePartPrimitiveInProgress>\n    </p>\n  ),\n  Reasoning: () => null,\n  Source: () => null,\n  Image: () => <MessagePartPrimitiveImage />,\n  File: () => null,\n  Unstable_Audio: () => null,\n  ToolGroup: ({ children }) => children,\n} satisfies MessagePrimitiveParts.Props[\"components\"];\n\ntype MessagePartComponentProps = {\n  components: MessagePrimitiveParts.Props[\"components\"];\n};\n\nconst MessagePartComponent: FC<MessagePartComponentProps> = ({\n  components: {\n    Text = defaultComponents.Text,\n    Reasoning = defaultComponents.Reasoning,\n    Image = defaultComponents.Image,\n    Source = defaultComponents.Source,\n    File = defaultComponents.File,\n    Unstable_Audio: Audio = defaultComponents.Unstable_Audio,\n    tools = {},\n  } = {},\n}) => {\n  const MessagePartRuntime = useMessagePartRuntime();\n\n  const part = useMessagePart();\n\n  const type = part.type;\n  if (type === \"tool-call\") {\n    const addResult = (result: any) => MessagePartRuntime.addToolResult(result);\n    if (\"Override\" in tools)\n      return <tools.Override {...part} addResult={addResult} />;\n    const Tool = tools.by_name?.[part.toolName] ?? tools.Fallback;\n    return <ToolUIDisplay {...part} Fallback={Tool} addResult={addResult} />;\n  }\n\n  if (part.status.type === \"requires-action\")\n    throw new Error(\"Encountered unexpected requires-action status\");\n\n  switch (type) {\n    case \"text\":\n      return <Text {...part} />;\n\n    case \"reasoning\":\n      return <Reasoning {...part} />;\n\n    case \"source\":\n      return <Source {...part} />;\n\n    case \"image\":\n      // eslint-disable-next-line jsx-a11y/alt-text\n      return <Image {...part} />;\n\n    case \"file\":\n      return <File {...part} />;\n\n    case \"audio\":\n      return <Audio {...part} />;\n\n    default:\n      const unhandledType: never = type;\n      throw new Error(`Unknown message part type: ${unhandledType}`);\n  }\n};\n\nexport namespace MessagePrimitivePartByIndex {\n  export type Props = {\n    index: number;\n    components: MessagePrimitiveParts.Props[\"components\"];\n  };\n}\n\n/**\n * Renders a single message part at the specified index.\n *\n * This component provides direct access to render a specific message part\n * within the current message context, using the provided component configuration.\n *\n * @example\n * ```tsx\n * <MessagePrimitive.PartByIndex\n *   index={0}\n *   components={{\n *     Text: MyTextComponent,\n *     Image: MyImageComponent\n *   }}\n * />\n * ```\n */\nexport const MessagePrimitivePartByIndex: FC<MessagePrimitivePartByIndex.Props> =\n  memo(\n    ({ index, components }) => {\n      const messageRuntime = useMessageRuntime();\n      const runtime = useMemo(\n        () => messageRuntime.getMessagePartByIndex(index),\n        [messageRuntime, index],\n      );\n\n      return (\n        <MessagePartRuntimeProvider runtime={runtime}>\n          <MessagePartComponent components={components} />\n        </MessagePartRuntimeProvider>\n      );\n    },\n    (prev, next) =>\n      prev.index === next.index &&\n      prev.components?.Text === next.components?.Text &&\n      prev.components?.Reasoning === next.components?.Reasoning &&\n      prev.components?.Source === next.components?.Source &&\n      prev.components?.Image === next.components?.Image &&\n      prev.components?.File === next.components?.File &&\n      prev.components?.Unstable_Audio === next.components?.Unstable_Audio &&\n      prev.components?.tools === next.components?.tools &&\n      prev.components?.ToolGroup === next.components?.ToolGroup,\n  );\n\nMessagePrimitivePartByIndex.displayName = \"MessagePrimitive.PartByIndex\";\n\nconst COMPLETE_STATUS: MessagePartStatus = Object.freeze({\n  type: \"complete\",\n});\n\nconst EmptyPartFallback: FC<{\n  status: MessagePartStatus;\n  component: TextMessagePartComponent;\n}> = ({ status, component: Component }) => {\n  return (\n    <TextMessagePartProvider text=\"\" isRunning={status.type === \"running\"}>\n      <Component type=\"text\" text=\"\" status={status} />\n    </TextMessagePartProvider>\n  );\n};\n\nconst EmptyPartsImpl: FC<MessagePartComponentProps> = ({ components }) => {\n  const status =\n    useMessage((s) => s.status as MessagePartStatus) ?? COMPLETE_STATUS;\n\n  if (components?.Empty) return <components.Empty status={status} />;\n\n  return (\n    <EmptyPartFallback\n      status={status}\n      component={components?.Text ?? defaultComponents.Text}\n    />\n  );\n};\n\nconst EmptyParts = memo(\n  EmptyPartsImpl,\n  (prev, next) =>\n    prev.components?.Empty === next.components?.Empty &&\n    prev.components?.Text === next.components?.Text,\n);\n\n/**\n * Renders the parts of a message with support for multiple content types.\n *\n * This component automatically handles different types of message content including\n * text, images, files, tool calls, and more. It provides a flexible component\n * system for customizing how each content type is rendered.\n *\n * @example\n * ```tsx\n * <MessagePrimitive.Parts\n *   components={{\n *     Text: ({ text }) => <p className=\"message-text\">{text}</p>,\n *     Image: ({ image }) => <img src={image} alt=\"Message image\" />,\n *     tools: {\n *       by_name: {\n *         calculator: CalculatorTool,\n *         weather: WeatherTool,\n *       },\n *       Fallback: DefaultToolComponent\n *     }\n *   }}\n * />\n * ```\n */\nexport const MessagePrimitiveParts: FC<MessagePrimitiveParts.Props> = ({\n  components,\n}) => {\n  const contentLength = useMessage((s) => s.content.length);\n  const messageRanges = useMessagePartsGroups();\n\n  const partsElements = useMemo(() => {\n    if (contentLength === 0) {\n      return <EmptyParts components={components} />;\n    }\n\n    return messageRanges.map((range) => {\n      if (range.type === \"single\") {\n        return (\n          <MessagePrimitivePartByIndex\n            key={range.index}\n            index={range.index}\n            components={components}\n          />\n        );\n      } else {\n        const ToolGroupComponent =\n          components!.ToolGroup ?? defaultComponents.ToolGroup;\n        return (\n          <ToolGroupComponent\n            key={range.startIndex}\n            startIndex={range.startIndex}\n            endIndex={range.endIndex}\n          >\n            {Array.from(\n              { length: range.endIndex - range.startIndex + 1 },\n              (_, i) => (\n                <MessagePrimitivePartByIndex\n                  key={i}\n                  index={range.startIndex + i}\n                  components={components}\n                />\n              ),\n            )}\n          </ToolGroupComponent>\n        );\n      }\n    });\n  }, [messageRanges, components, contentLength]);\n\n  return <>{partsElements}</>;\n};\n\nMessagePrimitiveParts.displayName = \"MessagePrimitive.Parts\";\n"], "mappings": ";;;AAEA;AAAA,EAGE;AAAA,EAEA;AAAA,OACK;AACP;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP;AAAA,EACE;AAAA,EACA;AAAA,OACK;AACP,SAAS,kCAAkC;AAC3C,SAAS,gCAAgC;AACzC,SAAS,iCAAiC;AAY1C,SAAS,sCAAsC;AAE/C,SAAS,kBAAkB;AAyKlB,SA2OA,UA3OA,KAKL,YALK;AA/JT,IAAM,oBAAoB,CACxB,iBACuB;AACvB,QAAM,SAA6B,CAAC;AACpC,MAAI,wBAAwB;AAE5B,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC5C,UAAM,OAAO,aAAa,CAAC;AAE3B,QAAI,SAAS,aAAa;AAExB,UAAI,0BAA0B,IAAI;AAChC,gCAAwB;AAAA,MAC1B;AAAA,IACF,OAAO;AAEL,UAAI,0BAA0B,IAAI;AAChC,eAAO,KAAK;AAAA,UACV,MAAM;AAAA,UACN,YAAY;AAAA,UACZ,UAAU,IAAI;AAAA,QAChB,CAAC;AACD,gCAAwB;AAAA,MAC1B;AAGA,aAAO,KAAK,EAAE,MAAM,UAAU,OAAO,EAAE,CAAC;AAAA,IAC1C;AAAA,EACF;AAGA,MAAI,0BAA0B,IAAI;AAChC,WAAO,KAAK;AAAA,MACV,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,UAAU,aAAa,SAAS;AAAA,IAClC,CAAC;AAAA,EACH;AAEA,SAAO;AACT;AAEA,IAAM,wBAAwB,MAA0B;AACtD,QAAM,eAAe;AAAA,IACnB,WAAW,CAAC,MAAM,EAAE,QAAQ,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC;AAAA,EAChD;AAEA,SAAO,QAAQ,MAAM;AACnB,QAAI,aAAa,WAAW,GAAG;AAC7B,aAAO,CAAC;AAAA,IACV;AACA,WAAO,kBAAkB,YAAY;AAAA,EACvC,GAAG,CAAC,YAAY,CAAC;AACnB;AAkGA,IAAM,gBAAgB,CAAC;AAAA,EACrB;AAAA,EACA,GAAG;AACL,MAEiC;AAC/B,QAAM,SAAS,WAAW,CAAC,MAAM,EAAE,UAAU,MAAM,QAAQ,CAAC,KAAK;AACjE,MAAI,CAAC,OAAQ,QAAO;AACpB,SAAO,oBAAC,UAAQ,GAAG,OAAO;AAC5B;AAEA,IAAM,oBAAoB;AAAA,EACxB,MAAM,MACJ,qBAAC,OAAE,OAAO,EAAE,YAAY,WAAW,GACjC;AAAA,wBAAC,4BAAyB;AAAA,IAC1B,oBAAC,kCACC,8BAAC,UAAK,OAAO,EAAE,YAAY,SAAS,GAAI,qBAAU,GACpD;AAAA,KACF;AAAA,EAEF,WAAW,MAAM;AAAA,EACjB,QAAQ,MAAM;AAAA,EACd,OAAO,MAAM,oBAAC,6BAA0B;AAAA,EACxC,MAAM,MAAM;AAAA,EACZ,gBAAgB,MAAM;AAAA,EACtB,WAAW,CAAC,EAAE,SAAS,MAAM;AAC/B;AAMA,IAAM,uBAAsD,CAAC;AAAA,EAC3D,YAAY;AAAA,IACV,OAAO,kBAAkB;AAAA,IACzB,YAAY,kBAAkB;AAAA,IAC9B,QAAQ,kBAAkB;AAAA,IAC1B,SAAS,kBAAkB;AAAA,IAC3B,OAAO,kBAAkB;AAAA,IACzB,gBAAgB,QAAQ,kBAAkB;AAAA,IAC1C,QAAQ,CAAC;AAAA,EACX,IAAI,CAAC;AACP,MAAM;AACJ,QAAM,qBAAqB,sBAAsB;AAEjD,QAAM,OAAO,eAAe;AAE5B,QAAM,OAAO,KAAK;AAClB,MAAI,SAAS,aAAa;AACxB,UAAM,YAAY,CAAC,WAAgB,mBAAmB,cAAc,MAAM;AAC1E,QAAI,cAAc;AAChB,aAAO,oBAAC,MAAM,UAAN,EAAgB,GAAG,MAAM,WAAsB;AACzD,UAAM,OAAO,MAAM,UAAU,KAAK,QAAQ,KAAK,MAAM;AACrD,WAAO,oBAAC,iBAAe,GAAG,MAAM,UAAU,MAAM,WAAsB;AAAA,EACxE;AAEA,MAAI,KAAK,OAAO,SAAS;AACvB,UAAM,IAAI,MAAM,+CAA+C;AAEjE,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,aAAO,oBAAC,QAAM,GAAG,MAAM;AAAA,IAEzB,KAAK;AACH,aAAO,oBAAC,aAAW,GAAG,MAAM;AAAA,IAE9B,KAAK;AACH,aAAO,oBAAC,UAAQ,GAAG,MAAM;AAAA,IAE3B,KAAK;AAEH,aAAO,oBAAC,SAAO,GAAG,MAAM;AAAA,IAE1B,KAAK;AACH,aAAO,oBAAC,QAAM,GAAG,MAAM;AAAA,IAEzB,KAAK;AACH,aAAO,oBAAC,SAAO,GAAG,MAAM;AAAA,IAE1B;AACE,YAAM,gBAAuB;AAC7B,YAAM,IAAI,MAAM,8BAA8B,aAAa,EAAE;AAAA,EACjE;AACF;AA0BO,IAAM,8BACX;AAAA,EACE,CAAC,EAAE,OAAO,WAAW,MAAM;AACzB,UAAM,iBAAiB,kBAAkB;AACzC,UAAM,UAAU;AAAA,MACd,MAAM,eAAe,sBAAsB,KAAK;AAAA,MAChD,CAAC,gBAAgB,KAAK;AAAA,IACxB;AAEA,WACE,oBAAC,8BAA2B,SAC1B,8BAAC,wBAAqB,YAAwB,GAChD;AAAA,EAEJ;AAAA,EACA,CAAC,MAAM,SACL,KAAK,UAAU,KAAK,SACpB,KAAK,YAAY,SAAS,KAAK,YAAY,QAC3C,KAAK,YAAY,cAAc,KAAK,YAAY,aAChD,KAAK,YAAY,WAAW,KAAK,YAAY,UAC7C,KAAK,YAAY,UAAU,KAAK,YAAY,SAC5C,KAAK,YAAY,SAAS,KAAK,YAAY,QAC3C,KAAK,YAAY,mBAAmB,KAAK,YAAY,kBACrD,KAAK,YAAY,UAAU,KAAK,YAAY,SAC5C,KAAK,YAAY,cAAc,KAAK,YAAY;AACpD;AAEF,4BAA4B,cAAc;AAE1C,IAAM,kBAAqC,OAAO,OAAO;AAAA,EACvD,MAAM;AACR,CAAC;AAED,IAAM,oBAGD,CAAC,EAAE,QAAQ,WAAW,UAAU,MAAM;AACzC,SACE,oBAAC,2BAAwB,MAAK,IAAG,WAAW,OAAO,SAAS,WAC1D,8BAAC,aAAU,MAAK,QAAO,MAAK,IAAG,QAAgB,GACjD;AAEJ;AAEA,IAAM,iBAAgD,CAAC,EAAE,WAAW,MAAM;AACxE,QAAM,SACJ,WAAW,CAAC,MAAM,EAAE,MAA2B,KAAK;AAEtD,MAAI,YAAY,MAAO,QAAO,oBAAC,WAAW,OAAX,EAAiB,QAAgB;AAEhE,SACE;AAAA,IAAC;AAAA;AAAA,MACC;AAAA,MACA,WAAW,YAAY,QAAQ,kBAAkB;AAAA;AAAA,EACnD;AAEJ;AAEA,IAAM,aAAa;AAAA,EACjB;AAAA,EACA,CAAC,MAAM,SACL,KAAK,YAAY,UAAU,KAAK,YAAY,SAC5C,KAAK,YAAY,SAAS,KAAK,YAAY;AAC/C;AA0BO,IAAM,wBAAyD,CAAC;AAAA,EACrE;AACF,MAAM;AACJ,QAAM,gBAAgB,WAAW,CAAC,MAAM,EAAE,QAAQ,MAAM;AACxD,QAAM,gBAAgB,sBAAsB;AAE5C,QAAM,gBAAgB,QAAQ,MAAM;AAClC,QAAI,kBAAkB,GAAG;AACvB,aAAO,oBAAC,cAAW,YAAwB;AAAA,IAC7C;AAEA,WAAO,cAAc,IAAI,CAAC,UAAU;AAClC,UAAI,MAAM,SAAS,UAAU;AAC3B,eACE;AAAA,UAAC;AAAA;AAAA,YAEC,OAAO,MAAM;AAAA,YACb;AAAA;AAAA,UAFK,MAAM;AAAA,QAGb;AAAA,MAEJ,OAAO;AACL,cAAM,qBACJ,WAAY,aAAa,kBAAkB;AAC7C,eACE;AAAA,UAAC;AAAA;AAAA,YAEC,YAAY,MAAM;AAAA,YAClB,UAAU,MAAM;AAAA,YAEf,gBAAM;AAAA,cACL,EAAE,QAAQ,MAAM,WAAW,MAAM,aAAa,EAAE;AAAA,cAChD,CAAC,GAAG,MACF;AAAA,gBAAC;AAAA;AAAA,kBAEC,OAAO,MAAM,aAAa;AAAA,kBAC1B;AAAA;AAAA,gBAFK;AAAA,cAGP;AAAA,YAEJ;AAAA;AAAA,UAbK,MAAM;AAAA,QAcb;AAAA,MAEJ;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,eAAe,YAAY,aAAa,CAAC;AAE7C,SAAO,gCAAG,yBAAc;AAC1B;AAEA,sBAAsB,cAAc;", "names": []}