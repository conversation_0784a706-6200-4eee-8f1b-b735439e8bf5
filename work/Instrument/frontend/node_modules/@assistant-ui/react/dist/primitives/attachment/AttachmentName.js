"use client";

// src/primitives/attachment/AttachmentName.tsx
import { useAttachment } from "../../context/react/AttachmentContext.js";
import { Fragment, jsx } from "react/jsx-runtime";
var AttachmentPrimitiveName = () => {
  const name = useAttachment((a) => a.name);
  return /* @__PURE__ */ jsx(Fragment, { children: name });
};
AttachmentPrimitiveName.displayName = "AttachmentPrimitive.Name";
export {
  AttachmentPrimitiveName
};
//# sourceMappingURL=AttachmentName.js.map