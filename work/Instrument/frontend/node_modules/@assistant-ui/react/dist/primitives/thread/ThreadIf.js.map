{"version": 3, "sources": ["../../../src/primitives/thread/ThreadIf.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { FC, PropsWithChildren } from \"react\";\nimport { useThread } from \"../../context\";\nimport type { RequireAtLeastOne } from \"../../utils/RequireAtLeastOne\";\n\ntype ThreadIfFilters = {\n  empty: boolean | undefined;\n  running: boolean | undefined;\n  disabled: boolean | undefined;\n};\n\ntype UseThreadIfProps = RequireAtLeastOne<ThreadIfFilters>;\n\nconst useThreadIf = (props: UseThreadIfProps) => {\n  return useThread((thread) => {\n    if (props.empty === true && thread.messages.length !== 0) return false;\n    if (props.empty === false && thread.messages.length === 0) return false;\n    if (props.running === true && !thread.isRunning) return false;\n    if (props.running === false && thread.isRunning) return false;\n    if (props.disabled === true && !thread.isDisabled) return false;\n    if (props.disabled === false && thread.isDisabled) return false;\n\n    return true;\n  });\n};\n\nexport namespace ThreadPrimitiveIf {\n  export type Props = PropsWithChildren<UseThreadIfProps>;\n}\n\nexport const ThreadPrimitiveIf: FC<ThreadPrimitiveIf.Props> = ({\n  children,\n  ...query\n}) => {\n  const result = useThreadIf(query);\n  return result ? children : null;\n};\n\nThreadPrimitiveIf.displayName = \"ThreadPrimitive.If\";\n"], "mappings": ";;;AAGA,SAAS,iBAAiB;AAW1B,IAAM,cAAc,CAAC,UAA4B;AAC/C,SAAO,UAAU,CAAC,WAAW;AAC3B,QAAI,MAAM,UAAU,QAAQ,OAAO,SAAS,WAAW,EAAG,QAAO;AACjE,QAAI,MAAM,UAAU,SAAS,OAAO,SAAS,WAAW,EAAG,QAAO;AAClE,QAAI,MAAM,YAAY,QAAQ,CAAC,OAAO,UAAW,QAAO;AACxD,QAAI,MAAM,YAAY,SAAS,OAAO,UAAW,QAAO;AACxD,QAAI,MAAM,aAAa,QAAQ,CAAC,OAAO,WAAY,QAAO;AAC1D,QAAI,MAAM,aAAa,SAAS,OAAO,WAAY,QAAO;AAE1D,WAAO;AAAA,EACT,CAAC;AACH;AAMO,IAAM,oBAAiD,CAAC;AAAA,EAC7D;AAAA,EACA,GAAG;AACL,MAAM;AACJ,QAAM,SAAS,YAAY,KAAK;AAChC,SAAO,SAAS,WAAW;AAC7B;AAEA,kBAAkB,cAAc;", "names": []}