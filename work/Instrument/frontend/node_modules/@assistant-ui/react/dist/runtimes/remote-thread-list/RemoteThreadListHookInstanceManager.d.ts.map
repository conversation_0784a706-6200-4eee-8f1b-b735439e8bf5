{"version": 3, "file": "RemoteThreadListHookInstanceManager.d.ts", "sourceRoot": "", "sources": ["../../../src/runtimes/remote-thread-list/RemoteThreadListHookInstanceManager.tsx"], "names": [], "mappings": "AAGA,OAAO,EACL,EAAE,EAMF,iBAAiB,EACjB,aAAa,EACd,MAAM,OAAO,CAAC;AASf,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AACtD,OAAO,EAAE,gBAAgB,EAAE,MAAM,WAAW,CAAC;AAE7C,KAAK,oBAAoB,GAAG,MAAM,gBAAgB,CAAC;AAKnD,qBAAa,mCAAoC,SAAQ,gBAAgB;IACvE,OAAO,CAAC,cAAc,CAEpB;IACF,OAAO,CAAC,SAAS,CAAmD;IACpE,OAAO,CAAC,0BAA0B,CAAsB;gBAE5C,WAAW,EAAE,oBAAoB;IAKtC,kBAAkB,CAAC,QAAQ,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAwBnC,oBAAoB,CAAC,QAAQ,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMrC,iBAAiB,CAAC,QAAQ,EAAE,MAAM;IAKlC,cAAc,CAAC,cAAc,EAAE,oBAAoB;IAO1D,OAAO,CAAC,0BAA0B,CAkDhC;IAEF,OAAO,CAAC,0BAA0B,CAkB/B;IAEI,+BAA+B,EAAE,EAAE,CAAC;QACzC,QAAQ,EAAE,aAAa,CAAC,iBAAiB,CAAC,CAAC;KAC5C,CAAC,CAUA;CACH"}