import React from "react";
export declare namespace ComposerAttachmentDropzonePrimitive {
    type Element = HTMLDivElement;
    type Props = React.HTMLAttributes<HTMLDivElement> & {
        asChild?: boolean | undefined;
        disabled?: boolean | undefined;
    };
}
export declare const ComposerAttachmentDropzone: React.ForwardRefExoticComponent<React.HTMLAttributes<HTMLDivElement> & {
    asChild?: boolean | undefined;
    disabled?: boolean | undefined;
} & React.RefAttributes<HTMLDivElement>>;
//# sourceMappingURL=ComposerAttachmentDropzone.d.ts.map