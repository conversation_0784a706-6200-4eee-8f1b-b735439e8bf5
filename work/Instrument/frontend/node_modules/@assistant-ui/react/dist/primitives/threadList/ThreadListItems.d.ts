import { ComponentType, FC } from "react";
export declare namespace ThreadListPrimitiveItems {
    type Props = {
        archived?: boolean | undefined;
        components: {
            ThreadListItem: ComponentType;
        };
    };
}
export declare namespace ThreadListPrimitiveItemByIndex {
    type Props = {
        index: number;
        archived?: boolean | undefined;
        components: ThreadListPrimitiveItems.Props["components"];
    };
}
/**
 * Renders a single thread list item at the specified index.
 *
 * This component provides direct access to render a specific thread
 * from the thread list using the provided component configuration.
 *
 * @example
 * ```tsx
 * <ThreadListPrimitive.ItemByIndex
 *   index={0}
 *   components={{
 *     ThreadListItem: MyThreadListItem
 *   }}
 * />
 * ```
 */
export declare const ThreadListPrimitiveItemByIndex: FC<ThreadListPrimitiveItemByIndex.Props>;
export declare const ThreadListPrimitiveItems: FC<ThreadListPrimitiveItems.Props>;
//# sourceMappingURL=ThreadListItems.d.ts.map