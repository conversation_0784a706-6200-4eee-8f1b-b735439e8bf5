{"version": 3, "sources": ["../../../src/primitives/threadList/ThreadListRoot.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { ComponentPropsWithoutRef, ComponentRef, forwardRef } from \"react\";\n\ntype PrimitiveDivProps = ComponentPropsWithoutRef<typeof Primitive.div>;\n\nexport namespace ThreadListPrimitiveRoot {\n  export type Element = ComponentRef<typeof Primitive.div>;\n  export type Props = PrimitiveDivProps;\n}\n\nexport const ThreadListPrimitiveRoot = forwardRef<\n  ThreadListPrimitiveRoot.Element,\n  ThreadListPrimitiveRoot.Props\n>((props, ref) => {\n  return <Primitive.div {...props} ref={ref} />;\n});\n\nThreadListPrimitiveRoot.displayName = \"ThreadListPrimitive.Root\";\n"], "mappings": ";;;AAEA,SAAS,iBAAiB;AAC1B,SAAiD,kBAAkB;AAa1D;AAJF,IAAM,0BAA0B,WAGrC,CAAC,OAAO,QAAQ;AAChB,SAAO,oBAAC,UAAU,KAAV,EAAe,GAAG,OAAO,KAAU;AAC7C,CAAC;AAED,wBAAwB,cAAc;", "names": []}