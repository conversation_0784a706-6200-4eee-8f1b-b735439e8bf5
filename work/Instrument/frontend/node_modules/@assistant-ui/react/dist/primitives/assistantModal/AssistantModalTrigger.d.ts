import { ComponentPropsWithoutRef, ComponentRef } from "react";
import * as PopoverPrimitive from "@radix-ui/react-popover";
export declare namespace AssistantModalPrimitiveTrigger {
    type Element = ComponentRef<typeof PopoverPrimitive.Trigger>;
    type Props = ComponentPropsWithoutRef<typeof PopoverPrimitive.Trigger>;
}
export declare const AssistantModalPrimitiveTrigger: import("react").ForwardRefExoticComponent<Omit<PopoverPrimitive.PopoverTriggerProps & import("react").RefAttributes<HTMLButtonElement>, "ref"> & import("react").RefAttributes<HTMLButtonElement>>;
//# sourceMappingURL=AssistantModalTrigger.d.ts.map