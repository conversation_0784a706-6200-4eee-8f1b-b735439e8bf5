{"version": 3, "sources": ["../../../src/primitives/threadList/ThreadListItems.tsx"], "sourcesContent": ["\"use client\";\n\nimport { ComponentType, FC, memo, useMemo } from \"react\";\nimport { ThreadListItemRuntimeProvider } from \"../../context/providers/ThreadListItemRuntimeProvider\";\nimport { useAssistantRuntime, useThreadList } from \"../../context\";\n\nexport namespace ThreadListPrimitiveItems {\n  export type Props = {\n    archived?: boolean | undefined;\n    components: {\n      ThreadListItem: ComponentType;\n    };\n  };\n}\n\nexport namespace ThreadListPrimitiveItemByIndex {\n  export type Props = {\n    index: number;\n    archived?: boolean | undefined;\n    components: ThreadListPrimitiveItems.Props[\"components\"];\n  };\n}\n\n/**\n * Renders a single thread list item at the specified index.\n *\n * This component provides direct access to render a specific thread\n * from the thread list using the provided component configuration.\n *\n * @example\n * ```tsx\n * <ThreadListPrimitive.ItemByIndex\n *   index={0}\n *   components={{\n *     ThreadListItem: MyThreadListItem\n *   }}\n * />\n * ```\n */\nexport const ThreadListPrimitiveItemByIndex: FC<ThreadListPrimitiveItemByIndex.Props> =\n  memo(\n    ({ index, archived = false, components }) => {\n      const assistantRuntime = useAssistantRuntime();\n      const runtime = useMemo(\n        () =>\n          archived\n            ? assistantRuntime.threads.getArchivedItemByIndex(index)\n            : assistantRuntime.threads.getItemByIndex(index),\n        [assistantRuntime, index, archived],\n      );\n\n      const ThreadListItemComponent = components.ThreadListItem;\n\n      return (\n        <ThreadListItemRuntimeProvider runtime={runtime}>\n          <ThreadListItemComponent />\n        </ThreadListItemRuntimeProvider>\n      );\n    },\n    (prev, next) =>\n      prev.index === next.index &&\n      prev.archived === next.archived &&\n      prev.components.ThreadListItem === next.components.ThreadListItem,\n  );\n\nThreadListPrimitiveItemByIndex.displayName = \"ThreadListPrimitive.ItemByIndex\";\n\nexport const ThreadListPrimitiveItems: FC<ThreadListPrimitiveItems.Props> = ({\n  archived = false,\n  components,\n}) => {\n  const contentLength = useThreadList((s) =>\n    archived ? s.archivedThreads.length : s.threads.length,\n  );\n\n  const listElements = useMemo(() => {\n    return Array.from({ length: contentLength }, (_, index) => (\n      <ThreadListPrimitiveItemByIndex\n        key={index}\n        index={index}\n        archived={archived}\n        components={components}\n      />\n    ));\n  }, [contentLength, archived, components]);\n\n  return listElements;\n};\n\nThreadListPrimitiveItems.displayName = \"ThreadListPrimitive.Items\";\n"], "mappings": ";;;AAEA,SAA4B,MAAM,eAAe;AACjD,SAAS,qCAAqC;AAC9C,SAAS,qBAAqB,qBAAqB;AAmDzC;AAhBH,IAAM,iCACX;AAAA,EACE,CAAC,EAAE,OAAO,WAAW,OAAO,WAAW,MAAM;AAC3C,UAAM,mBAAmB,oBAAoB;AAC7C,UAAM,UAAU;AAAA,MACd,MACE,WACI,iBAAiB,QAAQ,uBAAuB,KAAK,IACrD,iBAAiB,QAAQ,eAAe,KAAK;AAAA,MACnD,CAAC,kBAAkB,OAAO,QAAQ;AAAA,IACpC;AAEA,UAAM,0BAA0B,WAAW;AAE3C,WACE,oBAAC,iCAA8B,SAC7B,8BAAC,2BAAwB,GAC3B;AAAA,EAEJ;AAAA,EACA,CAAC,MAAM,SACL,KAAK,UAAU,KAAK,SACpB,KAAK,aAAa,KAAK,YACvB,KAAK,WAAW,mBAAmB,KAAK,WAAW;AACvD;AAEF,+BAA+B,cAAc;AAEtC,IAAM,2BAA+D,CAAC;AAAA,EAC3E,WAAW;AAAA,EACX;AACF,MAAM;AACJ,QAAM,gBAAgB;AAAA,IAAc,CAAC,MACnC,WAAW,EAAE,gBAAgB,SAAS,EAAE,QAAQ;AAAA,EAClD;AAEA,QAAM,eAAe,QAAQ,MAAM;AACjC,WAAO,MAAM,KAAK,EAAE,QAAQ,cAAc,GAAG,CAAC,GAAG,UAC/C;AAAA,MAAC;AAAA;AAAA,QAEC;AAAA,QACA;AAAA,QACA;AAAA;AAAA,MAHK;AAAA,IAIP,CACD;AAAA,EACH,GAAG,CAAC,eAAe,UAAU,UAAU,CAAC;AAExC,SAAO;AACT;AAEA,yBAAyB,cAAc;", "names": []}