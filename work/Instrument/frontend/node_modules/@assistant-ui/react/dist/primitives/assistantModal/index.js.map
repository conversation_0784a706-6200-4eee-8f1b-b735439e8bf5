{"version": 3, "sources": ["../../../src/primitives/assistantModal/index.ts"], "sourcesContent": ["export { AssistantModalPrimitiveRoot as Root } from \"./AssistantModalRoot\";\nexport { AssistantModalPrimitiveTrigger as Trigger } from \"./AssistantModalTrigger\";\nexport { AssistantModalPrimitiveContent as Content } from \"./AssistantModalContent\";\nexport { AssistantModalPrimitiveAnchor as Anchor } from \"./AssistantModalAnchor\";\n"], "mappings": ";AAAA,SAAwC,mCAAY;AACpD,SAA2C,sCAAe;AAC1D,SAA2C,sCAAe;AAC1D,SAA0C,qCAAc;", "names": []}