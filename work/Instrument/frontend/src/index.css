/* Global Reset and Base Styles for The Instrument */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  width: 100%;
  overflow: hidden;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  height: 100vh;
  width: 100vw;
  margin: 0;
  padding: 0;
}

/* Remove default button styles */
button {
  border: none;
  background: none;
  font-family: inherit;
  cursor: pointer;
}

/* Remove default textarea styles */
textarea {
  font-family: inherit;
  border: none;
  outline: none;
  resize: none;
}

/* Ensure proper text selection */
::selection {
  background: rgba(59, 130, 246, 0.3);
}

/* Disable text selection on UI elements */
.control-btn,
.status-bar,
.chat-header {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}
