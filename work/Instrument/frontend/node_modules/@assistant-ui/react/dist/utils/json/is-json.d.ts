import { ReadonlyJSONArray, <PERSON>onlyJSONO<PERSON>, <PERSON>onlyJSONValue } from "assistant-stream/utils";
export declare function isJSONValue(value: unknown, currentDepth?: number): value is ReadonlyJSONValue;
export declare function isJSONArray(value: unknown): value is ReadonlyJSONArray;
export declare function isJSONObject(value: unknown): value is ReadonlyJSONObject;
//# sourceMappingURL=is-json.d.ts.map