{"version": 3, "sources": ["../../../src/runtimes/external-store/ExternalStoreThreadRuntimeCore.tsx"], "sourcesContent": ["import {\n  AddToolResultOptions,\n  StartRunConfig,\n  ThreadSuggestion,\n} from \"../core/ThreadRuntimeCore\";\n\nimport { AppendMessage, ThreadMessage } from \"../../types\";\nimport { ExternalStoreAdapter } from \"./ExternalStoreAdapter\";\nimport {\n  getExternalStoreMessage,\n  symbolInnerMessage,\n} from \"./getExternalStoreMessage\";\nimport { ThreadMessageConverter } from \"./ThreadMessageConverter\";\nimport { getAutoStatus, isAutoStatus } from \"./auto-status\";\nimport { fromThreadMessageLike } from \"./ThreadMessageLike\";\nimport { getThreadMessageText } from \"../../utils/getThreadMessageText\";\nimport {\n  RuntimeCapabilities,\n  ThreadRuntimeCore,\n} from \"../core/ThreadRuntimeCore\";\nimport { BaseThreadRuntimeCore } from \"../core/BaseThreadRuntimeCore\";\nimport { ModelContextProvider } from \"../../model-context\";\n\nconst EMPTY_ARRAY = Object.freeze([]);\n\nexport const hasUpcomingMessage = (\n  isRunning: boolean,\n  messages: readonly ThreadMessage[],\n) => {\n  return isRunning && messages[messages.length - 1]?.role !== \"assistant\";\n};\n\nexport class ExternalStoreThreadRuntimeCore\n  extends BaseThreadRuntimeCore\n  implements ThreadRuntimeCore\n{\n  private assistantOptimisticId: string | null = null;\n\n  private _capabilities: RuntimeCapabilities = {\n    switchToBranch: false,\n    edit: false,\n    reload: false,\n    cancel: false,\n    unstable_copy: false,\n    speech: false,\n    attachments: false,\n    feedback: false,\n  };\n\n  public get capabilities() {\n    return this._capabilities;\n  }\n\n  private _messages!: readonly ThreadMessage[];\n  public isDisabled!: boolean;\n  public get isLoading() {\n    return this._store.isLoading ?? false;\n  }\n\n  public override get messages() {\n    return this._messages;\n  }\n\n  public get adapters() {\n    return this._store.adapters;\n  }\n\n  public suggestions: readonly ThreadSuggestion[] = [];\n  public extras: unknown = undefined;\n\n  private _converter = new ThreadMessageConverter();\n\n  private _store!: ExternalStoreAdapter<any>;\n\n  public override beginEdit(messageId: string) {\n    if (!this._store.onEdit)\n      throw new Error(\"Runtime does not support editing.\");\n\n    super.beginEdit(messageId);\n  }\n\n  constructor(\n    contextProvider: ModelContextProvider,\n    store: ExternalStoreAdapter<any>,\n  ) {\n    super(contextProvider);\n    this.__internal_setAdapter(store);\n  }\n\n  public __internal_setAdapter(store: ExternalStoreAdapter<any>) {\n    if (this._store === store) return;\n\n    const isRunning = store.isRunning ?? false;\n    this.isDisabled = store.isDisabled ?? false;\n\n    const oldStore = this._store as ExternalStoreAdapter<any> | undefined;\n    this._store = store;\n    this.extras = store.extras;\n    this.suggestions = store.suggestions ?? EMPTY_ARRAY;\n    this._capabilities = {\n      switchToBranch: this._store.setMessages !== undefined,\n      edit: this._store.onEdit !== undefined,\n      reload: this._store.onReload !== undefined,\n      cancel: this._store.onCancel !== undefined,\n      speech: this._store.adapters?.speech !== undefined,\n      unstable_copy: this._store.unstable_capabilities?.copy !== false, // default true\n      attachments: !!this._store.adapters?.attachments,\n      feedback: !!this._store.adapters?.feedback,\n    };\n\n    let messages: readonly ThreadMessage[];\n\n    if (store.messageRepository) {\n      // Handle messageRepository\n      if (\n        oldStore &&\n        oldStore.isRunning === store.isRunning &&\n        oldStore.messageRepository === store.messageRepository\n      ) {\n        this._notifySubscribers();\n        return;\n      }\n\n      // Clear and import the message repository\n      this.repository.clear();\n      this.assistantOptimisticId = null;\n      this.repository.import(store.messageRepository);\n\n      messages = this.repository.getMessages();\n    } else if (store.messages) {\n      // Handle messages array\n\n      if (oldStore) {\n        // flush the converter cache when the convertMessage prop changes\n        if (oldStore.convertMessage !== store.convertMessage) {\n          this._converter = new ThreadMessageConverter();\n        } else if (\n          oldStore.isRunning === store.isRunning &&\n          oldStore.messages === store.messages\n        ) {\n          this._notifySubscribers();\n          // no conversion update\n          return;\n        }\n      }\n\n      messages = !store.convertMessage\n        ? store.messages\n        : this._converter.convertMessages(store.messages, (cache, m, idx) => {\n            if (!store.convertMessage) return m;\n\n            const isLast = idx === store.messages!.length - 1;\n            const autoStatus = getAutoStatus(isLast, isRunning, false);\n\n            if (\n              cache &&\n              (cache.role !== \"assistant\" ||\n                !isAutoStatus(cache.status) ||\n                cache.status === autoStatus)\n            )\n              return cache;\n\n            const messageLike = store.convertMessage(m, idx);\n            const newMessage = fromThreadMessageLike(\n              messageLike,\n              idx.toString(),\n              autoStatus,\n            );\n            (newMessage as any)[symbolInnerMessage] = m;\n            return newMessage;\n          });\n\n      for (let i = 0; i < messages.length; i++) {\n        const message = messages[i]!;\n        const parent = messages[i - 1];\n        this.repository.addOrUpdateMessage(parent?.id ?? null, message);\n      }\n    } else {\n      throw new Error(\n        \"ExternalStoreAdapter must provide either 'messages' or 'messageRepository'\",\n      );\n    }\n\n    // Common logic for both paths\n    if (messages.length > 0) this.ensureInitialized();\n\n    if ((oldStore?.isRunning ?? false) !== (store.isRunning ?? false)) {\n      if (store.isRunning) {\n        this._notifyEventSubscribers(\"run-start\");\n      } else {\n        this._notifyEventSubscribers(\"run-end\");\n      }\n    }\n\n    if (this.assistantOptimisticId) {\n      this.repository.deleteMessage(this.assistantOptimisticId);\n      this.assistantOptimisticId = null;\n    }\n\n    if (hasUpcomingMessage(isRunning, messages)) {\n      this.assistantOptimisticId = this.repository.appendOptimisticMessage(\n        messages.at(-1)?.id ?? null,\n        {\n          role: \"assistant\",\n          content: [],\n        },\n      );\n    }\n\n    this.repository.resetHead(\n      this.assistantOptimisticId ?? messages.at(-1)?.id ?? null,\n    );\n\n    this._messages = this.repository.getMessages();\n    this._notifySubscribers();\n  }\n\n  public override switchToBranch(branchId: string): void {\n    if (!this._store.setMessages)\n      throw new Error(\"Runtime does not support switching branches.\");\n\n    this.repository.switchToBranch(branchId);\n    this.updateMessages(this.repository.getMessages());\n  }\n\n  public async append(message: AppendMessage): Promise<void> {\n    if (message.parentId !== (this.messages.at(-1)?.id ?? null)) {\n      if (!this._store.onEdit)\n        throw new Error(\"Runtime does not support editing messages.\");\n      await this._store.onEdit(message);\n    } else {\n      await this._store.onNew(message);\n    }\n  }\n\n  public async startRun(config: StartRunConfig): Promise<void> {\n    if (!this._store.onReload)\n      throw new Error(\"Runtime does not support reloading messages.\");\n\n    await this._store.onReload(config.parentId, config);\n  }\n\n  public async resumeRun(): Promise<void> {\n    throw new Error(\"Runtime does not support resuming runs.\");\n  }\n\n  public cancelRun(): void {\n    if (!this._store.onCancel)\n      throw new Error(\"Runtime does not support cancelling runs.\");\n\n    this._store.onCancel();\n\n    if (this.assistantOptimisticId) {\n      this.repository.deleteMessage(this.assistantOptimisticId);\n      this.assistantOptimisticId = null;\n    }\n\n    let messages = this.repository.getMessages();\n    const previousMessage = messages[messages.length - 1];\n    if (\n      previousMessage?.role === \"user\" &&\n      previousMessage.id === messages.at(-1)?.id // ensure the previous message is a leaf node\n    ) {\n      this.repository.deleteMessage(previousMessage.id);\n      if (!this.composer.text.trim()) {\n        this.composer.setText(getThreadMessageText(previousMessage));\n      }\n\n      messages = this.repository.getMessages();\n    } else {\n      this._notifySubscribers();\n    }\n\n    // resync messages (for reloading, to restore the previous branch)\n    setTimeout(() => {\n      this.updateMessages(messages);\n    }, 0);\n  }\n\n  public addToolResult(options: AddToolResultOptions) {\n    if (!this._store.onAddToolResult && !this._store.onAddToolResult)\n      throw new Error(\"Runtime does not support tool results.\");\n    this._store.onAddToolResult?.(options);\n  }\n\n  private updateMessages = (messages: readonly ThreadMessage[]) => {\n    const hasConverter = this._store.convertMessage !== undefined;\n    if (hasConverter) {\n      this._store.setMessages?.(\n        messages.flatMap(getExternalStoreMessage).filter((m) => m != null),\n      );\n    } else {\n      // TODO mark this as readonly in v0.8.0\n      this._store.setMessages?.(messages as ThreadMessage[]);\n    }\n  };\n}\n"], "mappings": ";AAQA;AAAA,EACE;AAAA,EACA;AAAA,OACK;AACP,SAAS,8BAA8B;AACvC,SAAS,eAAe,oBAAoB;AAC5C,SAAS,6BAA6B;AACtC,SAAS,4BAA4B;AAKrC,SAAS,6BAA6B;AAGtC,IAAM,cAAc,OAAO,OAAO,CAAC,CAAC;AAE7B,IAAM,qBAAqB,CAChC,WACA,aACG;AACH,SAAO,aAAa,SAAS,SAAS,SAAS,CAAC,GAAG,SAAS;AAC9D;AAEO,IAAM,iCAAN,cACG,sBAEV;AAAA,EACU,wBAAuC;AAAA,EAEvC,gBAAqC;AAAA,IAC3C,gBAAgB;AAAA,IAChB,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,UAAU;AAAA,EACZ;AAAA,EAEA,IAAW,eAAe;AACxB,WAAO,KAAK;AAAA,EACd;AAAA,EAEQ;AAAA,EACD;AAAA,EACP,IAAW,YAAY;AACrB,WAAO,KAAK,OAAO,aAAa;AAAA,EAClC;AAAA,EAEA,IAAoB,WAAW;AAC7B,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAW,WAAW;AACpB,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EAEO,cAA2C,CAAC;AAAA,EAC5C,SAAkB;AAAA,EAEjB,aAAa,IAAI,uBAAuB;AAAA,EAExC;AAAA,EAEQ,UAAU,WAAmB;AAC3C,QAAI,CAAC,KAAK,OAAO;AACf,YAAM,IAAI,MAAM,mCAAmC;AAErD,UAAM,UAAU,SAAS;AAAA,EAC3B;AAAA,EAEA,YACE,iBACA,OACA;AACA,UAAM,eAAe;AACrB,SAAK,sBAAsB,KAAK;AAAA,EAClC;AAAA,EAEO,sBAAsB,OAAkC;AAC7D,QAAI,KAAK,WAAW,MAAO;AAE3B,UAAM,YAAY,MAAM,aAAa;AACrC,SAAK,aAAa,MAAM,cAAc;AAEtC,UAAM,WAAW,KAAK;AACtB,SAAK,SAAS;AACd,SAAK,SAAS,MAAM;AACpB,SAAK,cAAc,MAAM,eAAe;AACxC,SAAK,gBAAgB;AAAA,MACnB,gBAAgB,KAAK,OAAO,gBAAgB;AAAA,MAC5C,MAAM,KAAK,OAAO,WAAW;AAAA,MAC7B,QAAQ,KAAK,OAAO,aAAa;AAAA,MACjC,QAAQ,KAAK,OAAO,aAAa;AAAA,MACjC,QAAQ,KAAK,OAAO,UAAU,WAAW;AAAA,MACzC,eAAe,KAAK,OAAO,uBAAuB,SAAS;AAAA;AAAA,MAC3D,aAAa,CAAC,CAAC,KAAK,OAAO,UAAU;AAAA,MACrC,UAAU,CAAC,CAAC,KAAK,OAAO,UAAU;AAAA,IACpC;AAEA,QAAI;AAEJ,QAAI,MAAM,mBAAmB;AAE3B,UACE,YACA,SAAS,cAAc,MAAM,aAC7B,SAAS,sBAAsB,MAAM,mBACrC;AACA,aAAK,mBAAmB;AACxB;AAAA,MACF;AAGA,WAAK,WAAW,MAAM;AACtB,WAAK,wBAAwB;AAC7B,WAAK,WAAW,OAAO,MAAM,iBAAiB;AAE9C,iBAAW,KAAK,WAAW,YAAY;AAAA,IACzC,WAAW,MAAM,UAAU;AAGzB,UAAI,UAAU;AAEZ,YAAI,SAAS,mBAAmB,MAAM,gBAAgB;AACpD,eAAK,aAAa,IAAI,uBAAuB;AAAA,QAC/C,WACE,SAAS,cAAc,MAAM,aAC7B,SAAS,aAAa,MAAM,UAC5B;AACA,eAAK,mBAAmB;AAExB;AAAA,QACF;AAAA,MACF;AAEA,iBAAW,CAAC,MAAM,iBACd,MAAM,WACN,KAAK,WAAW,gBAAgB,MAAM,UAAU,CAAC,OAAO,GAAG,QAAQ;AACjE,YAAI,CAAC,MAAM,eAAgB,QAAO;AAElC,cAAM,SAAS,QAAQ,MAAM,SAAU,SAAS;AAChD,cAAM,aAAa,cAAc,QAAQ,WAAW,KAAK;AAEzD,YACE,UACC,MAAM,SAAS,eACd,CAAC,aAAa,MAAM,MAAM,KAC1B,MAAM,WAAW;AAEnB,iBAAO;AAET,cAAM,cAAc,MAAM,eAAe,GAAG,GAAG;AAC/C,cAAM,aAAa;AAAA,UACjB;AAAA,UACA,IAAI,SAAS;AAAA,UACb;AAAA,QACF;AACA,QAAC,WAAmB,kBAAkB,IAAI;AAC1C,eAAO;AAAA,MACT,CAAC;AAEL,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,cAAM,UAAU,SAAS,CAAC;AAC1B,cAAM,SAAS,SAAS,IAAI,CAAC;AAC7B,aAAK,WAAW,mBAAmB,QAAQ,MAAM,MAAM,OAAO;AAAA,MAChE;AAAA,IACF,OAAO;AACL,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAGA,QAAI,SAAS,SAAS,EAAG,MAAK,kBAAkB;AAEhD,SAAK,UAAU,aAAa,YAAY,MAAM,aAAa,QAAQ;AACjE,UAAI,MAAM,WAAW;AACnB,aAAK,wBAAwB,WAAW;AAAA,MAC1C,OAAO;AACL,aAAK,wBAAwB,SAAS;AAAA,MACxC;AAAA,IACF;AAEA,QAAI,KAAK,uBAAuB;AAC9B,WAAK,WAAW,cAAc,KAAK,qBAAqB;AACxD,WAAK,wBAAwB;AAAA,IAC/B;AAEA,QAAI,mBAAmB,WAAW,QAAQ,GAAG;AAC3C,WAAK,wBAAwB,KAAK,WAAW;AAAA,QAC3C,SAAS,GAAG,EAAE,GAAG,MAAM;AAAA,QACvB;AAAA,UACE,MAAM;AAAA,UACN,SAAS,CAAC;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AAEA,SAAK,WAAW;AAAA,MACd,KAAK,yBAAyB,SAAS,GAAG,EAAE,GAAG,MAAM;AAAA,IACvD;AAEA,SAAK,YAAY,KAAK,WAAW,YAAY;AAC7C,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EAEgB,eAAe,UAAwB;AACrD,QAAI,CAAC,KAAK,OAAO;AACf,YAAM,IAAI,MAAM,8CAA8C;AAEhE,SAAK,WAAW,eAAe,QAAQ;AACvC,SAAK,eAAe,KAAK,WAAW,YAAY,CAAC;AAAA,EACnD;AAAA,EAEA,MAAa,OAAO,SAAuC;AACzD,QAAI,QAAQ,cAAc,KAAK,SAAS,GAAG,EAAE,GAAG,MAAM,OAAO;AAC3D,UAAI,CAAC,KAAK,OAAO;AACf,cAAM,IAAI,MAAM,4CAA4C;AAC9D,YAAM,KAAK,OAAO,OAAO,OAAO;AAAA,IAClC,OAAO;AACL,YAAM,KAAK,OAAO,MAAM,OAAO;AAAA,IACjC;AAAA,EACF;AAAA,EAEA,MAAa,SAAS,QAAuC;AAC3D,QAAI,CAAC,KAAK,OAAO;AACf,YAAM,IAAI,MAAM,8CAA8C;AAEhE,UAAM,KAAK,OAAO,SAAS,OAAO,UAAU,MAAM;AAAA,EACpD;AAAA,EAEA,MAAa,YAA2B;AACtC,UAAM,IAAI,MAAM,yCAAyC;AAAA,EAC3D;AAAA,EAEO,YAAkB;AACvB,QAAI,CAAC,KAAK,OAAO;AACf,YAAM,IAAI,MAAM,2CAA2C;AAE7D,SAAK,OAAO,SAAS;AAErB,QAAI,KAAK,uBAAuB;AAC9B,WAAK,WAAW,cAAc,KAAK,qBAAqB;AACxD,WAAK,wBAAwB;AAAA,IAC/B;AAEA,QAAI,WAAW,KAAK,WAAW,YAAY;AAC3C,UAAM,kBAAkB,SAAS,SAAS,SAAS,CAAC;AACpD,QACE,iBAAiB,SAAS,UAC1B,gBAAgB,OAAO,SAAS,GAAG,EAAE,GAAG,IACxC;AACA,WAAK,WAAW,cAAc,gBAAgB,EAAE;AAChD,UAAI,CAAC,KAAK,SAAS,KAAK,KAAK,GAAG;AAC9B,aAAK,SAAS,QAAQ,qBAAqB,eAAe,CAAC;AAAA,MAC7D;AAEA,iBAAW,KAAK,WAAW,YAAY;AAAA,IACzC,OAAO;AACL,WAAK,mBAAmB;AAAA,IAC1B;AAGA,eAAW,MAAM;AACf,WAAK,eAAe,QAAQ;AAAA,IAC9B,GAAG,CAAC;AAAA,EACN;AAAA,EAEO,cAAc,SAA+B;AAClD,QAAI,CAAC,KAAK,OAAO,mBAAmB,CAAC,KAAK,OAAO;AAC/C,YAAM,IAAI,MAAM,wCAAwC;AAC1D,SAAK,OAAO,kBAAkB,OAAO;AAAA,EACvC;AAAA,EAEQ,iBAAiB,CAAC,aAAuC;AAC/D,UAAM,eAAe,KAAK,OAAO,mBAAmB;AACpD,QAAI,cAAc;AAChB,WAAK,OAAO;AAAA,QACV,SAAS,QAAQ,uBAAuB,EAAE,OAAO,CAAC,MAAM,KAAK,IAAI;AAAA,MACnE;AAAA,IACF,OAAO;AAEL,WAAK,OAAO,cAAc,QAA2B;AAAA,IACvD;AAAA,EACF;AACF;", "names": []}