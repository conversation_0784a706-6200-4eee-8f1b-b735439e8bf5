{"version": 3, "sources": ["../../../src/primitives/message/MessagePartsGrouped.tsx"], "sourcesContent": ["\"use client\";\n\nimport {\n  type ComponentType,\n  type FC,\n  memo,\n  PropsWithChildren,\n  useMemo,\n} from \"react\";\nimport {\n  TextMessagePartProvider,\n  useMessagePart,\n  useMessagePartRuntime,\n  useToolUIs,\n} from \"../../context\";\nimport {\n  useMessage,\n  useMessageRuntime,\n} from \"../../context/react/MessageContext\";\nimport { MessagePartRuntimeProvider } from \"../../context/providers/MessagePartRuntimeProvider\";\nimport { MessagePartPrimitiveText } from \"../messagePart/MessagePartText\";\nimport { MessagePartPrimitiveImage } from \"../messagePart/MessagePartImage\";\nimport type {\n  Unstable_AudioMessagePartComponent,\n  EmptyMessagePartComponent,\n  TextMessagePartComponent,\n  ImageMessagePartComponent,\n  SourceMessagePartComponent,\n  ToolCallMessagePartComponent,\n  ToolCallMessagePartProps,\n  FileMessagePartComponent,\n  ReasoningMessagePartComponent,\n} from \"../../types/MessagePartComponentTypes\";\nimport { MessagePartPrimitiveInProgress } from \"../messagePart/MessagePartInProgress\";\nimport { MessagePartStatus } from \"../../types/AssistantTypes\";\n\ntype MessagePartGroup = {\n  groupKey: string | undefined;\n  indices: number[];\n};\n\nexport type GroupingFunction = (parts: readonly any[]) => MessagePartGroup[];\n\n/**\n * Groups message parts by their parent ID.\n * Parts without a parent ID appear in their chronological position as individual groups.\n * Parts with the same parent ID are grouped together at the position of their first occurrence.\n */\nconst groupMessagePartsByParentId: GroupingFunction = (\n  parts: readonly any[],\n): MessagePartGroup[] => {\n  // Map maintains insertion order, so groups appear in order of first occurrence\n  const groupMap = new Map<string, number[]>();\n\n  // Process each part in order\n  for (let i = 0; i < parts.length; i++) {\n    const part = parts[i];\n    const parentId = part?.parentId as string | undefined;\n\n    // For parts without parentId, assign a unique group ID to maintain their position\n    const groupId = parentId ?? `__ungrouped_${i}`;\n\n    // Get or create the indices array for this group\n    const indices = groupMap.get(groupId) ?? [];\n    indices.push(i);\n    groupMap.set(groupId, indices);\n  }\n\n  // Convert map to array of groups\n  const groups: MessagePartGroup[] = [];\n  for (const [groupId, indices] of groupMap) {\n    // Extract parentId (undefined for ungrouped parts)\n    const groupKey = groupId.startsWith(\"__ungrouped_\") ? undefined : groupId;\n    groups.push({ groupKey, indices });\n  }\n\n  return groups;\n};\n\nconst useMessagePartsGrouped = (\n  groupingFunction: GroupingFunction,\n): MessagePartGroup[] => {\n  const parts = useMessage((m) => m.content);\n\n  return useMemo(() => {\n    if (parts.length === 0) {\n      return [];\n    }\n    return groupingFunction(parts);\n  }, [parts, groupingFunction]);\n};\n\nexport namespace MessagePrimitiveUnstable_PartsGrouped {\n  export type Props = {\n    /**\n     * Function that takes an array of message parts and returns an array of groups.\n     * Each group contains a key (for identification) and an array of indices.\n     *\n     * @example\n     * ```tsx\n     * // Group by parent ID (default behavior)\n     * groupingFunction={(parts) => {\n     *   const groups = new Map<string, number[]>();\n     *   parts.forEach((part, i) => {\n     *     const key = part.parentId ?? `__ungrouped_${i}`;\n     *     const indices = groups.get(key) ?? [];\n     *     indices.push(i);\n     *     groups.set(key, indices);\n     *   });\n     *   return Array.from(groups.entries()).map(([key, indices]) => ({\n     *     key: key.startsWith(\"__ungrouped_\") ? undefined : key,\n     *     indices\n     *   }));\n     * }}\n     * ```\n     *\n     * @example\n     * ```tsx\n     * // Group by tool name\n     * import { groupMessagePartsByToolName } from \"@assistant-ui/react\";\n     *\n     * <MessagePrimitive.Unstable_PartsGrouped\n     *   groupingFunction={groupMessagePartsByToolName}\n     *   components={{\n     *     Group: ({ key, indices, children }) => {\n     *       if (!key) return <>{children}</>;\n     *       return (\n     *         <div className=\"tool-group\">\n     *           <h4>Tool: {key}</h4>\n     *           {children}\n     *         </div>\n     *       );\n     *     }\n     *   }}\n     * />\n     * ```\n     */\n    groupingFunction: GroupingFunction;\n\n    /**\n     * Component configuration for rendering different types of message content.\n     *\n     * You can provide custom components for each content type (text, image, file, etc.)\n     * and configure tool rendering behavior. If not provided, default components will be used.\n     */\n    components:\n      | {\n          /** Component for rendering empty messages */\n          Empty?: EmptyMessagePartComponent | undefined;\n          /** Component for rendering text content */\n          Text?: TextMessagePartComponent | undefined;\n          /** Component for rendering reasoning content (typically hidden) */\n          Reasoning?: ReasoningMessagePartComponent | undefined;\n          /** Component for rendering source content */\n          Source?: SourceMessagePartComponent | undefined;\n          /** Component for rendering image content */\n          Image?: ImageMessagePartComponent | undefined;\n          /** Component for rendering file content */\n          File?: FileMessagePartComponent | undefined;\n          /** Component for rendering audio content (experimental) */\n          Unstable_Audio?: Unstable_AudioMessagePartComponent | undefined;\n          /** Configuration for tool call rendering */\n          tools?:\n            | {\n                /** Map of tool names to their specific components */\n                by_name?:\n                  | Record<string, ToolCallMessagePartComponent | undefined>\n                  | undefined;\n                /** Fallback component for unregistered tools */\n                Fallback?: ComponentType<ToolCallMessagePartProps> | undefined;\n              }\n            | {\n                /** Override component that handles all tool calls */\n                Override: ComponentType<ToolCallMessagePartProps>;\n              }\n            | undefined;\n\n          /**\n           * Component for rendering grouped message parts.\n           *\n           * When provided, this component will automatically wrap message parts that share\n           * the same group key as determined by the groupingFunction.\n           *\n           * The component receives:\n           * - `groupKey`: The group key (or undefined for ungrouped parts)\n           * - `indices`: Array of indices for the parts in this group\n           * - `children`: The rendered message part components\n           *\n           * @example\n           * ```tsx\n           * // Collapsible group\n           * Group: ({ groupKey, indices, children }) => {\n           *   if (!groupKey) return <>{children}</>;\n           *   return (\n           *     <details className=\"message-group\">\n           *       <summary>\n           *         Group {groupKey} ({indices.length} parts)\n           *       </summary>\n           *       <div className=\"group-content\">\n           *         {children}\n           *       </div>\n           *     </details>\n           *   );\n           * }\n           * ```\n           *\n           * @param groupKey - The group key (undefined for ungrouped parts)\n           * @param indices - Array of indices for the parts in this group\n           * @param children - Rendered message part components to display within the group\n           */\n          Group?: ComponentType<\n            PropsWithChildren<{\n              groupKey: string | undefined;\n              indices: number[];\n            }>\n          >;\n        }\n      | undefined;\n  };\n}\n\nconst ToolUIDisplay = ({\n  Fallback,\n  ...props\n}: {\n  Fallback: ToolCallMessagePartComponent | undefined;\n} & ToolCallMessagePartProps) => {\n  const Render = useToolUIs((s) => s.getToolUI(props.toolName)) ?? Fallback;\n  if (!Render) return null;\n  return <Render {...props} />;\n};\n\nconst defaultComponents = {\n  Text: () => (\n    <p style={{ whiteSpace: \"pre-line\" }}>\n      <MessagePartPrimitiveText />\n      <MessagePartPrimitiveInProgress>\n        <span style={{ fontFamily: \"revert\" }}>{\" \\u25CF\"}</span>\n      </MessagePartPrimitiveInProgress>\n    </p>\n  ),\n  Reasoning: () => null,\n  Source: () => null,\n  Image: () => <MessagePartPrimitiveImage />,\n  File: () => null,\n  Unstable_Audio: () => null,\n  Group: ({ children }) => children,\n} satisfies MessagePrimitiveUnstable_PartsGrouped.Props[\"components\"];\n\ntype MessagePartComponentProps = {\n  components: MessagePrimitiveUnstable_PartsGrouped.Props[\"components\"];\n};\n\nconst MessagePartComponent: FC<MessagePartComponentProps> = ({\n  components: {\n    Text = defaultComponents.Text,\n    Reasoning = defaultComponents.Reasoning,\n    Image = defaultComponents.Image,\n    Source = defaultComponents.Source,\n    File = defaultComponents.File,\n    Unstable_Audio: Audio = defaultComponents.Unstable_Audio,\n    tools = {},\n  } = {},\n}) => {\n  const MessagePartRuntime = useMessagePartRuntime();\n\n  const part = useMessagePart();\n\n  const type = part.type;\n  if (type === \"tool-call\") {\n    const addResult = (result: any) => MessagePartRuntime.addToolResult(result);\n    if (\"Override\" in tools)\n      return <tools.Override {...part} addResult={addResult} />;\n    const Tool = tools.by_name?.[part.toolName] ?? tools.Fallback;\n    return <ToolUIDisplay {...part} Fallback={Tool} addResult={addResult} />;\n  }\n\n  if (part.status.type === \"requires-action\")\n    throw new Error(\"Encountered unexpected requires-action status\");\n\n  switch (type) {\n    case \"text\":\n      return <Text {...part} />;\n\n    case \"reasoning\":\n      return <Reasoning {...part} />;\n\n    case \"source\":\n      return <Source {...part} />;\n\n    case \"image\":\n      // eslint-disable-next-line jsx-a11y/alt-text\n      return <Image {...part} />;\n\n    case \"file\":\n      return <File {...part} />;\n\n    case \"audio\":\n      return <Audio {...part} />;\n\n    default:\n      const unhandledType: never = type;\n      throw new Error(`Unknown message part type: ${unhandledType}`);\n  }\n};\n\ntype MessagePartProps = {\n  partIndex: number;\n  components: MessagePrimitiveUnstable_PartsGrouped.Props[\"components\"];\n};\n\nconst MessagePartImpl: FC<MessagePartProps> = ({ partIndex, components }) => {\n  const messageRuntime = useMessageRuntime();\n  const runtime = useMemo(\n    () => messageRuntime.getMessagePartByIndex(partIndex),\n    [messageRuntime, partIndex],\n  );\n\n  return (\n    <MessagePartRuntimeProvider runtime={runtime}>\n      <MessagePartComponent components={components} />\n    </MessagePartRuntimeProvider>\n  );\n};\n\nconst MessagePart = memo(\n  MessagePartImpl,\n  (prev, next) =>\n    prev.partIndex === next.partIndex &&\n    prev.components?.Text === next.components?.Text &&\n    prev.components?.Reasoning === next.components?.Reasoning &&\n    prev.components?.Source === next.components?.Source &&\n    prev.components?.Image === next.components?.Image &&\n    prev.components?.File === next.components?.File &&\n    prev.components?.Unstable_Audio === next.components?.Unstable_Audio &&\n    prev.components?.tools === next.components?.tools &&\n    prev.components?.Group === next.components?.Group,\n);\n\nconst COMPLETE_STATUS: MessagePartStatus = Object.freeze({\n  type: \"complete\",\n});\n\nconst EmptyPartFallback: FC<{\n  status: MessagePartStatus;\n  component: TextMessagePartComponent;\n}> = ({ status, component: Component }) => {\n  return (\n    <TextMessagePartProvider text=\"\" isRunning={status.type === \"running\"}>\n      <Component type=\"text\" text=\"\" status={status} />\n    </TextMessagePartProvider>\n  );\n};\n\nconst EmptyPartsImpl: FC<MessagePartComponentProps> = ({ components }) => {\n  const status =\n    useMessage((s) => s.status as MessagePartStatus) ?? COMPLETE_STATUS;\n\n  if (components?.Empty) return <components.Empty status={status} />;\n\n  return (\n    <EmptyPartFallback\n      status={status}\n      component={components?.Text ?? defaultComponents.Text}\n    />\n  );\n};\n\nconst EmptyParts = memo(\n  EmptyPartsImpl,\n  (prev, next) =>\n    prev.components?.Empty === next.components?.Empty &&\n    prev.components?.Text === next.components?.Text,\n);\n\n/**\n * Renders the parts of a message grouped by a custom grouping function.\n *\n * This component allows you to group message parts based on any criteria you define.\n * The grouping function receives all message parts and returns an array of groups,\n * where each group has a key and an array of part indices.\n *\n * @example\n * ```tsx\n * // Group by parent ID (default behavior)\n * <MessagePrimitive.Unstable_PartsGrouped\n *   components={{\n *     Text: ({ text }) => <p className=\"message-text\">{text}</p>,\n *     Image: ({ image }) => <img src={image} alt=\"Message image\" />,\n *     Group: ({ groupKey, indices, children }) => {\n *       if (!groupKey) return <>{children}</>;\n *       return (\n *         <div className=\"parent-group border rounded p-4\">\n *           <h4>Parent ID: {groupKey}</h4>\n *           {children}\n *         </div>\n *       );\n *     }\n *   }}\n * />\n * ```\n *\n * @example\n * ```tsx\n * // Group by tool name\n * import { groupMessagePartsByToolName } from \"@assistant-ui/react\";\n *\n * <MessagePrimitive.Unstable_PartsGrouped\n *   groupingFunction={groupMessagePartsByToolName}\n *   components={{\n *     Group: ({ groupKey, indices, children }) => {\n *       if (!groupKey) return <>{children}</>;\n *       return (\n *         <div className=\"tool-group\">\n *           <h4>Tool: {groupKey}</h4>\n *           {children}\n *         </div>\n *       );\n *     }\n *   }}\n * />\n * ```\n */\nexport const MessagePrimitiveUnstable_PartsGrouped: FC<\n  MessagePrimitiveUnstable_PartsGrouped.Props\n> = ({ groupingFunction, components }) => {\n  const contentLength = useMessage((s) => s.content.length);\n  const messageGroups = useMessagePartsGrouped(groupingFunction);\n\n  const partsElements = useMemo(() => {\n    if (contentLength === 0) {\n      return <EmptyParts components={components} />;\n    }\n\n    return messageGroups.map((group, groupIndex) => {\n      const GroupComponent = components?.Group ?? defaultComponents.Group;\n\n      return (\n        <GroupComponent\n          key={`group-${groupIndex}-${group.groupKey ?? \"ungrouped\"}`}\n          groupKey={group.groupKey}\n          indices={group.indices}\n        >\n          {group.indices.map((partIndex) => (\n            <MessagePart\n              key={partIndex}\n              partIndex={partIndex}\n              components={components}\n            />\n          ))}\n        </GroupComponent>\n      );\n    });\n  }, [messageGroups, components, contentLength]);\n\n  return <>{partsElements}</>;\n};\n\nMessagePrimitiveUnstable_PartsGrouped.displayName =\n  \"MessagePrimitive.Unstable_PartsGrouped\";\n\n/**\n * Renders the parts of a message grouped by their parent ID.\n * This is a convenience wrapper around Unstable_PartsGrouped with parent ID grouping.\n *\n * @deprecated Use MessagePrimitive.Unstable_PartsGrouped instead for more flexibility\n */\nexport const MessagePrimitiveUnstable_PartsGroupedByParentId: FC<\n  Omit<MessagePrimitiveUnstable_PartsGrouped.Props, \"groupingFunction\">\n> = ({ components, ...props }) => {\n  return (\n    <MessagePrimitiveUnstable_PartsGrouped\n      {...props}\n      components={components}\n      groupingFunction={groupMessagePartsByParentId}\n    />\n  );\n};\n\nMessagePrimitiveUnstable_PartsGroupedByParentId.displayName =\n  \"MessagePrimitive.Unstable_PartsGroupedByParentId\";\n"], "mappings": ";;;AAEA;AAAA,EAGE;AAAA,EAEA;AAAA,OACK;AACP;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP;AAAA,EACE;AAAA,EACA;AAAA,OACK;AACP,SAAS,kCAAkC;AAC3C,SAAS,gCAAgC;AACzC,SAAS,iCAAiC;AAY1C,SAAS,sCAAsC;AAoMtC,SAkOA,UAlOA,KAKL,YALK;AArLT,IAAM,8BAAgD,CACpD,UACuB;AAEvB,QAAM,WAAW,oBAAI,IAAsB;AAG3C,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAM,OAAO,MAAM,CAAC;AACpB,UAAM,WAAW,MAAM;AAGvB,UAAM,UAAU,YAAY,eAAe,CAAC;AAG5C,UAAM,UAAU,SAAS,IAAI,OAAO,KAAK,CAAC;AAC1C,YAAQ,KAAK,CAAC;AACd,aAAS,IAAI,SAAS,OAAO;AAAA,EAC/B;AAGA,QAAM,SAA6B,CAAC;AACpC,aAAW,CAAC,SAAS,OAAO,KAAK,UAAU;AAEzC,UAAM,WAAW,QAAQ,WAAW,cAAc,IAAI,SAAY;AAClE,WAAO,KAAK,EAAE,UAAU,QAAQ,CAAC;AAAA,EACnC;AAEA,SAAO;AACT;AAEA,IAAM,yBAAyB,CAC7B,qBACuB;AACvB,QAAM,QAAQ,WAAW,CAAC,MAAM,EAAE,OAAO;AAEzC,SAAO,QAAQ,MAAM;AACnB,QAAI,MAAM,WAAW,GAAG;AACtB,aAAO,CAAC;AAAA,IACV;AACA,WAAO,iBAAiB,KAAK;AAAA,EAC/B,GAAG,CAAC,OAAO,gBAAgB,CAAC;AAC9B;AAmIA,IAAM,gBAAgB,CAAC;AAAA,EACrB;AAAA,EACA,GAAG;AACL,MAEiC;AAC/B,QAAM,SAAS,WAAW,CAAC,MAAM,EAAE,UAAU,MAAM,QAAQ,CAAC,KAAK;AACjE,MAAI,CAAC,OAAQ,QAAO;AACpB,SAAO,oBAAC,UAAQ,GAAG,OAAO;AAC5B;AAEA,IAAM,oBAAoB;AAAA,EACxB,MAAM,MACJ,qBAAC,OAAE,OAAO,EAAE,YAAY,WAAW,GACjC;AAAA,wBAAC,4BAAyB;AAAA,IAC1B,oBAAC,kCACC,8BAAC,UAAK,OAAO,EAAE,YAAY,SAAS,GAAI,qBAAU,GACpD;AAAA,KACF;AAAA,EAEF,WAAW,MAAM;AAAA,EACjB,QAAQ,MAAM;AAAA,EACd,OAAO,MAAM,oBAAC,6BAA0B;AAAA,EACxC,MAAM,MAAM;AAAA,EACZ,gBAAgB,MAAM;AAAA,EACtB,OAAO,CAAC,EAAE,SAAS,MAAM;AAC3B;AAMA,IAAM,uBAAsD,CAAC;AAAA,EAC3D,YAAY;AAAA,IACV,OAAO,kBAAkB;AAAA,IACzB,YAAY,kBAAkB;AAAA,IAC9B,QAAQ,kBAAkB;AAAA,IAC1B,SAAS,kBAAkB;AAAA,IAC3B,OAAO,kBAAkB;AAAA,IACzB,gBAAgB,QAAQ,kBAAkB;AAAA,IAC1C,QAAQ,CAAC;AAAA,EACX,IAAI,CAAC;AACP,MAAM;AACJ,QAAM,qBAAqB,sBAAsB;AAEjD,QAAM,OAAO,eAAe;AAE5B,QAAM,OAAO,KAAK;AAClB,MAAI,SAAS,aAAa;AACxB,UAAM,YAAY,CAAC,WAAgB,mBAAmB,cAAc,MAAM;AAC1E,QAAI,cAAc;AAChB,aAAO,oBAAC,MAAM,UAAN,EAAgB,GAAG,MAAM,WAAsB;AACzD,UAAM,OAAO,MAAM,UAAU,KAAK,QAAQ,KAAK,MAAM;AACrD,WAAO,oBAAC,iBAAe,GAAG,MAAM,UAAU,MAAM,WAAsB;AAAA,EACxE;AAEA,MAAI,KAAK,OAAO,SAAS;AACvB,UAAM,IAAI,MAAM,+CAA+C;AAEjE,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,aAAO,oBAAC,QAAM,GAAG,MAAM;AAAA,IAEzB,KAAK;AACH,aAAO,oBAAC,aAAW,GAAG,MAAM;AAAA,IAE9B,KAAK;AACH,aAAO,oBAAC,UAAQ,GAAG,MAAM;AAAA,IAE3B,KAAK;AAEH,aAAO,oBAAC,SAAO,GAAG,MAAM;AAAA,IAE1B,KAAK;AACH,aAAO,oBAAC,QAAM,GAAG,MAAM;AAAA,IAEzB,KAAK;AACH,aAAO,oBAAC,SAAO,GAAG,MAAM;AAAA,IAE1B;AACE,YAAM,gBAAuB;AAC7B,YAAM,IAAI,MAAM,8BAA8B,aAAa,EAAE;AAAA,EACjE;AACF;AAOA,IAAM,kBAAwC,CAAC,EAAE,WAAW,WAAW,MAAM;AAC3E,QAAM,iBAAiB,kBAAkB;AACzC,QAAM,UAAU;AAAA,IACd,MAAM,eAAe,sBAAsB,SAAS;AAAA,IACpD,CAAC,gBAAgB,SAAS;AAAA,EAC5B;AAEA,SACE,oBAAC,8BAA2B,SAC1B,8BAAC,wBAAqB,YAAwB,GAChD;AAEJ;AAEA,IAAM,cAAc;AAAA,EAClB;AAAA,EACA,CAAC,MAAM,SACL,KAAK,cAAc,KAAK,aACxB,KAAK,YAAY,SAAS,KAAK,YAAY,QAC3C,KAAK,YAAY,cAAc,KAAK,YAAY,aAChD,KAAK,YAAY,WAAW,KAAK,YAAY,UAC7C,KAAK,YAAY,UAAU,KAAK,YAAY,SAC5C,KAAK,YAAY,SAAS,KAAK,YAAY,QAC3C,KAAK,YAAY,mBAAmB,KAAK,YAAY,kBACrD,KAAK,YAAY,UAAU,KAAK,YAAY,SAC5C,KAAK,YAAY,UAAU,KAAK,YAAY;AAChD;AAEA,IAAM,kBAAqC,OAAO,OAAO;AAAA,EACvD,MAAM;AACR,CAAC;AAED,IAAM,oBAGD,CAAC,EAAE,QAAQ,WAAW,UAAU,MAAM;AACzC,SACE,oBAAC,2BAAwB,MAAK,IAAG,WAAW,OAAO,SAAS,WAC1D,8BAAC,aAAU,MAAK,QAAO,MAAK,IAAG,QAAgB,GACjD;AAEJ;AAEA,IAAM,iBAAgD,CAAC,EAAE,WAAW,MAAM;AACxE,QAAM,SACJ,WAAW,CAAC,MAAM,EAAE,MAA2B,KAAK;AAEtD,MAAI,YAAY,MAAO,QAAO,oBAAC,WAAW,OAAX,EAAiB,QAAgB;AAEhE,SACE;AAAA,IAAC;AAAA;AAAA,MACC;AAAA,MACA,WAAW,YAAY,QAAQ,kBAAkB;AAAA;AAAA,EACnD;AAEJ;AAEA,IAAM,aAAa;AAAA,EACjB;AAAA,EACA,CAAC,MAAM,SACL,KAAK,YAAY,UAAU,KAAK,YAAY,SAC5C,KAAK,YAAY,SAAS,KAAK,YAAY;AAC/C;AAkDO,IAAM,wCAET,CAAC,EAAE,kBAAkB,WAAW,MAAM;AACxC,QAAM,gBAAgB,WAAW,CAAC,MAAM,EAAE,QAAQ,MAAM;AACxD,QAAM,gBAAgB,uBAAuB,gBAAgB;AAE7D,QAAM,gBAAgB,QAAQ,MAAM;AAClC,QAAI,kBAAkB,GAAG;AACvB,aAAO,oBAAC,cAAW,YAAwB;AAAA,IAC7C;AAEA,WAAO,cAAc,IAAI,CAAC,OAAO,eAAe;AAC9C,YAAM,iBAAiB,YAAY,SAAS,kBAAkB;AAE9D,aACE;AAAA,QAAC;AAAA;AAAA,UAEC,UAAU,MAAM;AAAA,UAChB,SAAS,MAAM;AAAA,UAEd,gBAAM,QAAQ,IAAI,CAAC,cAClB;AAAA,YAAC;AAAA;AAAA,cAEC;AAAA,cACA;AAAA;AAAA,YAFK;AAAA,UAGP,CACD;AAAA;AAAA,QAVI,SAAS,UAAU,IAAI,MAAM,YAAY,WAAW;AAAA,MAW3D;AAAA,IAEJ,CAAC;AAAA,EACH,GAAG,CAAC,eAAe,YAAY,aAAa,CAAC;AAE7C,SAAO,gCAAG,yBAAc;AAC1B;AAEA,sCAAsC,cACpC;AAQK,IAAM,kDAET,CAAC,EAAE,YAAY,GAAG,MAAM,MAAM;AAChC,SACE;AAAA,IAAC;AAAA;AAAA,MACE,GAAG;AAAA,MACJ;AAAA,MACA,kBAAkB;AAAA;AAAA,EACpB;AAEJ;AAEA,gDAAgD,cAC9C;", "names": []}