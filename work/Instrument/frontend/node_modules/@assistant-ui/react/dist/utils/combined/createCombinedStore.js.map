{"version": 3, "sources": ["../../../src/utils/combined/createCombinedStore.ts"], "sourcesContent": ["\"use client\";\n\nimport { useSyncExternalStore } from \"react\";\nimport type { Unsubscribe } from \"../../types/Unsubscribe\";\n\nexport type StoreOrRuntime<T> = {\n  getState: () => T;\n  subscribe: (callback: () => void) => Unsubscribe;\n};\n\nexport type CombinedSelector<T extends Array<unknown>, R> = (...args: T) => R;\n\nexport const createCombinedStore = <T extends Array<unknown>, R>(stores: {\n  [K in keyof T]: StoreOrRuntime<T[K]>;\n}) => {\n  const subscribe = (callback: () => void): Unsubscribe => {\n    const unsubscribes = stores.map((store) => store.subscribe(callback));\n    return () => {\n      for (const unsub of unsubscribes) {\n        unsub();\n      }\n    };\n  };\n\n  return (selector: CombinedSelector<T, R>): R => {\n    const getSnapshot = (): R =>\n      selector(...(stores.map((store) => store.getState()) as T));\n\n    return useSyncExternalStore(subscribe, getSnapshot, getSnapshot);\n  };\n};\n"], "mappings": ";;;AAEA,SAAS,4BAA4B;AAU9B,IAAM,sBAAsB,CAA8B,WAE3D;AACJ,QAAM,YAAY,CAAC,aAAsC;AACvD,UAAM,eAAe,OAAO,IAAI,CAAC,UAAU,MAAM,UAAU,QAAQ,CAAC;AACpE,WAAO,MAAM;AACX,iBAAW,SAAS,cAAc;AAChC,cAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAEA,SAAO,CAAC,aAAwC;AAC9C,UAAM,cAAc,MAClB,SAAS,GAAI,OAAO,IAAI,CAAC,UAAU,MAAM,SAAS,CAAC,CAAO;AAE5D,WAAO,qBAAqB,WAAW,aAAa,WAAW;AAAA,EACjE;AACF;", "names": []}