{"version": 3, "sources": ["../../../src/runtimes/external-store/index.ts"], "sourcesContent": ["export type {\n  ExternalStoreAdapter,\n  ExternalStoreMessageConverter,\n  ExternalStoreThreadListAdapter,\n  ExternalStoreThreadData,\n} from \"./ExternalStoreAdapter\";\nexport type { ThreadMessageLike } from \"./ThreadMessageLike\";\nexport { useExternalStoreRuntime } from \"./useExternalStoreRuntime\";\nexport {\n  getExternalStoreMessage,\n  getExternalStoreMessages,\n} from \"./getExternalStoreMessage\";\nexport {\n  useExternalMessageConverter,\n  convertExternalMessages as unstable_convertExternalMessages,\n} from \"./external-message-converter\";\nexport { createMessageConverter as unstable_createMessageConverter } from \"./createMessageConverter\";\n"], "mappings": ";AAOA,SAAS,+BAA+B;AACxC;AAAA,EACE;AAAA,EACA;AAAA,OACK;AACP;AAAA,EACE;AAAA,EAC2B;AAAA,OACtB;AACP,SAAmC,8BAAuC;", "names": []}