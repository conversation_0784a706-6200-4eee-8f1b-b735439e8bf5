{"version": 3, "sources": ["../../../src/primitives/branchPicker/BranchPickerPrevious.tsx"], "sourcesContent": ["\"use client\";\n\nimport {\n  ActionButtonElement,\n  ActionButtonProps,\n  createActionButton,\n} from \"../../utils/createActionButton\";\nimport { useCallback } from \"react\";\nimport {\n  useMessage,\n  useMessageRuntime,\n} from \"../../context/react/MessageContext\";\n\n/**\n * Hook that provides navigation to the previous branch functionality.\n *\n * This hook returns a callback function that switches to the previous branch\n * in the message branch tree, or null if there is no previous branch available.\n *\n * @returns A previous branch callback function, or null if navigation is disabled\n *\n * @example\n * ```tsx\n * function CustomPreviousButton() {\n *   const previous = useBranchPickerPrevious();\n *\n *   return (\n *     <button onClick={previous} disabled={!previous}>\n *       {previous ? \"Previous Branch\" : \"No Previous Branch\"}\n *     </button>\n *   );\n * }\n * ```\n */\nconst useBranchPickerPrevious = () => {\n  const messageRuntime = useMessageRuntime();\n  const disabled = useMessage((m) => m.branchNumber <= 1);\n\n  const callback = useCallback(() => {\n    messageRuntime.switchToBranch({ position: \"previous\" });\n  }, [messageRuntime]);\n\n  if (disabled) return null;\n  return callback;\n};\n\nexport namespace BranchPickerPrimitivePrevious {\n  export type Element = ActionButtonElement;\n  /**\n   * Props for the BranchPickerPrimitive.Previous component.\n   * Inherits all button element props and action button functionality.\n   */\n  export type Props = ActionButtonProps<typeof useBranchPickerPrevious>;\n}\n\n/**\n * A button component that navigates to the previous branch in the message tree.\n *\n * This component automatically handles switching to the previous available branch\n * and is disabled when there are no previous branches to navigate to.\n *\n * @example\n * ```tsx\n * <BranchPickerPrimitive.Previous>\n *   ← Previous\n * </BranchPickerPrimitive.Previous>\n * ```\n */\nexport const BranchPickerPrimitivePrevious = createActionButton(\n  \"BranchPickerPrimitive.Previous\",\n  useBranchPickerPrevious,\n);\n"], "mappings": ";;;AAEA;AAAA,EAGE;AAAA,OACK;AACP,SAAS,mBAAmB;AAC5B;AAAA,EACE;AAAA,EACA;AAAA,OACK;AAuBP,IAAM,0BAA0B,MAAM;AACpC,QAAM,iBAAiB,kBAAkB;AACzC,QAAM,WAAW,WAAW,CAAC,MAAM,EAAE,gBAAgB,CAAC;AAEtD,QAAM,WAAW,YAAY,MAAM;AACjC,mBAAe,eAAe,EAAE,UAAU,WAAW,CAAC;AAAA,EACxD,GAAG,CAAC,cAAc,CAAC;AAEnB,MAAI,SAAU,QAAO;AACrB,SAAO;AACT;AAwBO,IAAM,gCAAgC;AAAA,EAC3C;AAAA,EACA;AACF;", "names": []}