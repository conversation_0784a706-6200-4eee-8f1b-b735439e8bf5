import type { FC } from "react";
export declare namespace BranchPickerPrimitiveCount {
    /**
     * Props for the BranchPickerPrimitive.Count component.
     * This component takes no props.
     */
    type Props = Record<string, never>;
}
/**
 * A component that displays the total number of branches for the current message.
 *
 * This component renders the branch count as plain text, useful for showing
 * users how many alternative responses are available.
 *
 * @example
 * ```tsx
 * <div>
 *   Branch <BranchPickerPrimitive.Count /> of {totalBranches}
 * </div>
 * ```
 */
export declare const BranchPickerPrimitiveCount: FC<BranchPickerPrimitiveCount.Props>;
//# sourceMappingURL=BranchPickerCount.d.ts.map