{"version": 3, "sources": ["../../../src/runtimes/external-store/useExternalStoreRuntime.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useMemo, useState } from \"react\";\nimport { ExternalStoreRuntimeCore } from \"./ExternalStoreRuntimeCore\";\nimport { ExternalStoreAdapter } from \"./ExternalStoreAdapter\";\nimport {\n  AssistantRuntime,\n  AssistantRuntimeImpl,\n} from \"../../api/AssistantRuntime\";\nimport { useRuntimeAdapters } from \"../adapters/RuntimeAdapterProvider\";\n\nexport const useExternalStoreRuntime = <T,>(\n  store: ExternalStoreAdapter<T>,\n): AssistantRuntime => {\n  const [runtime] = useState(() => new ExternalStoreRuntimeCore(store));\n\n  useEffect(() => {\n    runtime.setAdapter(store);\n  });\n\n  const { modelContext } = useRuntimeAdapters() ?? {};\n\n  useEffect(() => {\n    if (!modelContext) return undefined;\n    return runtime.registerModelContextProvider(modelContext);\n  }, [modelContext, runtime]);\n\n  return useMemo(() => new AssistantRuntimeImpl(runtime), [runtime]);\n};\n"], "mappings": ";;;AAEA,SAAS,WAAW,SAAS,gBAAgB;AAC7C,SAAS,gCAAgC;AAEzC;AAAA,EAEE;AAAA,OACK;AACP,SAAS,0BAA0B;AAE5B,IAAM,0BAA0B,CACrC,UACqB;AACrB,QAAM,CAAC,OAAO,IAAI,SAAS,MAAM,IAAI,yBAAyB,KAAK,CAAC;AAEpE,YAAU,MAAM;AACd,YAAQ,WAAW,KAAK;AAAA,EAC1B,CAAC;AAED,QAAM,EAAE,aAAa,IAAI,mBAAmB,KAAK,CAAC;AAElD,YAAU,MAAM;AACd,QAAI,CAAC,aAAc,QAAO;AAC1B,WAAO,QAAQ,6BAA6B,YAAY;AAAA,EAC1D,GAAG,CAAC,cAAc,OAAO,CAAC;AAE1B,SAAO,QAAQ,MAAM,IAAI,qBAAqB,OAAO,GAAG,CAAC,OAAO,CAAC;AACnE;", "names": []}