{"version": 3, "sources": ["../../../src/primitives/actionBar/ActionBarStopSpeaking.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef } from \"react\";\nimport { ActionButtonProps } from \"../../utils/createActionButton\";\nimport { useEscapeKeydown } from \"@radix-ui/react-use-escape-keydown\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { useCallback } from \"react\";\nimport {\n  useMessage,\n  useMessageRuntime,\n} from \"../../context/react/MessageContext\";\n\nconst useActionBarStopSpeaking = () => {\n  const messageRuntime = useMessageRuntime();\n  const isSpeaking = useMessage((u) => u.speech != null);\n\n  const callback = useCallback(() => {\n    messageRuntime.stopSpeaking();\n  }, [messageRuntime]);\n\n  if (!isSpeaking) return null;\n\n  return callback;\n};\n\nexport namespace ActionBarPrimitiveStopSpeaking {\n  export type Element = HTMLButtonElement;\n  export type Props = ActionButtonProps<typeof useActionBarStopSpeaking>;\n}\n\nexport const ActionBarPrimitiveStopSpeaking = forwardRef<\n  ActionBarPrimitiveStopSpeaking.Element,\n  ActionBarPrimitiveStopSpeaking.Props\n>((props, ref) => {\n  const callback = useActionBarStopSpeaking();\n\n  // TODO this stops working if the user is not hovering over an older message\n  useEscapeKeydown((e) => {\n    if (callback) {\n      e.preventDefault();\n      callback();\n    }\n  });\n\n  return (\n    <Primitive.button\n      type=\"button\"\n      disabled={!callback}\n      {...props}\n      ref={ref}\n      onClick={composeEventHandlers(props.onClick, () => {\n        callback?.();\n      })}\n    />\n  );\n});\n\nActionBarPrimitiveStopSpeaking.displayName = \"ActionBarPrimitive.StopSpeaking\";\n"], "mappings": ";;;AAEA,SAAS,kBAAkB;AAE3B,SAAS,wBAAwB;AACjC,SAAS,iBAAiB;AAC1B,SAAS,4BAA4B;AACrC,SAAS,mBAAmB;AAC5B;AAAA,EACE;AAAA,EACA;AAAA,OACK;AAmCH;AAjCJ,IAAM,2BAA2B,MAAM;AACrC,QAAM,iBAAiB,kBAAkB;AACzC,QAAM,aAAa,WAAW,CAAC,MAAM,EAAE,UAAU,IAAI;AAErD,QAAM,WAAW,YAAY,MAAM;AACjC,mBAAe,aAAa;AAAA,EAC9B,GAAG,CAAC,cAAc,CAAC;AAEnB,MAAI,CAAC,WAAY,QAAO;AAExB,SAAO;AACT;AAOO,IAAM,iCAAiC,WAG5C,CAAC,OAAO,QAAQ;AAChB,QAAM,WAAW,yBAAyB;AAG1C,mBAAiB,CAAC,MAAM;AACtB,QAAI,UAAU;AACZ,QAAE,eAAe;AACjB,eAAS;AAAA,IACX;AAAA,EACF,CAAC;AAED,SACE;AAAA,IAAC,UAAU;AAAA,IAAV;AAAA,MACC,MAAK;AAAA,MACL,UAAU,CAAC;AAAA,MACV,GAAG;AAAA,MACJ;AAAA,MACA,SAAS,qBAAqB,MAAM,SAAS,MAAM;AACjD,mBAAW;AAAA,MACb,CAAC;AAAA;AAAA,EACH;AAEJ,CAAC;AAED,+BAA+B,cAAc;", "names": []}