{"version": 3, "sources": ["../../../src/primitives/assistantModal/AssistantModalTrigger.tsx"], "sourcesContent": ["import { ComponentPropsWithoutRef, ComponentRef, forwardRef } from \"react\";\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\";\nimport { ScopedProps, usePopoverScope } from \"./scope\";\n\nexport namespace AssistantModalPrimitiveTrigger {\n  export type Element = ComponentRef<typeof PopoverPrimitive.Trigger>;\n  export type Props = ComponentPropsWithoutRef<typeof PopoverPrimitive.Trigger>;\n}\n\nexport const AssistantModalPrimitiveTrigger = forwardRef<\n  AssistantModalPrimitiveTrigger.Element,\n  AssistantModalPrimitiveTrigger.Props\n>(\n  (\n    {\n      __scopeAssistantModal,\n      ...rest\n    }: ScopedProps<AssistantModalPrimitiveTrigger.Props>,\n    ref,\n  ) => {\n    const scope = usePopoverScope(__scopeAssistantModal);\n\n    return <PopoverPrimitive.Trigger {...scope} {...rest} ref={ref} />;\n  },\n);\n\nAssistantModalPrimitiveTrigger.displayName = \"AssistantModalPrimitive.Trigger\";\n"], "mappings": ";AAAA,SAAiD,kBAAkB;AACnE,YAAY,sBAAsB;AAClC,SAAsB,uBAAuB;AAoBlC;AAbJ,IAAM,iCAAiC;AAAA,EAI5C,CACE;AAAA,IACE;AAAA,IACA,GAAG;AAAA,EACL,GACA,QACG;AACH,UAAM,QAAQ,gBAAgB,qBAAqB;AAEnD,WAAO,oBAAkB,0BAAjB,EAA0B,GAAG,OAAQ,GAAG,MAAM,KAAU;AAAA,EAClE;AACF;AAEA,+BAA+B,cAAc;", "names": []}