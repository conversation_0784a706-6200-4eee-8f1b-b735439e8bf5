{"version": 3, "sources": ["../../../src/primitives/composer/ComposerSend.tsx"], "sourcesContent": ["\"use client\";\n\nimport {\n  ActionButtonElement,\n  ActionButtonProps,\n  createActionButton,\n} from \"../../utils/createActionButton\";\nimport { useCallback } from \"react\";\nimport { useCombinedStore } from \"../../utils/combined/useCombinedStore\";\nimport { useThreadRuntime } from \"../../context/react/ThreadContext\";\nimport { useComposerRuntime } from \"../../context\";\n\nexport const useComposerSend = () => {\n  const composerRuntime = useComposerRuntime();\n  const threadRuntime = useThreadRuntime();\n\n  const disabled = useCombinedStore(\n    [threadRuntime, composerRuntime],\n    (t, c) => t.isRunning || !c.isEditing || c.isEmpty,\n  );\n\n  const callback = useCallback(() => {\n    composerRuntime.send();\n  }, [composerRuntime]);\n\n  if (disabled) return null;\n  return callback;\n};\n\nexport namespace ComposerPrimitiveSend {\n  export type Element = ActionButtonElement;\n  /**\n   * Props for the ComposerPrimitive.Send component.\n   * Inherits all button element props and action button functionality.\n   */\n  export type Props = ActionButtonProps<typeof useComposerSend>;\n}\n\n/**\n * A button component that sends the current message in the composer.\n *\n * This component automatically handles the send functionality and is disabled\n * when sending is not available (e.g., when the thread is running, the composer\n * is empty, or not in editing mode).\n *\n * @example\n * ```tsx\n * <ComposerPrimitive.Send>\n *   Send Message\n * </ComposerPrimitive.Send>\n * ```\n */\nexport const ComposerPrimitiveSend = createActionButton(\n  \"ComposerPrimitive.Send\",\n  useComposerSend,\n);\n"], "mappings": ";;;AAEA;AAAA,EAGE;AAAA,OACK;AACP,SAAS,mBAAmB;AAC5B,SAAS,wBAAwB;AACjC,SAAS,wBAAwB;AACjC,SAAS,0BAA0B;AAE5B,IAAM,kBAAkB,MAAM;AACnC,QAAM,kBAAkB,mBAAmB;AAC3C,QAAM,gBAAgB,iBAAiB;AAEvC,QAAM,WAAW;AAAA,IACf,CAAC,eAAe,eAAe;AAAA,IAC/B,CAAC,GAAG,MAAM,EAAE,aAAa,CAAC,EAAE,aAAa,EAAE;AAAA,EAC7C;AAEA,QAAM,WAAW,YAAY,MAAM;AACjC,oBAAgB,KAAK;AAAA,EACvB,GAAG,CAAC,eAAe,CAAC;AAEpB,MAAI,SAAU,QAAO;AACrB,SAAO;AACT;AAyBO,IAAM,wBAAwB;AAAA,EACnC;AAAA,EACA;AACF;", "names": []}