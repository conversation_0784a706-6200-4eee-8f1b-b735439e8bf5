{"version": 3, "sources": ["../../../src/primitives/error/ErrorMessage.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { type ComponentRef, forwardRef, ComponentPropsWithoutRef } from \"react\";\nimport { useMessage } from \"../../context/react/MessageContext\";\n\nexport namespace ErrorPrimitiveMessage {\n  export type Element = ComponentRef<typeof Primitive.span>;\n  export type Props = ComponentPropsWithoutRef<typeof Primitive.span>;\n}\n\nexport const ErrorPrimitiveMessage = forwardRef<\n  ErrorPrimitiveMessage.Element,\n  ErrorPrimitiveMessage.Props\n>(({ children, ...props }, forwardRef) => {\n  const error = useMessage((m) => {\n    return m.status?.type === \"incomplete\" && m.status.reason === \"error\"\n      ? m.status.error\n      : undefined;\n  });\n\n  if (error === undefined) return null;\n\n  return (\n    <Primitive.span {...props} ref={forwardRef}>\n      {children ?? String(error)}\n    </Primitive.span>\n  );\n});\n\nErrorPrimitiveMessage.displayName = \"ErrorPrimitive.Message\";\n"], "mappings": ";;;AAEA,SAAS,iBAAiB;AAC1B,SAA4B,kBAA4C;AACxE,SAAS,kBAAkB;AAoBvB;AAbG,IAAM,wBAAwB,WAGnC,CAAC,EAAE,UAAU,GAAG,MAAM,GAAGA,gBAAe;AACxC,QAAM,QAAQ,WAAW,CAAC,MAAM;AAC9B,WAAO,EAAE,QAAQ,SAAS,gBAAgB,EAAE,OAAO,WAAW,UAC1D,EAAE,OAAO,QACT;AAAA,EACN,CAAC;AAED,MAAI,UAAU,OAAW,QAAO;AAEhC,SACE,oBAAC,UAAU,MAAV,EAAgB,GAAG,OAAO,KAAKA,aAC7B,sBAAY,OAAO,KAAK,GAC3B;AAEJ,CAAC;AAED,sBAAsB,cAAc;", "names": ["forwardRef"]}