import { type TextareaAutosizeProps } from "react-textarea-autosize";
export declare namespace ComposerPrimitiveInput {
    type Element = HTMLTextAreaElement;
    type Props = TextareaAutosizeProps & {
        /**
         * Whether to render as a child component using Slot.
         * When true, the component will merge its props with its child.
         */
        asChild?: boolean | undefined;
        /**
         * Whether to submit the message when En<PERSON> is pressed (without Shift).
         * @default true
         */
        submitOnEnter?: boolean | undefined;
        /**
         * Whether to cancel message composition when Escape is pressed.
         * @default true
         */
        cancelOnEscape?: boolean | undefined;
        /**
         * Whether to automatically focus the input when a new run starts.
         * @default true
         */
        unstable_focusOnRunStart?: boolean | undefined;
        /**
         * Whether to automatically focus the input when scrolling to bottom.
         * @default true
         */
        unstable_focusOnScrollToBottom?: boolean | undefined;
        /**
         * Whether to automatically focus the input when switching threads.
         * @default true
         */
        unstable_focusOnThreadSwitched?: boolean | undefined;
        /**
         * Whether to automatically add pasted files as attachments.
         * @default true
         */
        addAttachmentOnPaste?: boolean | undefined;
    };
}
/**
 * A text input component for composing messages.
 *
 * This component provides a rich text input experience with automatic resizing,
 * keyboard shortcuts, file paste support, and intelligent focus management.
 * It integrates with the composer context to manage message state and submission.
 *
 * @example
 * ```tsx
 * <ComposerPrimitive.Input
 *   placeholder="Type your message..."
 *   submitOnEnter={true}
 *   addAttachmentOnPaste={true}
 * />
 * ```
 */
export declare const ComposerPrimitiveInput: import("react").ForwardRefExoticComponent<TextareaAutosizeProps & {
    /**
     * Whether to render as a child component using Slot.
     * When true, the component will merge its props with its child.
     */
    asChild?: boolean | undefined;
    /**
     * Whether to submit the message when Enter is pressed (without Shift).
     * @default true
     */
    submitOnEnter?: boolean | undefined;
    /**
     * Whether to cancel message composition when Escape is pressed.
     * @default true
     */
    cancelOnEscape?: boolean | undefined;
    /**
     * Whether to automatically focus the input when a new run starts.
     * @default true
     */
    unstable_focusOnRunStart?: boolean | undefined;
    /**
     * Whether to automatically focus the input when scrolling to bottom.
     * @default true
     */
    unstable_focusOnScrollToBottom?: boolean | undefined;
    /**
     * Whether to automatically focus the input when switching threads.
     * @default true
     */
    unstable_focusOnThreadSwitched?: boolean | undefined;
    /**
     * Whether to automatically add pasted files as attachments.
     * @default true
     */
    addAttachmentOnPaste?: boolean | undefined;
} & import("react").RefAttributes<HTMLTextAreaElement>>;
//# sourceMappingURL=ComposerInput.d.ts.map