import { ActionButtonElement, ActionButtonProps } from "../../utils/createActionButton";
declare const useThreadSuggestion: ({ prompt, autoSend, }: {
    prompt: string;
    method?: "replace";
    autoSend?: boolean | undefined;
}) => (() => void) | null;
export declare namespace ThreadPrimitiveSuggestion {
    type Element = ActionButtonElement;
    type Props = ActionButtonProps<typeof useThreadSuggestion>;
}
export declare const ThreadPrimitiveSuggestion: import("react").ForwardRefExoticComponent<Omit<import("react").ClassAttributes<HTMLButtonElement> & import("react").ButtonHTMLAttributes<HTMLButtonElement> & {
    asChild?: boolean;
}, "ref"> & {
    prompt: string;
    method?: "replace";
    autoSend?: boolean | undefined;
} & import("react").RefAttributes<HTMLButtonElement>>;
export {};
//# sourceMappingURL=ThreadSuggestion.d.ts.map