{"version": 3, "sources": ["../../../src/primitives/composer/ComposerRoot.tsx"], "sourcesContent": ["\"use client\";\n\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport {\n  type ComponentRef,\n  type FormEvent,\n  forwardRef,\n  ComponentPropsWithoutRef,\n} from \"react\";\nimport { useComposerSend } from \"./ComposerSend\";\n\nexport namespace ComposerPrimitiveRoot {\n  export type Element = ComponentRef<typeof Primitive.form>;\n  /**\n   * Props for the ComposerPrimitive.Root component.\n   * Accepts all standard form element props.\n   */\n  export type Props = ComponentPropsWithoutRef<typeof Primitive.form>;\n}\n\n/**\n * The root form container for message composition.\n *\n * This component provides a form wrapper that handles message submission when the form\n * is submitted (e.g., via Enter key or submit button). It automatically prevents the\n * default form submission and triggers the composer's send functionality.\n *\n * @example\n * ```tsx\n * <ComposerPrimitive.Root>\n *   <ComposerPrimitive.Input placeholder=\"Type your message...\" />\n *   <ComposerPrimitive.Send>Send</ComposerPrimitive.Send>\n * </ComposerPrimitive.Root>\n * ```\n */\nexport const ComposerPrimitiveRoot = forwardRef<\n  ComposerPrimitiveRoot.Element,\n  ComposerPrimitiveRoot.Props\n>(({ onSubmit, ...rest }, forwardedRef) => {\n  const send = useComposerSend();\n\n  const handleSubmit = (e: FormEvent) => {\n    e.preventDefault();\n\n    if (!send) return;\n    send();\n  };\n\n  return (\n    <Primitive.form\n      {...rest}\n      ref={forwardedRef}\n      onSubmit={composeEventHandlers(onSubmit, handleSubmit)}\n    />\n  );\n});\n\nComposerPrimitiveRoot.displayName = \"ComposerPrimitive.Root\";\n"], "mappings": ";;;AAEA,SAAS,4BAA4B;AACrC,SAAS,iBAAiB;AAC1B;AAAA,EAGE;AAAA,OAEK;AACP,SAAS,uBAAuB;AAwC5B;AAdG,IAAM,wBAAwB,WAGnC,CAAC,EAAE,UAAU,GAAG,KAAK,GAAG,iBAAiB;AACzC,QAAM,OAAO,gBAAgB;AAE7B,QAAM,eAAe,CAAC,MAAiB;AACrC,MAAE,eAAe;AAEjB,QAAI,CAAC,KAAM;AACX,SAAK;AAAA,EACP;AAEA,SACE;AAAA,IAAC,UAAU;AAAA,IAAV;AAAA,MACE,GAAG;AAAA,MACJ,KAAK;AAAA,MACL,UAAU,qBAAqB,UAAU,YAAY;AAAA;AAAA,EACvD;AAEJ,CAAC;AAED,sBAAsB,cAAc;", "names": []}