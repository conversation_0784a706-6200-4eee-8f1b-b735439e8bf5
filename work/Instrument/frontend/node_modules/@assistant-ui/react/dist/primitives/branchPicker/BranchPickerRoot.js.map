{"version": 3, "sources": ["../../../src/primitives/branchPicker/BranchPickerRoot.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { type ComponentRef, forwardRef, ComponentPropsWithoutRef } from \"react\";\nimport { If } from \"../message\";\n\nexport namespace BranchPickerPrimitiveRoot {\n  export type Element = ComponentRef<typeof Primitive.div>;\n  export type Props = ComponentPropsWithoutRef<typeof Primitive.div> & {\n    /**\n     * Whether to hide the branch picker when there's only one branch available.\n     * When true, the component will only render when multiple branches exist.\n     * @default false\n     */\n    hideWhenSingleBranch?: boolean | undefined;\n  };\n}\n\n/**\n * The root container for branch picker components.\n *\n * This component provides a container for branch navigation controls,\n * with optional conditional rendering based on the number of available branches.\n * It integrates with the message branching system to allow users to navigate\n * between different response variations.\n *\n * @example\n * ```tsx\n * <BranchPickerPrimitive.Root hideWhenSingleBranch={true}>\n *   <BranchPickerPrimitive.Previous />\n *   <BranchPickerPrimitive.Count />\n *   <BranchPickerPrimitive.Next />\n * </BranchPickerPrimitive.Root>\n * ```\n */\nexport const BranchPickerPrimitiveRoot = forwardRef<\n  BranchPickerPrimitiveRoot.Element,\n  BranchPickerPrimitiveRoot.Props\n>(({ hideWhenSingleBranch, ...rest }, ref) => {\n  return (\n    <If hasBranches={hideWhenSingleBranch ? true : undefined}>\n      <Primitive.div {...rest} ref={ref} />\n    </If>\n  );\n});\n\nBranchPickerPrimitiveRoot.displayName = \"BranchPickerPrimitive.Root\";\n"], "mappings": ";;;AAEA,SAAS,iBAAiB;AAC1B,SAA4B,kBAA4C;AACxE,SAAS,UAAU;AAqCb;AANC,IAAM,4BAA4B,WAGvC,CAAC,EAAE,sBAAsB,GAAG,KAAK,GAAG,QAAQ;AAC5C,SACE,oBAAC,MAAG,aAAa,uBAAuB,OAAO,QAC7C,8BAAC,UAAU,KAAV,EAAe,GAAG,MAAM,KAAU,GACrC;AAEJ,CAAC;AAED,0BAA0B,cAAc;", "names": []}