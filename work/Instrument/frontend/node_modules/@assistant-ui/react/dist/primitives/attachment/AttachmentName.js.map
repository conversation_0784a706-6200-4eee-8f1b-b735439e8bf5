{"version": 3, "sources": ["../../../src/primitives/attachment/AttachmentName.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { FC } from \"react\";\nimport { useAttachment } from \"../../context/react/AttachmentContext\";\n\nexport namespace AttachmentPrimitiveName {\n  export type Props = Record<string, never>;\n}\n\nexport const AttachmentPrimitiveName: FC<\n  AttachmentPrimitiveName.Props\n> = () => {\n  const name = useAttachment((a) => a.name);\n  return <>{name}</>;\n};\n\nAttachmentPrimitiveName.displayName = \"AttachmentPrimitive.Name\";\n"], "mappings": ";;;AAGA,SAAS,qBAAqB;AAUrB;AAJF,IAAM,0BAET,MAAM;AACR,QAAM,OAAO,cAAc,CAAC,MAAM,EAAE,IAAI;AACxC,SAAO,gCAAG,gBAAK;AACjB;AAEA,wBAAwB,cAAc;", "names": []}