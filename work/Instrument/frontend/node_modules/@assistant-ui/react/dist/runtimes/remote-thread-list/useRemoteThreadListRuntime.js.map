{"version": 3, "sources": ["../../../src/runtimes/remote-thread-list/useRemoteThreadListRuntime.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect, useMemo } from \"react\";\nimport { BaseAssistantRuntimeCore } from \"../core/BaseAssistantRuntimeCore\";\nimport { RemoteThreadListThreadListRuntimeCore } from \"./RemoteThreadListThreadListRuntimeCore\";\nimport { RemoteThreadListOptions } from \"./types\";\nimport { AssistantRuntimeImpl } from \"../../internal\";\nimport { AssistantRuntimeCore } from \"../core/AssistantRuntimeCore\";\n\nclass RemoteThreadListRuntimeCore\n  extends BaseAssistantRuntimeCore\n  implements AssistantRuntimeCore\n{\n  public readonly threads;\n\n  constructor(options: RemoteThreadListOptions) {\n    super();\n    this.threads = new RemoteThreadListThreadListRuntimeCore(\n      options,\n      this._contextProvider,\n    );\n  }\n\n  public get RenderComponent() {\n    return this.threads.__internal_RenderComponent;\n  }\n}\n\nexport const useRemoteThreadListRuntime = (\n  options: RemoteThreadListOptions,\n) => {\n  const [runtime] = useState(() => new RemoteThreadListRuntimeCore(options));\n  useEffect(() => {\n    runtime.threads.__internal_setOptions(options);\n    runtime.threads.__internal_load();\n  }, [runtime, options]);\n  return useMemo(() => new AssistantRuntimeImpl(runtime), [runtime]);\n};\n"], "mappings": ";;;AAEA,SAAS,UAAU,WAAW,eAAe;AAC7C,SAAS,gCAAgC;AACzC,SAAS,6CAA6C;AAEtD,SAAS,4BAA4B;AAGrC,IAAM,8BAAN,cACU,yBAEV;AAAA,EACkB;AAAA,EAEhB,YAAY,SAAkC;AAC5C,UAAM;AACN,SAAK,UAAU,IAAI;AAAA,MACjB;AAAA,MACA,KAAK;AAAA,IACP;AAAA,EACF;AAAA,EAEA,IAAW,kBAAkB;AAC3B,WAAO,KAAK,QAAQ;AAAA,EACtB;AACF;AAEO,IAAM,6BAA6B,CACxC,YACG;AACH,QAAM,CAAC,OAAO,IAAI,SAAS,MAAM,IAAI,4BAA4B,OAAO,CAAC;AACzE,YAAU,MAAM;AACd,YAAQ,QAAQ,sBAAsB,OAAO;AAC7C,YAAQ,QAAQ,gBAAgB;AAAA,EAClC,GAAG,CAAC,SAAS,OAAO,CAAC;AACrB,SAAO,QAAQ,MAAM,IAAI,qBAAqB,OAAO,GAAG,CAAC,OAAO,CAAC;AACnE;", "names": []}