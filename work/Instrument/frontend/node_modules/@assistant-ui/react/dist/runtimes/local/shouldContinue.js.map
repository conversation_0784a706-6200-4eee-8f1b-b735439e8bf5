{"version": 3, "sources": ["../../../src/runtimes/local/shouldContinue.tsx"], "sourcesContent": ["import type { ThreadAssistantMessage } from \"../../types\";\n\nexport const shouldContinue = (\n  result: ThreadAssistantMessage,\n  humanToolNames: string[] | undefined,\n) => {\n  // TODO legacy behavior -- make specifying human tool names required\n  if (humanToolNames === undefined) {\n    return (\n      result.status?.type === \"requires-action\" &&\n      result.status.reason === \"tool-calls\" &&\n      result.content.every((c) => c.type !== \"tool-call\" || !!c.result)\n    );\n  }\n\n  return (\n    result.status?.type === \"requires-action\" &&\n    result.status.reason === \"tool-calls\" &&\n    result.content.every(\n      (c) =>\n        c.type !== \"tool-call\" ||\n        !!c.result ||\n        !humanToolNames.includes(c.toolName),\n    )\n  );\n};\n"], "mappings": ";AAEO,IAAM,iBAAiB,CAC5B,QACA,mBACG;AAEH,MAAI,mBAAmB,QAAW;AAChC,WACE,OAAO,QAAQ,SAAS,qBACxB,OAAO,OAAO,WAAW,gBACzB,OAAO,QAAQ,MAAM,CAAC,MAAM,EAAE,SAAS,eAAe,CAAC,CAAC,EAAE,MAAM;AAAA,EAEpE;AAEA,SACE,OAAO,QAAQ,SAAS,qBACxB,OAAO,OAAO,WAAW,gBACzB,OAAO,QAAQ;AAAA,IACb,CAAC,MACC,EAAE,SAAS,eACX,CAAC,CAAC,EAAE,UACJ,CAAC,eAAe,SAAS,EAAE,QAAQ;AAAA,EACvC;AAEJ;", "names": []}