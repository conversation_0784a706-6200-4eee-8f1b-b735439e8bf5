import { ThreadListRuntimeCore } from "../core/ThreadListRuntimeCore";
import { RemoteThreadInitializeResponse, RemoteThreadListOptions } from "./types";
import { BaseSubscribable } from "./BaseSubscribable";
import { FC } from "react";
import { ModelContextProvider } from "../../model-context";
type RemoteThreadData = {
    readonly threadId: string;
    readonly remoteId?: undefined;
    readonly externalId?: undefined;
    readonly status: "new";
    readonly title: undefined;
} | {
    readonly threadId: string;
    readonly initializeTask: Promise<RemoteThreadInitializeResponse>;
    readonly remoteId?: undefined;
    readonly externalId?: undefined;
    readonly status: "regular" | "archived";
    readonly title?: string | undefined;
} | {
    readonly threadId: string;
    readonly initializeTask: Promise<RemoteThreadInitializeResponse>;
    readonly remoteId: string;
    readonly externalId: string | undefined;
    readonly status: "regular" | "archived";
    readonly title?: string | undefined;
};
export declare class RemoteThreadListThreadListRuntimeCore extends BaseSubscribable implements ThreadListRuntimeCore {
    private readonly contextProvider;
    private _options;
    private readonly _hookManager;
    private _loadThreadsPromise;
    private _mainThreadId;
    private readonly _state;
    getLoadThreadsPromise(): Promise<void>;
    constructor(options: RemoteThreadListOptions, contextProvider: ModelContextProvider);
    private useProvider;
    __internal_setOptions(options: RemoteThreadListOptions): void;
    __internal_load(): void;
    get isLoading(): boolean;
    get threadIds(): readonly string[];
    get archivedThreadIds(): readonly string[];
    get newThreadId(): string | undefined;
    get mainThreadId(): string;
    getMainThreadRuntimeCore(): Readonly<{
        getMessageById: (messageId: string) => {
            parentId: string | null;
            message: import("../..").ThreadMessage;
        } | undefined;
        getBranches: (messageId: string) => readonly string[];
        switchToBranch: (branchId: string) => void;
        append: (message: import("../..").AppendMessage) => void;
        startRun: (config: import("../core/ThreadRuntimeCore").StartRunConfig) => void;
        resumeRun: (config: import("../core/ThreadRuntimeCore").ResumeRunConfig) => void;
        cancelRun: () => void;
        addToolResult: (options: import("..").AddToolResultOptions) => void;
        speak: (messageId: string) => void;
        stopSpeaking: () => void;
        getSubmittedFeedback: (messageId: string) => import("../core/ThreadRuntimeCore").SubmittedFeedback | undefined;
        submitFeedback: (feedback: import("..").SubmitFeedbackOptions) => void;
        getModelContext: () => import("../..").AssistantConfig;
        composer: import("../core/ComposerRuntimeCore").ThreadComposerRuntimeCore;
        getEditComposer: (messageId: string) => import("../core/ComposerRuntimeCore").ComposerRuntimeCore | undefined;
        beginEdit: (messageId: string) => void;
        speech: import("../core/ThreadRuntimeCore").SpeechState | undefined;
        capabilities: Readonly<import("../core/ThreadRuntimeCore").RuntimeCapabilities>;
        isDisabled: boolean;
        isLoading: boolean;
        messages: readonly import("../..").ThreadMessage[];
        state: import("assistant-stream/utils").ReadonlyJSONValue;
        suggestions: readonly import("..").ThreadSuggestion[];
        extras: unknown;
        subscribe: (callback: () => void) => import("../..").Unsubscribe;
        import(repository: import("..").ExportedMessageRepository): void;
        export(): import("..").ExportedMessageRepository;
        reset(initialMessages?: readonly import("..").ThreadMessageLike[]): void;
        unstable_on(event: import("../core/ThreadRuntimeCore").ThreadRuntimeEventType, callback: () => void): import("../..").Unsubscribe;
    }>;
    getThreadRuntimeCore(threadIdOrRemoteId: string): Readonly<{
        getMessageById: (messageId: string) => {
            parentId: string | null;
            message: import("../..").ThreadMessage;
        } | undefined;
        getBranches: (messageId: string) => readonly string[];
        switchToBranch: (branchId: string) => void;
        append: (message: import("../..").AppendMessage) => void;
        startRun: (config: import("../core/ThreadRuntimeCore").StartRunConfig) => void;
        resumeRun: (config: import("../core/ThreadRuntimeCore").ResumeRunConfig) => void;
        cancelRun: () => void;
        addToolResult: (options: import("..").AddToolResultOptions) => void;
        speak: (messageId: string) => void;
        stopSpeaking: () => void;
        getSubmittedFeedback: (messageId: string) => import("../core/ThreadRuntimeCore").SubmittedFeedback | undefined;
        submitFeedback: (feedback: import("..").SubmitFeedbackOptions) => void;
        getModelContext: () => import("../..").AssistantConfig;
        composer: import("../core/ComposerRuntimeCore").ThreadComposerRuntimeCore;
        getEditComposer: (messageId: string) => import("../core/ComposerRuntimeCore").ComposerRuntimeCore | undefined;
        beginEdit: (messageId: string) => void;
        speech: import("../core/ThreadRuntimeCore").SpeechState | undefined;
        capabilities: Readonly<import("../core/ThreadRuntimeCore").RuntimeCapabilities>;
        isDisabled: boolean;
        isLoading: boolean;
        messages: readonly import("../..").ThreadMessage[];
        state: import("assistant-stream/utils").ReadonlyJSONValue;
        suggestions: readonly import("..").ThreadSuggestion[];
        extras: unknown;
        subscribe: (callback: () => void) => import("../..").Unsubscribe;
        import(repository: import("..").ExportedMessageRepository): void;
        export(): import("..").ExportedMessageRepository;
        reset(initialMessages?: readonly import("..").ThreadMessageLike[]): void;
        unstable_on(event: import("../core/ThreadRuntimeCore").ThreadRuntimeEventType, callback: () => void): import("../..").Unsubscribe;
    }>;
    getItemById(threadIdOrRemoteId: string): RemoteThreadData | undefined;
    switchToThread(threadIdOrRemoteId: string): Promise<void>;
    switchToNewThread(): Promise<void>;
    initialize: (threadId: string) => Promise<RemoteThreadInitializeResponse>;
    generateTitle: (threadId: string) => Promise<void>;
    rename(threadIdOrRemoteId: string, newTitle: string): Promise<void>;
    private _ensureThreadIsNotMain;
    archive(threadIdOrRemoteId: string): Promise<void>;
    unarchive(threadIdOrRemoteId: string): Promise<void>;
    delete(threadIdOrRemoteId: string): Promise<void>;
    detach(threadIdOrRemoteId: string): Promise<void>;
    private useBoundIds;
    __internal_RenderComponent: FC;
}
export {};
//# sourceMappingURL=RemoteThreadListThreadListRuntimeCore.d.ts.map