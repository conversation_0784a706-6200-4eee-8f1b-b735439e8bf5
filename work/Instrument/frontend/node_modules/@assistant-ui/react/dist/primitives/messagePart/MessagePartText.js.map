{"version": 3, "sources": ["../../../src/primitives/messagePart/MessagePartText.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport {\n  type ComponentRef,\n  forwardRef,\n  ComponentPropsWithoutRef,\n  ElementType,\n} from \"react\";\nimport { useMessagePartText } from \"./useMessagePartText\";\nimport { useSmooth } from \"../../utils/smooth/useSmooth\";\n\nexport namespace MessagePartPrimitiveText {\n  export type Element = ComponentRef<typeof Primitive.span>;\n  export type Props = Omit<\n    ComponentPropsWithoutRef<typeof Primitive.span>,\n    \"children\" | \"asChild\"\n  > & {\n    /**\n     * Whether to enable smooth text streaming animation.\n     * When enabled, text appears with a typing effect as it streams in.\n     * @default true\n     */\n    smooth?: boolean;\n    /**\n     * The HTML element or React component to render as.\n     * @default \"span\"\n     */\n    component?: ElementType;\n  };\n}\n\n/**\n * Renders the text content of a message part with optional smooth streaming.\n *\n * This component displays text content from the current message part context,\n * with support for smooth streaming animation that shows text appearing\n * character by character as it's generated.\n *\n * @example\n * ```tsx\n * <MessagePartPrimitive.Text\n *   smooth={true}\n *   component=\"p\"\n *   className=\"message-text\"\n * />\n * ```\n */\nexport const MessagePartPrimitiveText = forwardRef<\n  MessagePartPrimitiveText.Element,\n  MessagePartPrimitiveText.Props\n>(({ smooth = true, component: Component = \"span\", ...rest }, forwardedRef) => {\n  const { text, status } = useSmooth(useMessagePartText(), smooth);\n\n  return (\n    <Component data-status={status.type} {...rest} ref={forwardedRef}>\n      {text}\n    </Component>\n  );\n});\n\nMessagePartPrimitiveText.displayName = \"MessagePartPrimitive.Text\";\n"], "mappings": ";;;AAGA;AAAA,EAEE;AAAA,OAGK;AACP,SAAS,0BAA0B;AACnC,SAAS,iBAAiB;AA6CtB;AAPG,IAAM,2BAA2B,WAGtC,CAAC,EAAE,SAAS,MAAM,WAAW,YAAY,QAAQ,GAAG,KAAK,GAAG,iBAAiB;AAC7E,QAAM,EAAE,MAAM,OAAO,IAAI,UAAU,mBAAmB,GAAG,MAAM;AAE/D,SACE,oBAAC,aAAU,eAAa,OAAO,MAAO,GAAG,MAAM,KAAK,cACjD,gBACH;AAEJ,CAAC;AAED,yBAAyB,cAAc;", "names": []}