{"version": 3, "sources": ["../../../src/primitives/thread/ThreadEmpty.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { FC, PropsWithChildren } from \"react\";\nimport { useThread } from \"../../context\";\n\nexport namespace ThreadPrimitiveEmpty {\n  export type Props = PropsWithChildren;\n}\n\nexport const ThreadPrimitiveEmpty: FC<ThreadPrimitiveEmpty.Props> = ({\n  children,\n}) => {\n  const empty = useThread((u) => u.messages.length === 0);\n  return empty ? children : null;\n};\n\nThreadPrimitiveEmpty.displayName = \"ThreadPrimitive.Empty\";\n"], "mappings": ";;;AAGA,SAAS,iBAAiB;AAMnB,IAAM,uBAAuD,CAAC;AAAA,EACnE;AACF,MAAM;AACJ,QAAM,QAAQ,UAAU,CAAC,MAAM,EAAE,SAAS,WAAW,CAAC;AACtD,SAAO,QAAQ,WAAW;AAC5B;AAEA,qBAAqB,cAAc;", "names": []}