{"version": 3, "sources": ["../../../src/runtimes/external-store/ExternalStoreThreadListRuntimeCore.tsx"], "sourcesContent": ["import type { Unsubscribe } from \"../../types\";\nimport { ExternalStoreThreadRuntimeCore } from \"./ExternalStoreThreadRuntimeCore\";\nimport { ThreadListRuntimeCore } from \"../core/ThreadListRuntimeCore\";\nimport {\n  ExternalStoreThreadData,\n  ExternalStoreThreadListAdapter,\n} from \"./ExternalStoreAdapter\";\n\nexport type ExternalStoreThreadFactory = () => ExternalStoreThreadRuntimeCore;\n\nconst EMPTY_ARRAY = Object.freeze([]);\nconst DEFAULT_THREAD_ID = \"DEFAULT_THREAD_ID\";\nconst DEFAULT_THREADS = Object.freeze([DEFAULT_THREAD_ID]);\nconst DEFAULT_THREAD: ExternalStoreThreadData<\"regular\"> = Object.freeze({\n  threadId: DEFAULT_THREAD_ID,\n  status: \"regular\",\n});\nconst RESOLVED_PROMISE = Promise.resolve();\n\nexport class ExternalStoreThreadListRunt<PERSON>Core\n  implements ThreadListRuntimeCore\n{\n  private _mainThreadId: string = DEFAULT_THREAD_ID;\n  private _threads: readonly string[] = DEFAULT_THREADS;\n  private _archivedThreads: readonly string[] = EMPTY_ARRAY;\n\n  public get isLoading() {\n    return this.adapter.isLoading ?? false;\n  }\n\n  public get newThreadId() {\n    return undefined;\n  }\n\n  public get threadIds() {\n    return this._threads;\n  }\n\n  public get archivedThreadIds() {\n    return this._archivedThreads;\n  }\n\n  public getLoadThreadsPromise() {\n    return RESOLVED_PROMISE;\n  }\n\n  private _mainThread: ExternalStoreThreadRuntimeCore;\n\n  public get mainThreadId() {\n    return this._mainThreadId;\n  }\n\n  constructor(\n    private adapter: ExternalStoreThreadListAdapter = {},\n    private threadFactory: ExternalStoreThreadFactory,\n  ) {\n    this._mainThread = this.threadFactory();\n  }\n\n  public getMainThreadRuntimeCore() {\n    return this._mainThread;\n  }\n\n  public getThreadRuntimeCore(): never {\n    throw new Error(\"Method not implemented.\");\n  }\n\n  public getItemById(threadId: string) {\n    for (const thread of this.adapter.threads ?? []) {\n      if (thread.threadId === threadId) return thread;\n    }\n    for (const thread of this.adapter.archivedThreads ?? []) {\n      if (thread.threadId === threadId) return thread;\n    }\n    if (threadId === DEFAULT_THREAD_ID) return DEFAULT_THREAD;\n    return undefined;\n  }\n\n  public __internal_setAdapter(adapter: ExternalStoreThreadListAdapter) {\n    const previousAdapter = this.adapter;\n    this.adapter = adapter;\n\n    const newThreadId = adapter.threadId ?? DEFAULT_THREAD_ID;\n    const newThreads = adapter.threads ?? EMPTY_ARRAY;\n    const newArchivedThreads = adapter.archivedThreads ?? EMPTY_ARRAY;\n\n    const previousThreadId = previousAdapter.threadId ?? DEFAULT_THREAD_ID;\n    const previousThreads = previousAdapter.threads ?? EMPTY_ARRAY;\n    const previousArchivedThreads =\n      previousAdapter.archivedThreads ?? EMPTY_ARRAY;\n\n    if (\n      previousThreadId === newThreadId &&\n      previousThreads === newThreads &&\n      previousArchivedThreads === newArchivedThreads\n    ) {\n      return;\n    }\n\n    if (previousThreads !== newThreads) {\n      this._threads =\n        this.adapter.threads?.map((t) => t.threadId) ?? EMPTY_ARRAY;\n    }\n\n    if (previousArchivedThreads !== newArchivedThreads) {\n      this._archivedThreads =\n        this.adapter.archivedThreads?.map((t) => t.threadId) ?? EMPTY_ARRAY;\n    }\n\n    if (previousThreadId !== newThreadId) {\n      this._mainThreadId = newThreadId;\n      this._mainThread = this.threadFactory();\n    }\n\n    this._notifySubscribers();\n  }\n\n  public async switchToThread(threadId: string): Promise<void> {\n    if (this._mainThreadId === threadId) return;\n    const onSwitchToThread = this.adapter.onSwitchToThread;\n    if (!onSwitchToThread)\n      throw new Error(\n        \"External store adapter does not support switching to thread\",\n      );\n    onSwitchToThread(threadId);\n  }\n\n  public async switchToNewThread(): Promise<void> {\n    const onSwitchToNewThread = this.adapter.onSwitchToNewThread;\n    if (!onSwitchToNewThread)\n      throw new Error(\n        \"External store adapter does not support switching to new thread\",\n      );\n\n    onSwitchToNewThread();\n  }\n\n  public async rename(threadId: string, newTitle: string): Promise<void> {\n    const onRename = this.adapter.onRename;\n    if (!onRename)\n      throw new Error(\"External store adapter does not support renaming\");\n\n    onRename(threadId, newTitle);\n  }\n\n  public async detach(): Promise<void> {\n    // no-op\n  }\n\n  public async archive(threadId: string): Promise<void> {\n    const onArchive = this.adapter.onArchive;\n    if (!onArchive)\n      throw new Error(\"External store adapter does not support archiving\");\n\n    onArchive(threadId);\n  }\n\n  public async unarchive(threadId: string): Promise<void> {\n    const onUnarchive = this.adapter.onUnarchive;\n    if (!onUnarchive)\n      throw new Error(\"External store adapter does not support unarchiving\");\n\n    onUnarchive(threadId);\n  }\n\n  public async delete(threadId: string): Promise<void> {\n    const onDelete = this.adapter.onDelete;\n    if (!onDelete)\n      throw new Error(\"External store adapter does not support deleting\");\n\n    onDelete(threadId);\n  }\n\n  public initialize(): never {\n    throw new Error(\"Method not implemented.\");\n  }\n\n  public generateTitle(): never {\n    throw new Error(\"Method not implemented.\");\n  }\n\n  private _subscriptions = new Set<() => void>();\n\n  public subscribe(callback: () => void): Unsubscribe {\n    this._subscriptions.add(callback);\n    return () => this._subscriptions.delete(callback);\n  }\n\n  private _notifySubscribers() {\n    for (const callback of this._subscriptions) callback();\n  }\n}\n"], "mappings": ";AAUA,IAAM,cAAc,OAAO,OAAO,CAAC,CAAC;AACpC,IAAM,oBAAoB;AAC1B,IAAM,kBAAkB,OAAO,OAAO,CAAC,iBAAiB,CAAC;AACzD,IAAM,iBAAqD,OAAO,OAAO;AAAA,EACvE,UAAU;AAAA,EACV,QAAQ;AACV,CAAC;AACD,IAAM,mBAAmB,QAAQ,QAAQ;AAElC,IAAM,qCAAN,MAEP;AAAA,EA+BE,YACU,UAA0C,CAAC,GAC3C,eACR;AAFQ;AACA;AAER,SAAK,cAAc,KAAK,cAAc;AAAA,EACxC;AAAA,EAnCQ,gBAAwB;AAAA,EACxB,WAA8B;AAAA,EAC9B,mBAAsC;AAAA,EAE9C,IAAW,YAAY;AACrB,WAAO,KAAK,QAAQ,aAAa;AAAA,EACnC;AAAA,EAEA,IAAW,cAAc;AACvB,WAAO;AAAA,EACT;AAAA,EAEA,IAAW,YAAY;AACrB,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAW,oBAAoB;AAC7B,WAAO,KAAK;AAAA,EACd;AAAA,EAEO,wBAAwB;AAC7B,WAAO;AAAA,EACT;AAAA,EAEQ;AAAA,EAER,IAAW,eAAe;AACxB,WAAO,KAAK;AAAA,EACd;AAAA,EASO,2BAA2B;AAChC,WAAO,KAAK;AAAA,EACd;AAAA,EAEO,uBAA8B;AACnC,UAAM,IAAI,MAAM,yBAAyB;AAAA,EAC3C;AAAA,EAEO,YAAY,UAAkB;AACnC,eAAW,UAAU,KAAK,QAAQ,WAAW,CAAC,GAAG;AAC/C,UAAI,OAAO,aAAa,SAAU,QAAO;AAAA,IAC3C;AACA,eAAW,UAAU,KAAK,QAAQ,mBAAmB,CAAC,GAAG;AACvD,UAAI,OAAO,aAAa,SAAU,QAAO;AAAA,IAC3C;AACA,QAAI,aAAa,kBAAmB,QAAO;AAC3C,WAAO;AAAA,EACT;AAAA,EAEO,sBAAsB,SAAyC;AACpE,UAAM,kBAAkB,KAAK;AAC7B,SAAK,UAAU;AAEf,UAAM,cAAc,QAAQ,YAAY;AACxC,UAAM,aAAa,QAAQ,WAAW;AACtC,UAAM,qBAAqB,QAAQ,mBAAmB;AAEtD,UAAM,mBAAmB,gBAAgB,YAAY;AACrD,UAAM,kBAAkB,gBAAgB,WAAW;AACnD,UAAM,0BACJ,gBAAgB,mBAAmB;AAErC,QACE,qBAAqB,eACrB,oBAAoB,cACpB,4BAA4B,oBAC5B;AACA;AAAA,IACF;AAEA,QAAI,oBAAoB,YAAY;AAClC,WAAK,WACH,KAAK,QAAQ,SAAS,IAAI,CAAC,MAAM,EAAE,QAAQ,KAAK;AAAA,IACpD;AAEA,QAAI,4BAA4B,oBAAoB;AAClD,WAAK,mBACH,KAAK,QAAQ,iBAAiB,IAAI,CAAC,MAAM,EAAE,QAAQ,KAAK;AAAA,IAC5D;AAEA,QAAI,qBAAqB,aAAa;AACpC,WAAK,gBAAgB;AACrB,WAAK,cAAc,KAAK,cAAc;AAAA,IACxC;AAEA,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EAEA,MAAa,eAAe,UAAiC;AAC3D,QAAI,KAAK,kBAAkB,SAAU;AACrC,UAAM,mBAAmB,KAAK,QAAQ;AACtC,QAAI,CAAC;AACH,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AACF,qBAAiB,QAAQ;AAAA,EAC3B;AAAA,EAEA,MAAa,oBAAmC;AAC9C,UAAM,sBAAsB,KAAK,QAAQ;AACzC,QAAI,CAAC;AACH,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAEF,wBAAoB;AAAA,EACtB;AAAA,EAEA,MAAa,OAAO,UAAkB,UAAiC;AACrE,UAAM,WAAW,KAAK,QAAQ;AAC9B,QAAI,CAAC;AACH,YAAM,IAAI,MAAM,kDAAkD;AAEpE,aAAS,UAAU,QAAQ;AAAA,EAC7B;AAAA,EAEA,MAAa,SAAwB;AAAA,EAErC;AAAA,EAEA,MAAa,QAAQ,UAAiC;AACpD,UAAM,YAAY,KAAK,QAAQ;AAC/B,QAAI,CAAC;AACH,YAAM,IAAI,MAAM,mDAAmD;AAErE,cAAU,QAAQ;AAAA,EACpB;AAAA,EAEA,MAAa,UAAU,UAAiC;AACtD,UAAM,cAAc,KAAK,QAAQ;AACjC,QAAI,CAAC;AACH,YAAM,IAAI,MAAM,qDAAqD;AAEvE,gBAAY,QAAQ;AAAA,EACtB;AAAA,EAEA,MAAa,OAAO,UAAiC;AACnD,UAAM,WAAW,KAAK,QAAQ;AAC9B,QAAI,CAAC;AACH,YAAM,IAAI,MAAM,kDAAkD;AAEpE,aAAS,QAAQ;AAAA,EACnB;AAAA,EAEO,aAAoB;AACzB,UAAM,IAAI,MAAM,yBAAyB;AAAA,EAC3C;AAAA,EAEO,gBAAuB;AAC5B,UAAM,IAAI,MAAM,yBAAyB;AAAA,EAC3C;AAAA,EAEQ,iBAAiB,oBAAI,IAAgB;AAAA,EAEtC,UAAU,UAAmC;AAClD,SAAK,eAAe,IAAI,QAAQ;AAChC,WAAO,MAAM,KAAK,eAAe,OAAO,QAAQ;AAAA,EAClD;AAAA,EAEQ,qBAAqB;AAC3B,eAAW,YAAY,KAAK,eAAgB,UAAS;AAAA,EACvD;AACF;", "names": []}