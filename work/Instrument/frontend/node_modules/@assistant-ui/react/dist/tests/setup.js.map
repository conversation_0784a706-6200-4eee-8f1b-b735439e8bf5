{"version": 3, "sources": ["../../../../node_modules/.pnpm/vitest@3.2.4_@types+debug@4.1.12_@types+node@24.3.0_jiti@2.5.1_jsdom@26.1.0_lightningcss@1.30.1_tsx@4.20.4/node_modules/vitest/dist/chunks/vi.bdSIJ99Y.js", "../../../../node_modules/.pnpm/vitest@3.2.4_@types+debug@4.1.12_@types+node@24.3.0_jiti@2.5.1_jsdom@26.1.0_lightningcss@1.30.1_tsx@4.20.4/node_modules/vitest/dist/chunks/utils.XdZDrNZV.js", "../../../../node_modules/.pnpm/vitest@3.2.4_@types+debug@4.1.12_@types+node@24.3.0_jiti@2.5.1_jsdom@26.1.0_lightningcss@1.30.1_tsx@4.20.4/node_modules/vitest/dist/chunks/_commonjsHelpers.BFTU3MAI.js", "../../../../node_modules/.pnpm/vitest@3.2.4_@types+debug@4.1.12_@types+node@24.3.0_jiti@2.5.1_jsdom@26.1.0_lightningcss@1.30.1_tsx@4.20.4/node_modules/vitest/dist/chunks/date.Bq6ZW5rf.js", "../../../../node_modules/.pnpm/vitest@3.2.4_@types+debug@4.1.12_@types+node@24.3.0_jiti@2.5.1_jsdom@26.1.0_lightningcss@1.30.1_tsx@4.20.4/node_modules/vitest/dist/index.js", "../../src/tests/setup.ts"], "sourcesContent": ["import { equals, iterableEquality, subsetEquality, JestExtend, Jest<PERSON><PERSON>Expect, JestAsymmetricMatchers, GLOBAL_EXPECT, ASYMMETRIC_MATCHERS_OBJECT, getState, setState, addCustomEqualityTesters, customMatchers } from '@vitest/expect';\nimport { getCurrentTest } from '@vitest/runner';\nimport { getNames, getTestName } from '@vitest/runner/utils';\nimport * as chai$1 from 'chai';\nimport { g as getWorkerState, a as getCurrentEnvironment, i as isChildProcess, w as waitForImportsToResolve, r as resetModules } from './utils.XdZDrNZV.js';\nimport { getSafeTimers, assertTypes, createSimpleStackTrace } from '@vitest/utils';\nimport { g as getDefaultExportFromCjs, c as commonjsGlobal } from './_commonjsHelpers.BFTU3MAI.js';\nimport { stripSnapshotIndentation, addSerializer, SnapshotClient } from '@vitest/snapshot';\nimport '@vitest/utils/error';\nimport { fn, spyOn, mocks, isMockFunction } from '@vitest/spy';\nimport { parseSingleStack } from '@vitest/utils/source-map';\nimport { R as RealDate, r as resetDate, m as mockDate } from './date.Bq6ZW5rf.js';\n\n// these matchers are not supported because they don't make sense with poll\nconst unsupported = [\n\t\"matchSnapshot\",\n\t\"toMatchSnapshot\",\n\t\"toMatchInlineSnapshot\",\n\t\"toThrowErrorMatchingSnapshot\",\n\t\"toThrowErrorMatchingInlineSnapshot\",\n\t\"throws\",\n\t\"Throw\",\n\t\"throw\",\n\t\"toThrow\",\n\t\"toThrowError\"\n];\nfunction createExpectPoll(expect) {\n\treturn function poll(fn, options = {}) {\n\t\tconst state = getWorkerState();\n\t\tconst defaults = state.config.expect?.poll ?? {};\n\t\tconst { interval = defaults.interval ?? 50, timeout = defaults.timeout ?? 1e3, message } = options;\n\t\t// @ts-expect-error private poll access\n\t\tconst assertion = expect(null, message).withContext({ poll: true });\n\t\tfn = fn.bind(assertion);\n\t\tconst test = chai$1.util.flag(assertion, \"vitest-test\");\n\t\tif (!test) throw new Error(\"expect.poll() must be called inside a test\");\n\t\tconst proxy = new Proxy(assertion, { get(target, key, receiver) {\n\t\t\tconst assertionFunction = Reflect.get(target, key, receiver);\n\t\t\tif (typeof assertionFunction !== \"function\") return assertionFunction instanceof chai$1.Assertion ? proxy : assertionFunction;\n\t\t\tif (key === \"assert\") return assertionFunction;\n\t\t\tif (typeof key === \"string\" && unsupported.includes(key)) throw new SyntaxError(`expect.poll() is not supported in combination with .${key}(). Use vi.waitFor() if your assertion condition is unstable.`);\n\t\t\treturn function(...args) {\n\t\t\t\tconst STACK_TRACE_ERROR = new Error(\"STACK_TRACE_ERROR\");\n\t\t\t\tconst promise = () => new Promise((resolve, reject) => {\n\t\t\t\t\tlet intervalId;\n\t\t\t\t\tlet timeoutId;\n\t\t\t\t\tlet lastError;\n\t\t\t\t\tconst { setTimeout, clearTimeout } = getSafeTimers();\n\t\t\t\t\tconst check = async () => {\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tchai$1.util.flag(assertion, \"_name\", key);\n\t\t\t\t\t\t\tconst obj = await fn();\n\t\t\t\t\t\t\tchai$1.util.flag(assertion, \"object\", obj);\n\t\t\t\t\t\t\tresolve(await assertionFunction.call(assertion, ...args));\n\t\t\t\t\t\t\tclearTimeout(intervalId);\n\t\t\t\t\t\t\tclearTimeout(timeoutId);\n\t\t\t\t\t\t} catch (err) {\n\t\t\t\t\t\t\tlastError = err;\n\t\t\t\t\t\t\tif (!chai$1.util.flag(assertion, \"_isLastPollAttempt\")) intervalId = setTimeout(check, interval);\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\t\t\t\t\ttimeoutId = setTimeout(() => {\n\t\t\t\t\t\tclearTimeout(intervalId);\n\t\t\t\t\t\tchai$1.util.flag(assertion, \"_isLastPollAttempt\", true);\n\t\t\t\t\t\tconst rejectWithCause = (cause) => {\n\t\t\t\t\t\t\treject(copyStackTrace$1(new Error(\"Matcher did not succeed in time.\", { cause }), STACK_TRACE_ERROR));\n\t\t\t\t\t\t};\n\t\t\t\t\t\tcheck().then(() => rejectWithCause(lastError)).catch((e) => rejectWithCause(e));\n\t\t\t\t\t}, timeout);\n\t\t\t\t\tcheck();\n\t\t\t\t});\n\t\t\t\tlet awaited = false;\n\t\t\t\ttest.onFinished ??= [];\n\t\t\t\ttest.onFinished.push(() => {\n\t\t\t\t\tif (!awaited) {\n\t\t\t\t\t\tconst negated = chai$1.util.flag(assertion, \"negate\") ? \"not.\" : \"\";\n\t\t\t\t\t\tconst name = chai$1.util.flag(assertion, \"_poll.element\") ? \"element(locator)\" : \"poll(assertion)\";\n\t\t\t\t\t\tconst assertionString = `expect.${name}.${negated}${String(key)}()`;\n\t\t\t\t\t\tconst error = new Error(`${assertionString} was not awaited. This assertion is asynchronous and must be awaited; otherwise, it is not executed to avoid unhandled rejections:\\n\\nawait ${assertionString}\\n`);\n\t\t\t\t\t\tthrow copyStackTrace$1(error, STACK_TRACE_ERROR);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\tlet resultPromise;\n\t\t\t\t// only .then is enough to check awaited, but we type this as `Promise<void>` in global types\n\t\t\t\t// so let's follow it\n\t\t\t\treturn {\n\t\t\t\t\tthen(onFulfilled, onRejected) {\n\t\t\t\t\t\tawaited = true;\n\t\t\t\t\t\treturn (resultPromise ||= promise()).then(onFulfilled, onRejected);\n\t\t\t\t\t},\n\t\t\t\t\tcatch(onRejected) {\n\t\t\t\t\t\treturn (resultPromise ||= promise()).catch(onRejected);\n\t\t\t\t\t},\n\t\t\t\t\tfinally(onFinally) {\n\t\t\t\t\t\treturn (resultPromise ||= promise()).finally(onFinally);\n\t\t\t\t\t},\n\t\t\t\t\t[Symbol.toStringTag]: \"Promise\"\n\t\t\t\t};\n\t\t\t};\n\t\t} });\n\t\treturn proxy;\n\t};\n}\nfunction copyStackTrace$1(target, source) {\n\tif (source.stack !== void 0) target.stack = source.stack.replace(source.message, target.message);\n\treturn target;\n}\n\nfunction commonjsRequire(path) {\n\tthrow new Error('Could not dynamically require \"' + path + '\". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.');\n}\n\nvar chaiSubset$1 = {exports: {}};\n\nvar chaiSubset = chaiSubset$1.exports;\n\nvar hasRequiredChaiSubset;\n\nfunction requireChaiSubset () {\n\tif (hasRequiredChaiSubset) return chaiSubset$1.exports;\n\thasRequiredChaiSubset = 1;\n\t(function (module, exports) {\n\t\t(function() {\n\t\t\t(function(chaiSubset) {\n\t\t\t\tif (typeof commonjsRequire === 'function' && 'object' === 'object' && 'object' === 'object') {\n\t\t\t\t\treturn module.exports = chaiSubset;\n\t\t\t\t} else {\n\t\t\t\t\treturn chai.use(chaiSubset);\n\t\t\t\t}\n\t\t\t})(function(chai, utils) {\n\t\t\t\tvar Assertion = chai.Assertion;\n\t\t\t\tvar assertionPrototype = Assertion.prototype;\n\n\t\t\t\tAssertion.addMethod('containSubset', function (expected) {\n\t\t\t\t\tvar actual = utils.flag(this, 'object');\n\t\t\t\t\tvar showDiff = chai.config.showDiff;\n\n\t\t\t\t\tassertionPrototype.assert.call(this,\n\t\t\t\t\t\tcompare(expected, actual),\n\t\t\t\t\t\t'expected #{act} to contain subset #{exp}',\n\t\t\t\t\t\t'expected #{act} to not contain subset #{exp}',\n\t\t\t\t\t\texpected,\n\t\t\t\t\t\tactual,\n\t\t\t\t\t\tshowDiff\n\t\t\t\t\t);\n\t\t\t\t});\n\n\t\t\t\tchai.assert.containSubset = function(val, exp, msg) {\n\t\t\t\t\tnew chai.Assertion(val, msg).to.be.containSubset(exp);\n\t\t\t\t};\n\n\t\t\t\tfunction compare(expected, actual) {\n\t\t\t\t\tif (expected === actual) {\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t\tif (typeof(actual) !== typeof(expected)) {\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t\tif (typeof(expected) !== 'object' || expected === null) {\n\t\t\t\t\t\treturn expected === actual;\n\t\t\t\t\t}\n\t\t\t\t\tif (!!expected && !actual) {\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (Array.isArray(expected)) {\n\t\t\t\t\t\tif (typeof(actual.length) !== 'number') {\n\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tvar aa = Array.prototype.slice.call(actual);\n\t\t\t\t\t\treturn expected.every(function (exp) {\n\t\t\t\t\t\t\treturn aa.some(function (act) {\n\t\t\t\t\t\t\t\treturn compare(exp, act);\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\n\t\t\t\t\tif (expected instanceof Date) {\n\t\t\t\t\t\tif (actual instanceof Date) {\n\t\t\t\t\t\t\treturn expected.getTime() === actual.getTime();\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\treturn Object.keys(expected).every(function (key) {\n\t\t\t\t\t\tvar eo = expected[key];\n\t\t\t\t\t\tvar ao = actual[key];\n\t\t\t\t\t\tif (typeof(eo) === 'object' && eo !== null && ao !== null) {\n\t\t\t\t\t\t\treturn compare(eo, ao);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (typeof(eo) === 'function') {\n\t\t\t\t\t\t\treturn eo(ao);\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn ao === eo;\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\n\t\t}).call(chaiSubset); \n\t} (chaiSubset$1));\n\treturn chaiSubset$1.exports;\n}\n\nvar chaiSubsetExports = requireChaiSubset();\nvar Subset = /*@__PURE__*/getDefaultExportFromCjs(chaiSubsetExports);\n\nfunction createAssertionMessage(util, assertion, hasArgs) {\n\tconst not = util.flag(assertion, \"negate\") ? \"not.\" : \"\";\n\tconst name = `${util.flag(assertion, \"_name\")}(${\"expected\" })`;\n\tconst promiseName = util.flag(assertion, \"promise\");\n\tconst promise = promiseName ? `.${promiseName}` : \"\";\n\treturn `expect(actual)${promise}.${not}${name}`;\n}\nfunction recordAsyncExpect(_test, promise, assertion, error) {\n\tconst test = _test;\n\t// record promise for test, that resolves before test ends\n\tif (test && promise instanceof Promise) {\n\t\t// if promise is explicitly awaited, remove it from the list\n\t\tpromise = promise.finally(() => {\n\t\t\tif (!test.promises) return;\n\t\t\tconst index = test.promises.indexOf(promise);\n\t\t\tif (index !== -1) test.promises.splice(index, 1);\n\t\t});\n\t\t// record promise\n\t\tif (!test.promises) test.promises = [];\n\t\ttest.promises.push(promise);\n\t\tlet resolved = false;\n\t\ttest.onFinished ??= [];\n\t\ttest.onFinished.push(() => {\n\t\t\tif (!resolved) {\n\t\t\t\tconst processor = globalThis.__vitest_worker__?.onFilterStackTrace || ((s) => s || \"\");\n\t\t\t\tconst stack = processor(error.stack);\n\t\t\t\tconsole.warn([\n\t\t\t\t\t`Promise returned by \\`${assertion}\\` was not awaited. `,\n\t\t\t\t\t\"Vitest currently auto-awaits hanging assertions at the end of the test, but this will cause the test to fail in Vitest 3. \",\n\t\t\t\t\t\"Please remember to await the assertion.\\n\",\n\t\t\t\t\tstack\n\t\t\t\t].join(\"\"));\n\t\t\t}\n\t\t});\n\t\treturn {\n\t\t\tthen(onFulfilled, onRejected) {\n\t\t\t\tresolved = true;\n\t\t\t\treturn promise.then(onFulfilled, onRejected);\n\t\t\t},\n\t\t\tcatch(onRejected) {\n\t\t\t\treturn promise.catch(onRejected);\n\t\t\t},\n\t\t\tfinally(onFinally) {\n\t\t\t\treturn promise.finally(onFinally);\n\t\t\t},\n\t\t\t[Symbol.toStringTag]: \"Promise\"\n\t\t};\n\t}\n\treturn promise;\n}\n\nlet _client;\nfunction getSnapshotClient() {\n\tif (!_client) _client = new SnapshotClient({ isEqual: (received, expected) => {\n\t\treturn equals(received, expected, [iterableEquality, subsetEquality]);\n\t} });\n\treturn _client;\n}\nfunction getError(expected, promise) {\n\tif (typeof expected !== \"function\") {\n\t\tif (!promise) throw new Error(`expected must be a function, received ${typeof expected}`);\n\t\t// when \"promised\", it receives thrown error\n\t\treturn expected;\n\t}\n\ttry {\n\t\texpected();\n\t} catch (e) {\n\t\treturn e;\n\t}\n\tthrow new Error(\"snapshot function didn't throw\");\n}\nfunction getTestNames(test) {\n\treturn {\n\t\tfilepath: test.file.filepath,\n\t\tname: getNames(test).slice(1).join(\" > \"),\n\t\ttestId: test.id\n\t};\n}\nconst SnapshotPlugin = (chai, utils) => {\n\tfunction getTest(assertionName, obj) {\n\t\tconst test = utils.flag(obj, \"vitest-test\");\n\t\tif (!test) throw new Error(`'${assertionName}' cannot be used without test context`);\n\t\treturn test;\n\t}\n\tfor (const key of [\"matchSnapshot\", \"toMatchSnapshot\"]) utils.addMethod(chai.Assertion.prototype, key, function(properties, message) {\n\t\tutils.flag(this, \"_name\", key);\n\t\tconst isNot = utils.flag(this, \"negate\");\n\t\tif (isNot) throw new Error(`${key} cannot be used with \"not\"`);\n\t\tconst expected = utils.flag(this, \"object\");\n\t\tconst test = getTest(key, this);\n\t\tif (typeof properties === \"string\" && typeof message === \"undefined\") {\n\t\t\tmessage = properties;\n\t\t\tproperties = void 0;\n\t\t}\n\t\tconst errorMessage = utils.flag(this, \"message\");\n\t\tgetSnapshotClient().assert({\n\t\t\treceived: expected,\n\t\t\tmessage,\n\t\t\tisInline: false,\n\t\t\tproperties,\n\t\t\terrorMessage,\n\t\t\t...getTestNames(test)\n\t\t});\n\t});\n\tutils.addMethod(chai.Assertion.prototype, \"toMatchFileSnapshot\", function(file, message) {\n\t\tutils.flag(this, \"_name\", \"toMatchFileSnapshot\");\n\t\tconst isNot = utils.flag(this, \"negate\");\n\t\tif (isNot) throw new Error(\"toMatchFileSnapshot cannot be used with \\\"not\\\"\");\n\t\tconst error = new Error(\"resolves\");\n\t\tconst expected = utils.flag(this, \"object\");\n\t\tconst test = getTest(\"toMatchFileSnapshot\", this);\n\t\tconst errorMessage = utils.flag(this, \"message\");\n\t\tconst promise = getSnapshotClient().assertRaw({\n\t\t\treceived: expected,\n\t\t\tmessage,\n\t\t\tisInline: false,\n\t\t\trawSnapshot: { file },\n\t\t\terrorMessage,\n\t\t\t...getTestNames(test)\n\t\t});\n\t\treturn recordAsyncExpect(test, promise, createAssertionMessage(utils, this), error);\n\t});\n\tutils.addMethod(chai.Assertion.prototype, \"toMatchInlineSnapshot\", function __INLINE_SNAPSHOT__(properties, inlineSnapshot, message) {\n\t\tutils.flag(this, \"_name\", \"toMatchInlineSnapshot\");\n\t\tconst isNot = utils.flag(this, \"negate\");\n\t\tif (isNot) throw new Error(\"toMatchInlineSnapshot cannot be used with \\\"not\\\"\");\n\t\tconst test = getTest(\"toMatchInlineSnapshot\", this);\n\t\tconst isInsideEach = test.each || test.suite?.each;\n\t\tif (isInsideEach) throw new Error(\"InlineSnapshot cannot be used inside of test.each or describe.each\");\n\t\tconst expected = utils.flag(this, \"object\");\n\t\tconst error = utils.flag(this, \"error\");\n\t\tif (typeof properties === \"string\") {\n\t\t\tmessage = inlineSnapshot;\n\t\t\tinlineSnapshot = properties;\n\t\t\tproperties = void 0;\n\t\t}\n\t\tif (inlineSnapshot) inlineSnapshot = stripSnapshotIndentation(inlineSnapshot);\n\t\tconst errorMessage = utils.flag(this, \"message\");\n\t\tgetSnapshotClient().assert({\n\t\t\treceived: expected,\n\t\t\tmessage,\n\t\t\tisInline: true,\n\t\t\tproperties,\n\t\t\tinlineSnapshot,\n\t\t\terror,\n\t\t\terrorMessage,\n\t\t\t...getTestNames(test)\n\t\t});\n\t});\n\tutils.addMethod(chai.Assertion.prototype, \"toThrowErrorMatchingSnapshot\", function(message) {\n\t\tutils.flag(this, \"_name\", \"toThrowErrorMatchingSnapshot\");\n\t\tconst isNot = utils.flag(this, \"negate\");\n\t\tif (isNot) throw new Error(\"toThrowErrorMatchingSnapshot cannot be used with \\\"not\\\"\");\n\t\tconst expected = utils.flag(this, \"object\");\n\t\tconst test = getTest(\"toThrowErrorMatchingSnapshot\", this);\n\t\tconst promise = utils.flag(this, \"promise\");\n\t\tconst errorMessage = utils.flag(this, \"message\");\n\t\tgetSnapshotClient().assert({\n\t\t\treceived: getError(expected, promise),\n\t\t\tmessage,\n\t\t\terrorMessage,\n\t\t\t...getTestNames(test)\n\t\t});\n\t});\n\tutils.addMethod(chai.Assertion.prototype, \"toThrowErrorMatchingInlineSnapshot\", function __INLINE_SNAPSHOT__(inlineSnapshot, message) {\n\t\tconst isNot = utils.flag(this, \"negate\");\n\t\tif (isNot) throw new Error(\"toThrowErrorMatchingInlineSnapshot cannot be used with \\\"not\\\"\");\n\t\tconst test = getTest(\"toThrowErrorMatchingInlineSnapshot\", this);\n\t\tconst isInsideEach = test.each || test.suite?.each;\n\t\tif (isInsideEach) throw new Error(\"InlineSnapshot cannot be used inside of test.each or describe.each\");\n\t\tconst expected = utils.flag(this, \"object\");\n\t\tconst error = utils.flag(this, \"error\");\n\t\tconst promise = utils.flag(this, \"promise\");\n\t\tconst errorMessage = utils.flag(this, \"message\");\n\t\tif (inlineSnapshot) inlineSnapshot = stripSnapshotIndentation(inlineSnapshot);\n\t\tgetSnapshotClient().assert({\n\t\t\treceived: getError(expected, promise),\n\t\t\tmessage,\n\t\t\tinlineSnapshot,\n\t\t\tisInline: true,\n\t\t\terror,\n\t\t\terrorMessage,\n\t\t\t...getTestNames(test)\n\t\t});\n\t});\n\tutils.addMethod(chai.expect, \"addSnapshotSerializer\", addSerializer);\n};\n\nchai$1.use(JestExtend);\nchai$1.use(JestChaiExpect);\nchai$1.use(Subset);\nchai$1.use(SnapshotPlugin);\nchai$1.use(JestAsymmetricMatchers);\n\nfunction createExpect(test) {\n\tconst expect = (value, message) => {\n\t\tconst { assertionCalls } = getState(expect);\n\t\tsetState({ assertionCalls: assertionCalls + 1 }, expect);\n\t\tconst assert = chai$1.expect(value, message);\n\t\tconst _test = test || getCurrentTest();\n\t\tif (_test)\n // @ts-expect-error internal\n\t\treturn assert.withTest(_test);\n\t\telse return assert;\n\t};\n\tObject.assign(expect, chai$1.expect);\n\tObject.assign(expect, globalThis[ASYMMETRIC_MATCHERS_OBJECT]);\n\texpect.getState = () => getState(expect);\n\texpect.setState = (state) => setState(state, expect);\n\t// @ts-expect-error global is not typed\n\tconst globalState = getState(globalThis[GLOBAL_EXPECT]) || {};\n\tsetState({\n\t\t...globalState,\n\t\tassertionCalls: 0,\n\t\tisExpectingAssertions: false,\n\t\tisExpectingAssertionsError: null,\n\t\texpectedAssertionsNumber: null,\n\t\texpectedAssertionsNumberErrorGen: null,\n\t\tenvironment: getCurrentEnvironment(),\n\t\tget testPath() {\n\t\t\treturn getWorkerState().filepath;\n\t\t},\n\t\tcurrentTestName: test ? getTestName(test) : globalState.currentTestName\n\t}, expect);\n\t// @ts-expect-error untyped\n\texpect.extend = (matchers) => chai$1.expect.extend(expect, matchers);\n\texpect.addEqualityTesters = (customTesters) => addCustomEqualityTesters(customTesters);\n\texpect.soft = (...args) => {\n\t\t// @ts-expect-error private soft access\n\t\treturn expect(...args).withContext({ soft: true });\n\t};\n\texpect.poll = createExpectPoll(expect);\n\texpect.unreachable = (message) => {\n\t\tchai$1.assert.fail(`expected${message ? ` \"${message}\" ` : \" \"}not to be reached`);\n\t};\n\tfunction assertions(expected) {\n\t\tconst errorGen = () => new Error(`expected number of assertions to be ${expected}, but got ${expect.getState().assertionCalls}`);\n\t\tif (Error.captureStackTrace) Error.captureStackTrace(errorGen(), assertions);\n\t\texpect.setState({\n\t\t\texpectedAssertionsNumber: expected,\n\t\t\texpectedAssertionsNumberErrorGen: errorGen\n\t\t});\n\t}\n\tfunction hasAssertions() {\n\t\tconst error = new Error(\"expected any number of assertion, but got none\");\n\t\tif (Error.captureStackTrace) Error.captureStackTrace(error, hasAssertions);\n\t\texpect.setState({\n\t\t\tisExpectingAssertions: true,\n\t\t\tisExpectingAssertionsError: error\n\t\t});\n\t}\n\tchai$1.util.addMethod(expect, \"assertions\", assertions);\n\tchai$1.util.addMethod(expect, \"hasAssertions\", hasAssertions);\n\texpect.extend(customMatchers);\n\treturn expect;\n}\nconst globalExpect = createExpect();\nObject.defineProperty(globalThis, GLOBAL_EXPECT, {\n\tvalue: globalExpect,\n\twritable: true,\n\tconfigurable: true\n});\n\n/**\n* Gives access to injected context provided from the main thread.\n* This usually returns a value provided by `globalSetup` or an external library.\n*/\nfunction inject(key) {\n\tconst workerState = getWorkerState();\n\treturn workerState.providedContext[key];\n}\n\nvar fakeTimersSrc = {};\n\nvar global;\nvar hasRequiredGlobal;\n\nfunction requireGlobal () {\n\tif (hasRequiredGlobal) return global;\n\thasRequiredGlobal = 1;\n\n\t/**\n\t * A reference to the global object\n\t * @type {object} globalObject\n\t */\n\tvar globalObject;\n\n\t/* istanbul ignore else */\n\tif (typeof commonjsGlobal !== \"undefined\") {\n\t    // Node\n\t    globalObject = commonjsGlobal;\n\t} else if (typeof window !== \"undefined\") {\n\t    // Browser\n\t    globalObject = window;\n\t} else {\n\t    // WebWorker\n\t    globalObject = self;\n\t}\n\n\tglobal = globalObject;\n\treturn global;\n}\n\nvar throwsOnProto_1;\nvar hasRequiredThrowsOnProto;\n\nfunction requireThrowsOnProto () {\n\tif (hasRequiredThrowsOnProto) return throwsOnProto_1;\n\thasRequiredThrowsOnProto = 1;\n\n\t/**\n\t * Is true when the environment causes an error to be thrown for accessing the\n\t * __proto__ property.\n\t * This is necessary in order to support `node --disable-proto=throw`.\n\t *\n\t * See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/proto\n\t * @type {boolean}\n\t */\n\tlet throwsOnProto;\n\ttry {\n\t    const object = {};\n\t    // eslint-disable-next-line no-proto, no-unused-expressions\n\t    object.__proto__;\n\t    throwsOnProto = false;\n\t} catch (_) {\n\t    // This branch is covered when tests are run with `--disable-proto=throw`,\n\t    // however we can test both branches at the same time, so this is ignored\n\t    /* istanbul ignore next */\n\t    throwsOnProto = true;\n\t}\n\n\tthrowsOnProto_1 = throwsOnProto;\n\treturn throwsOnProto_1;\n}\n\nvar copyPrototypeMethods;\nvar hasRequiredCopyPrototypeMethods;\n\nfunction requireCopyPrototypeMethods () {\n\tif (hasRequiredCopyPrototypeMethods) return copyPrototypeMethods;\n\thasRequiredCopyPrototypeMethods = 1;\n\n\tvar call = Function.call;\n\tvar throwsOnProto = requireThrowsOnProto();\n\n\tvar disallowedProperties = [\n\t    // ignore size because it throws from Map\n\t    \"size\",\n\t    \"caller\",\n\t    \"callee\",\n\t    \"arguments\",\n\t];\n\n\t// This branch is covered when tests are run with `--disable-proto=throw`,\n\t// however we can test both branches at the same time, so this is ignored\n\t/* istanbul ignore next */\n\tif (throwsOnProto) {\n\t    disallowedProperties.push(\"__proto__\");\n\t}\n\n\tcopyPrototypeMethods = function copyPrototypeMethods(prototype) {\n\t    // eslint-disable-next-line @sinonjs/no-prototype-methods/no-prototype-methods\n\t    return Object.getOwnPropertyNames(prototype).reduce(function (\n\t        result,\n\t        name\n\t    ) {\n\t        if (disallowedProperties.includes(name)) {\n\t            return result;\n\t        }\n\n\t        if (typeof prototype[name] !== \"function\") {\n\t            return result;\n\t        }\n\n\t        result[name] = call.bind(prototype[name]);\n\n\t        return result;\n\t    },\n\t    Object.create(null));\n\t};\n\treturn copyPrototypeMethods;\n}\n\nvar array;\nvar hasRequiredArray;\n\nfunction requireArray () {\n\tif (hasRequiredArray) return array;\n\thasRequiredArray = 1;\n\n\tvar copyPrototype = requireCopyPrototypeMethods();\n\n\tarray = copyPrototype(Array.prototype);\n\treturn array;\n}\n\nvar calledInOrder_1;\nvar hasRequiredCalledInOrder;\n\nfunction requireCalledInOrder () {\n\tif (hasRequiredCalledInOrder) return calledInOrder_1;\n\thasRequiredCalledInOrder = 1;\n\n\tvar every = requireArray().every;\n\n\t/**\n\t * @private\n\t */\n\tfunction hasCallsLeft(callMap, spy) {\n\t    if (callMap[spy.id] === undefined) {\n\t        callMap[spy.id] = 0;\n\t    }\n\n\t    return callMap[spy.id] < spy.callCount;\n\t}\n\n\t/**\n\t * @private\n\t */\n\tfunction checkAdjacentCalls(callMap, spy, index, spies) {\n\t    var calledBeforeNext = true;\n\n\t    if (index !== spies.length - 1) {\n\t        calledBeforeNext = spy.calledBefore(spies[index + 1]);\n\t    }\n\n\t    if (hasCallsLeft(callMap, spy) && calledBeforeNext) {\n\t        callMap[spy.id] += 1;\n\t        return true;\n\t    }\n\n\t    return false;\n\t}\n\n\t/**\n\t * A Sinon proxy object (fake, spy, stub)\n\t * @typedef {object} SinonProxy\n\t * @property {Function} calledBefore - A method that determines if this proxy was called before another one\n\t * @property {string} id - Some id\n\t * @property {number} callCount - Number of times this proxy has been called\n\t */\n\n\t/**\n\t * Returns true when the spies have been called in the order they were supplied in\n\t * @param  {SinonProxy[] | SinonProxy} spies An array of proxies, or several proxies as arguments\n\t * @returns {boolean} true when spies are called in order, false otherwise\n\t */\n\tfunction calledInOrder(spies) {\n\t    var callMap = {};\n\t    // eslint-disable-next-line no-underscore-dangle\n\t    var _spies = arguments.length > 1 ? arguments : spies;\n\n\t    return every(_spies, checkAdjacentCalls.bind(null, callMap));\n\t}\n\n\tcalledInOrder_1 = calledInOrder;\n\treturn calledInOrder_1;\n}\n\nvar className_1;\nvar hasRequiredClassName;\n\nfunction requireClassName () {\n\tif (hasRequiredClassName) return className_1;\n\thasRequiredClassName = 1;\n\n\t/**\n\t * Returns a display name for a value from a constructor\n\t * @param  {object} value A value to examine\n\t * @returns {(string|null)} A string or null\n\t */\n\tfunction className(value) {\n\t    const name = value.constructor && value.constructor.name;\n\t    return name || null;\n\t}\n\n\tclassName_1 = className;\n\treturn className_1;\n}\n\nvar deprecated = {};\n\n/* eslint-disable no-console */\n\nvar hasRequiredDeprecated;\n\nfunction requireDeprecated () {\n\tif (hasRequiredDeprecated) return deprecated;\n\thasRequiredDeprecated = 1;\n\t(function (exports) {\n\n\t\t/**\n\t\t * Returns a function that will invoke the supplied function and print a\n\t\t * deprecation warning to the console each time it is called.\n\t\t * @param  {Function} func\n\t\t * @param  {string} msg\n\t\t * @returns {Function}\n\t\t */\n\t\texports.wrap = function (func, msg) {\n\t\t    var wrapped = function () {\n\t\t        exports.printWarning(msg);\n\t\t        return func.apply(this, arguments);\n\t\t    };\n\t\t    if (func.prototype) {\n\t\t        wrapped.prototype = func.prototype;\n\t\t    }\n\t\t    return wrapped;\n\t\t};\n\n\t\t/**\n\t\t * Returns a string which can be supplied to `wrap()` to notify the user that a\n\t\t * particular part of the sinon API has been deprecated.\n\t\t * @param  {string} packageName\n\t\t * @param  {string} funcName\n\t\t * @returns {string}\n\t\t */\n\t\texports.defaultMsg = function (packageName, funcName) {\n\t\t    return `${packageName}.${funcName} is deprecated and will be removed from the public API in a future version of ${packageName}.`;\n\t\t};\n\n\t\t/**\n\t\t * Prints a warning on the console, when it exists\n\t\t * @param  {string} msg\n\t\t * @returns {undefined}\n\t\t */\n\t\texports.printWarning = function (msg) {\n\t\t    /* istanbul ignore next */\n\t\t    if (typeof process === \"object\" && process.emitWarning) {\n\t\t        // Emit Warnings in Node\n\t\t        process.emitWarning(msg);\n\t\t    } else if (console.info) {\n\t\t        console.info(msg);\n\t\t    } else {\n\t\t        console.log(msg);\n\t\t    }\n\t\t}; \n\t} (deprecated));\n\treturn deprecated;\n}\n\nvar every;\nvar hasRequiredEvery;\n\nfunction requireEvery () {\n\tif (hasRequiredEvery) return every;\n\thasRequiredEvery = 1;\n\n\t/**\n\t * Returns true when fn returns true for all members of obj.\n\t * This is an every implementation that works for all iterables\n\t * @param  {object}   obj\n\t * @param  {Function} fn\n\t * @returns {boolean}\n\t */\n\tevery = function every(obj, fn) {\n\t    var pass = true;\n\n\t    try {\n\t        // eslint-disable-next-line @sinonjs/no-prototype-methods/no-prototype-methods\n\t        obj.forEach(function () {\n\t            if (!fn.apply(this, arguments)) {\n\t                // Throwing an error is the only way to break `forEach`\n\t                throw new Error();\n\t            }\n\t        });\n\t    } catch (e) {\n\t        pass = false;\n\t    }\n\n\t    return pass;\n\t};\n\treturn every;\n}\n\nvar functionName;\nvar hasRequiredFunctionName;\n\nfunction requireFunctionName () {\n\tif (hasRequiredFunctionName) return functionName;\n\thasRequiredFunctionName = 1;\n\n\t/**\n\t * Returns a display name for a function\n\t * @param  {Function} func\n\t * @returns {string}\n\t */\n\tfunctionName = function functionName(func) {\n\t    if (!func) {\n\t        return \"\";\n\t    }\n\n\t    try {\n\t        return (\n\t            func.displayName ||\n\t            func.name ||\n\t            // Use function decomposition as a last resort to get function\n\t            // name. Does not rely on function decomposition to work - if it\n\t            // doesn't debugging will be slightly less informative\n\t            // (i.e. toString will say 'spy' rather than 'myFunc').\n\t            (String(func).match(/function ([^\\s(]+)/) || [])[1]\n\t        );\n\t    } catch (e) {\n\t        // Stringify may fail and we might get an exception, as a last-last\n\t        // resort fall back to empty string.\n\t        return \"\";\n\t    }\n\t};\n\treturn functionName;\n}\n\nvar orderByFirstCall_1;\nvar hasRequiredOrderByFirstCall;\n\nfunction requireOrderByFirstCall () {\n\tif (hasRequiredOrderByFirstCall) return orderByFirstCall_1;\n\thasRequiredOrderByFirstCall = 1;\n\n\tvar sort = requireArray().sort;\n\tvar slice = requireArray().slice;\n\n\t/**\n\t * @private\n\t */\n\tfunction comparator(a, b) {\n\t    // uuid, won't ever be equal\n\t    var aCall = a.getCall(0);\n\t    var bCall = b.getCall(0);\n\t    var aId = (aCall && aCall.callId) || -1;\n\t    var bId = (bCall && bCall.callId) || -1;\n\n\t    return aId < bId ? -1 : 1;\n\t}\n\n\t/**\n\t * A Sinon proxy object (fake, spy, stub)\n\t * @typedef {object} SinonProxy\n\t * @property {Function} getCall - A method that can return the first call\n\t */\n\n\t/**\n\t * Sorts an array of SinonProxy instances (fake, spy, stub) by their first call\n\t * @param  {SinonProxy[] | SinonProxy} spies\n\t * @returns {SinonProxy[]}\n\t */\n\tfunction orderByFirstCall(spies) {\n\t    return sort(slice(spies), comparator);\n\t}\n\n\torderByFirstCall_1 = orderByFirstCall;\n\treturn orderByFirstCall_1;\n}\n\nvar _function;\nvar hasRequired_function;\n\nfunction require_function () {\n\tif (hasRequired_function) return _function;\n\thasRequired_function = 1;\n\n\tvar copyPrototype = requireCopyPrototypeMethods();\n\n\t_function = copyPrototype(Function.prototype);\n\treturn _function;\n}\n\nvar map;\nvar hasRequiredMap;\n\nfunction requireMap () {\n\tif (hasRequiredMap) return map;\n\thasRequiredMap = 1;\n\n\tvar copyPrototype = requireCopyPrototypeMethods();\n\n\tmap = copyPrototype(Map.prototype);\n\treturn map;\n}\n\nvar object;\nvar hasRequiredObject;\n\nfunction requireObject () {\n\tif (hasRequiredObject) return object;\n\thasRequiredObject = 1;\n\n\tvar copyPrototype = requireCopyPrototypeMethods();\n\n\tobject = copyPrototype(Object.prototype);\n\treturn object;\n}\n\nvar set;\nvar hasRequiredSet;\n\nfunction requireSet () {\n\tif (hasRequiredSet) return set;\n\thasRequiredSet = 1;\n\n\tvar copyPrototype = requireCopyPrototypeMethods();\n\n\tset = copyPrototype(Set.prototype);\n\treturn set;\n}\n\nvar string;\nvar hasRequiredString;\n\nfunction requireString () {\n\tif (hasRequiredString) return string;\n\thasRequiredString = 1;\n\n\tvar copyPrototype = requireCopyPrototypeMethods();\n\n\tstring = copyPrototype(String.prototype);\n\treturn string;\n}\n\nvar prototypes;\nvar hasRequiredPrototypes;\n\nfunction requirePrototypes () {\n\tif (hasRequiredPrototypes) return prototypes;\n\thasRequiredPrototypes = 1;\n\n\tprototypes = {\n\t    array: requireArray(),\n\t    function: require_function(),\n\t    map: requireMap(),\n\t    object: requireObject(),\n\t    set: requireSet(),\n\t    string: requireString(),\n\t};\n\treturn prototypes;\n}\n\nvar typeDetect$1 = {exports: {}};\n\nvar typeDetect = typeDetect$1.exports;\n\nvar hasRequiredTypeDetect;\n\nfunction requireTypeDetect () {\n\tif (hasRequiredTypeDetect) return typeDetect$1.exports;\n\thasRequiredTypeDetect = 1;\n\t(function (module, exports) {\n\t\t(function (global, factory) {\n\t\t\tmodule.exports = factory() ;\n\t\t}(typeDetect, (function () {\n\t\t/* !\n\t\t * type-detect\n\t\t * Copyright(c) 2013 jake luer <<EMAIL>>\n\t\t * MIT Licensed\n\t\t */\n\t\tvar promiseExists = typeof Promise === 'function';\n\n\t\t/* eslint-disable no-undef */\n\t\tvar globalObject = typeof self === 'object' ? self : commonjsGlobal; // eslint-disable-line id-blacklist\n\n\t\tvar symbolExists = typeof Symbol !== 'undefined';\n\t\tvar mapExists = typeof Map !== 'undefined';\n\t\tvar setExists = typeof Set !== 'undefined';\n\t\tvar weakMapExists = typeof WeakMap !== 'undefined';\n\t\tvar weakSetExists = typeof WeakSet !== 'undefined';\n\t\tvar dataViewExists = typeof DataView !== 'undefined';\n\t\tvar symbolIteratorExists = symbolExists && typeof Symbol.iterator !== 'undefined';\n\t\tvar symbolToStringTagExists = symbolExists && typeof Symbol.toStringTag !== 'undefined';\n\t\tvar setEntriesExists = setExists && typeof Set.prototype.entries === 'function';\n\t\tvar mapEntriesExists = mapExists && typeof Map.prototype.entries === 'function';\n\t\tvar setIteratorPrototype = setEntriesExists && Object.getPrototypeOf(new Set().entries());\n\t\tvar mapIteratorPrototype = mapEntriesExists && Object.getPrototypeOf(new Map().entries());\n\t\tvar arrayIteratorExists = symbolIteratorExists && typeof Array.prototype[Symbol.iterator] === 'function';\n\t\tvar arrayIteratorPrototype = arrayIteratorExists && Object.getPrototypeOf([][Symbol.iterator]());\n\t\tvar stringIteratorExists = symbolIteratorExists && typeof String.prototype[Symbol.iterator] === 'function';\n\t\tvar stringIteratorPrototype = stringIteratorExists && Object.getPrototypeOf(''[Symbol.iterator]());\n\t\tvar toStringLeftSliceLength = 8;\n\t\tvar toStringRightSliceLength = -1;\n\t\t/**\n\t\t * ### typeOf (obj)\n\t\t *\n\t\t * Uses `Object.prototype.toString` to determine the type of an object,\n\t\t * normalising behaviour across engine versions & well optimised.\n\t\t *\n\t\t * @param {Mixed} object\n\t\t * @return {String} object type\n\t\t * @api public\n\t\t */\n\t\tfunction typeDetect(obj) {\n\t\t  /* ! Speed optimisation\n\t\t   * Pre:\n\t\t   *   string literal     x 3,039,035 ops/sec ±1.62% (78 runs sampled)\n\t\t   *   boolean literal    x 1,424,138 ops/sec ±4.54% (75 runs sampled)\n\t\t   *   number literal     x 1,653,153 ops/sec ±1.91% (82 runs sampled)\n\t\t   *   undefined          x 9,978,660 ops/sec ±1.92% (75 runs sampled)\n\t\t   *   function           x 2,556,769 ops/sec ±1.73% (77 runs sampled)\n\t\t   * Post:\n\t\t   *   string literal     x 38,564,796 ops/sec ±1.15% (79 runs sampled)\n\t\t   *   boolean literal    x 31,148,940 ops/sec ±1.10% (79 runs sampled)\n\t\t   *   number literal     x 32,679,330 ops/sec ±1.90% (78 runs sampled)\n\t\t   *   undefined          x 32,363,368 ops/sec ±1.07% (82 runs sampled)\n\t\t   *   function           x 31,296,870 ops/sec ±0.96% (83 runs sampled)\n\t\t   */\n\t\t  var typeofObj = typeof obj;\n\t\t  if (typeofObj !== 'object') {\n\t\t    return typeofObj;\n\t\t  }\n\n\t\t  /* ! Speed optimisation\n\t\t   * Pre:\n\t\t   *   null               x 28,645,765 ops/sec ±1.17% (82 runs sampled)\n\t\t   * Post:\n\t\t   *   null               x 36,428,962 ops/sec ±1.37% (84 runs sampled)\n\t\t   */\n\t\t  if (obj === null) {\n\t\t    return 'null';\n\t\t  }\n\n\t\t  /* ! Spec Conformance\n\t\t   * Test: `Object.prototype.toString.call(window)``\n\t\t   *  - Node === \"[object global]\"\n\t\t   *  - Chrome === \"[object global]\"\n\t\t   *  - Firefox === \"[object Window]\"\n\t\t   *  - PhantomJS === \"[object Window]\"\n\t\t   *  - Safari === \"[object Window]\"\n\t\t   *  - IE 11 === \"[object Window]\"\n\t\t   *  - IE Edge === \"[object Window]\"\n\t\t   * Test: `Object.prototype.toString.call(this)``\n\t\t   *  - Chrome Worker === \"[object global]\"\n\t\t   *  - Firefox Worker === \"[object DedicatedWorkerGlobalScope]\"\n\t\t   *  - Safari Worker === \"[object DedicatedWorkerGlobalScope]\"\n\t\t   *  - IE 11 Worker === \"[object WorkerGlobalScope]\"\n\t\t   *  - IE Edge Worker === \"[object WorkerGlobalScope]\"\n\t\t   */\n\t\t  if (obj === globalObject) {\n\t\t    return 'global';\n\t\t  }\n\n\t\t  /* ! Speed optimisation\n\t\t   * Pre:\n\t\t   *   array literal      x 2,888,352 ops/sec ±0.67% (82 runs sampled)\n\t\t   * Post:\n\t\t   *   array literal      x 22,479,650 ops/sec ±0.96% (81 runs sampled)\n\t\t   */\n\t\t  if (\n\t\t    Array.isArray(obj) &&\n\t\t    (symbolToStringTagExists === false || !(Symbol.toStringTag in obj))\n\t\t  ) {\n\t\t    return 'Array';\n\t\t  }\n\n\t\t  // Not caching existence of `window` and related properties due to potential\n\t\t  // for `window` to be unset before tests in quasi-browser environments.\n\t\t  if (typeof window === 'object' && window !== null) {\n\t\t    /* ! Spec Conformance\n\t\t     * (https://html.spec.whatwg.org/multipage/browsers.html#location)\n\t\t     * WhatWG HTML$7.7.3 - The `Location` interface\n\t\t     * Test: `Object.prototype.toString.call(window.location)``\n\t\t     *  - IE <=11 === \"[object Object]\"\n\t\t     *  - IE Edge <=13 === \"[object Object]\"\n\t\t     */\n\t\t    if (typeof window.location === 'object' && obj === window.location) {\n\t\t      return 'Location';\n\t\t    }\n\n\t\t    /* ! Spec Conformance\n\t\t     * (https://html.spec.whatwg.org/#document)\n\t\t     * WhatWG HTML$3.1.1 - The `Document` object\n\t\t     * Note: Most browsers currently adher to the W3C DOM Level 2 spec\n\t\t     *       (https://www.w3.org/TR/DOM-Level-2-HTML/html.html#ID-26809268)\n\t\t     *       which suggests that browsers should use HTMLTableCellElement for\n\t\t     *       both TD and TH elements. WhatWG separates these.\n\t\t     *       WhatWG HTML states:\n\t\t     *         > For historical reasons, Window objects must also have a\n\t\t     *         > writable, configurable, non-enumerable property named\n\t\t     *         > HTMLDocument whose value is the Document interface object.\n\t\t     * Test: `Object.prototype.toString.call(document)``\n\t\t     *  - Chrome === \"[object HTMLDocument]\"\n\t\t     *  - Firefox === \"[object HTMLDocument]\"\n\t\t     *  - Safari === \"[object HTMLDocument]\"\n\t\t     *  - IE <=10 === \"[object Document]\"\n\t\t     *  - IE 11 === \"[object HTMLDocument]\"\n\t\t     *  - IE Edge <=13 === \"[object HTMLDocument]\"\n\t\t     */\n\t\t    if (typeof window.document === 'object' && obj === window.document) {\n\t\t      return 'Document';\n\t\t    }\n\n\t\t    if (typeof window.navigator === 'object') {\n\t\t      /* ! Spec Conformance\n\t\t       * (https://html.spec.whatwg.org/multipage/webappapis.html#mimetypearray)\n\t\t       * WhatWG HTML$******* - Plugins - Interface MimeTypeArray\n\t\t       * Test: `Object.prototype.toString.call(navigator.mimeTypes)``\n\t\t       *  - IE <=10 === \"[object MSMimeTypesCollection]\"\n\t\t       */\n\t\t      if (typeof window.navigator.mimeTypes === 'object' &&\n\t\t          obj === window.navigator.mimeTypes) {\n\t\t        return 'MimeTypeArray';\n\t\t      }\n\n\t\t      /* ! Spec Conformance\n\t\t       * (https://html.spec.whatwg.org/multipage/webappapis.html#pluginarray)\n\t\t       * WhatWG HTML$******* - Plugins - Interface PluginArray\n\t\t       * Test: `Object.prototype.toString.call(navigator.plugins)``\n\t\t       *  - IE <=10 === \"[object MSPluginsCollection]\"\n\t\t       */\n\t\t      if (typeof window.navigator.plugins === 'object' &&\n\t\t          obj === window.navigator.plugins) {\n\t\t        return 'PluginArray';\n\t\t      }\n\t\t    }\n\n\t\t    if ((typeof window.HTMLElement === 'function' ||\n\t\t        typeof window.HTMLElement === 'object') &&\n\t\t        obj instanceof window.HTMLElement) {\n\t\t      /* ! Spec Conformance\n\t\t      * (https://html.spec.whatwg.org/multipage/webappapis.html#pluginarray)\n\t\t      * WhatWG HTML$4.4.4 - The `blockquote` element - Interface `HTMLQuoteElement`\n\t\t      * Test: `Object.prototype.toString.call(document.createElement('blockquote'))``\n\t\t      *  - IE <=10 === \"[object HTMLBlockElement]\"\n\t\t      */\n\t\t      if (obj.tagName === 'BLOCKQUOTE') {\n\t\t        return 'HTMLQuoteElement';\n\t\t      }\n\n\t\t      /* ! Spec Conformance\n\t\t       * (https://html.spec.whatwg.org/#htmltabledatacellelement)\n\t\t       * WhatWG HTML$4.9.9 - The `td` element - Interface `HTMLTableDataCellElement`\n\t\t       * Note: Most browsers currently adher to the W3C DOM Level 2 spec\n\t\t       *       (https://www.w3.org/TR/DOM-Level-2-HTML/html.html#ID-82915075)\n\t\t       *       which suggests that browsers should use HTMLTableCellElement for\n\t\t       *       both TD and TH elements. WhatWG separates these.\n\t\t       * Test: Object.prototype.toString.call(document.createElement('td'))\n\t\t       *  - Chrome === \"[object HTMLTableCellElement]\"\n\t\t       *  - Firefox === \"[object HTMLTableCellElement]\"\n\t\t       *  - Safari === \"[object HTMLTableCellElement]\"\n\t\t       */\n\t\t      if (obj.tagName === 'TD') {\n\t\t        return 'HTMLTableDataCellElement';\n\t\t      }\n\n\t\t      /* ! Spec Conformance\n\t\t       * (https://html.spec.whatwg.org/#htmltableheadercellelement)\n\t\t       * WhatWG HTML$4.9.9 - The `td` element - Interface `HTMLTableHeaderCellElement`\n\t\t       * Note: Most browsers currently adher to the W3C DOM Level 2 spec\n\t\t       *       (https://www.w3.org/TR/DOM-Level-2-HTML/html.html#ID-82915075)\n\t\t       *       which suggests that browsers should use HTMLTableCellElement for\n\t\t       *       both TD and TH elements. WhatWG separates these.\n\t\t       * Test: Object.prototype.toString.call(document.createElement('th'))\n\t\t       *  - Chrome === \"[object HTMLTableCellElement]\"\n\t\t       *  - Firefox === \"[object HTMLTableCellElement]\"\n\t\t       *  - Safari === \"[object HTMLTableCellElement]\"\n\t\t       */\n\t\t      if (obj.tagName === 'TH') {\n\t\t        return 'HTMLTableHeaderCellElement';\n\t\t      }\n\t\t    }\n\t\t  }\n\n\t\t  /* ! Speed optimisation\n\t\t  * Pre:\n\t\t  *   Float64Array       x 625,644 ops/sec ±1.58% (80 runs sampled)\n\t\t  *   Float32Array       x 1,279,852 ops/sec ±2.91% (77 runs sampled)\n\t\t  *   Uint32Array        x 1,178,185 ops/sec ±1.95% (83 runs sampled)\n\t\t  *   Uint16Array        x 1,008,380 ops/sec ±2.25% (80 runs sampled)\n\t\t  *   Uint8Array         x 1,128,040 ops/sec ±2.11% (81 runs sampled)\n\t\t  *   Int32Array         x 1,170,119 ops/sec ±2.88% (80 runs sampled)\n\t\t  *   Int16Array         x 1,176,348 ops/sec ±5.79% (86 runs sampled)\n\t\t  *   Int8Array          x 1,058,707 ops/sec ±4.94% (77 runs sampled)\n\t\t  *   Uint8ClampedArray  x 1,110,633 ops/sec ±4.20% (80 runs sampled)\n\t\t  * Post:\n\t\t  *   Float64Array       x 7,105,671 ops/sec ±13.47% (64 runs sampled)\n\t\t  *   Float32Array       x 5,887,912 ops/sec ±1.46% (82 runs sampled)\n\t\t  *   Uint32Array        x 6,491,661 ops/sec ±1.76% (79 runs sampled)\n\t\t  *   Uint16Array        x 6,559,795 ops/sec ±1.67% (82 runs sampled)\n\t\t  *   Uint8Array         x 6,463,966 ops/sec ±1.43% (85 runs sampled)\n\t\t  *   Int32Array         x 5,641,841 ops/sec ±3.49% (81 runs sampled)\n\t\t  *   Int16Array         x 6,583,511 ops/sec ±1.98% (80 runs sampled)\n\t\t  *   Int8Array          x 6,606,078 ops/sec ±1.74% (81 runs sampled)\n\t\t  *   Uint8ClampedArray  x 6,602,224 ops/sec ±1.77% (83 runs sampled)\n\t\t  */\n\t\t  var stringTag = (symbolToStringTagExists && obj[Symbol.toStringTag]);\n\t\t  if (typeof stringTag === 'string') {\n\t\t    return stringTag;\n\t\t  }\n\n\t\t  var objPrototype = Object.getPrototypeOf(obj);\n\t\t  /* ! Speed optimisation\n\t\t  * Pre:\n\t\t  *   regex literal      x 1,772,385 ops/sec ±1.85% (77 runs sampled)\n\t\t  *   regex constructor  x 2,143,634 ops/sec ±2.46% (78 runs sampled)\n\t\t  * Post:\n\t\t  *   regex literal      x 3,928,009 ops/sec ±0.65% (78 runs sampled)\n\t\t  *   regex constructor  x 3,931,108 ops/sec ±0.58% (84 runs sampled)\n\t\t  */\n\t\t  if (objPrototype === RegExp.prototype) {\n\t\t    return 'RegExp';\n\t\t  }\n\n\t\t  /* ! Speed optimisation\n\t\t  * Pre:\n\t\t  *   date               x 2,130,074 ops/sec ±4.42% (68 runs sampled)\n\t\t  * Post:\n\t\t  *   date               x 3,953,779 ops/sec ±1.35% (77 runs sampled)\n\t\t  */\n\t\t  if (objPrototype === Date.prototype) {\n\t\t    return 'Date';\n\t\t  }\n\n\t\t  /* ! Spec Conformance\n\t\t   * (http://www.ecma-international.org/ecma-262/6.0/index.html#sec-promise.prototype-@@tostringtag)\n\t\t   * ES6$25.4.5.4 - Promise.prototype[@@toStringTag] should be \"Promise\":\n\t\t   * Test: `Object.prototype.toString.call(Promise.resolve())``\n\t\t   *  - Chrome <=47 === \"[object Object]\"\n\t\t   *  - Edge <=20 === \"[object Object]\"\n\t\t   *  - Firefox 29-Latest === \"[object Promise]\"\n\t\t   *  - Safari 7.1-Latest === \"[object Promise]\"\n\t\t   */\n\t\t  if (promiseExists && objPrototype === Promise.prototype) {\n\t\t    return 'Promise';\n\t\t  }\n\n\t\t  /* ! Speed optimisation\n\t\t  * Pre:\n\t\t  *   set                x 2,222,186 ops/sec ±1.31% (82 runs sampled)\n\t\t  * Post:\n\t\t  *   set                x 4,545,879 ops/sec ±1.13% (83 runs sampled)\n\t\t  */\n\t\t  if (setExists && objPrototype === Set.prototype) {\n\t\t    return 'Set';\n\t\t  }\n\n\t\t  /* ! Speed optimisation\n\t\t  * Pre:\n\t\t  *   map                x 2,396,842 ops/sec ±1.59% (81 runs sampled)\n\t\t  * Post:\n\t\t  *   map                x 4,183,945 ops/sec ±6.59% (82 runs sampled)\n\t\t  */\n\t\t  if (mapExists && objPrototype === Map.prototype) {\n\t\t    return 'Map';\n\t\t  }\n\n\t\t  /* ! Speed optimisation\n\t\t  * Pre:\n\t\t  *   weakset            x 1,323,220 ops/sec ±2.17% (76 runs sampled)\n\t\t  * Post:\n\t\t  *   weakset            x 4,237,510 ops/sec ±2.01% (77 runs sampled)\n\t\t  */\n\t\t  if (weakSetExists && objPrototype === WeakSet.prototype) {\n\t\t    return 'WeakSet';\n\t\t  }\n\n\t\t  /* ! Speed optimisation\n\t\t  * Pre:\n\t\t  *   weakmap            x 1,500,260 ops/sec ±2.02% (78 runs sampled)\n\t\t  * Post:\n\t\t  *   weakmap            x 3,881,384 ops/sec ±1.45% (82 runs sampled)\n\t\t  */\n\t\t  if (weakMapExists && objPrototype === WeakMap.prototype) {\n\t\t    return 'WeakMap';\n\t\t  }\n\n\t\t  /* ! Spec Conformance\n\t\t   * (http://www.ecma-international.org/ecma-262/6.0/index.html#sec-dataview.prototype-@@tostringtag)\n\t\t   * ES6$24.2.4.21 - DataView.prototype[@@toStringTag] should be \"DataView\":\n\t\t   * Test: `Object.prototype.toString.call(new DataView(new ArrayBuffer(1)))``\n\t\t   *  - Edge <=13 === \"[object Object]\"\n\t\t   */\n\t\t  if (dataViewExists && objPrototype === DataView.prototype) {\n\t\t    return 'DataView';\n\t\t  }\n\n\t\t  /* ! Spec Conformance\n\t\t   * (http://www.ecma-international.org/ecma-262/6.0/index.html#sec-%mapiteratorprototype%-@@tostringtag)\n\t\t   * ES6$23.1.5.2.2 - %MapIteratorPrototype%[@@toStringTag] should be \"Map Iterator\":\n\t\t   * Test: `Object.prototype.toString.call(new Map().entries())``\n\t\t   *  - Edge <=13 === \"[object Object]\"\n\t\t   */\n\t\t  if (mapExists && objPrototype === mapIteratorPrototype) {\n\t\t    return 'Map Iterator';\n\t\t  }\n\n\t\t  /* ! Spec Conformance\n\t\t   * (http://www.ecma-international.org/ecma-262/6.0/index.html#sec-%setiteratorprototype%-@@tostringtag)\n\t\t   * ES6$23.2.5.2.2 - %SetIteratorPrototype%[@@toStringTag] should be \"Set Iterator\":\n\t\t   * Test: `Object.prototype.toString.call(new Set().entries())``\n\t\t   *  - Edge <=13 === \"[object Object]\"\n\t\t   */\n\t\t  if (setExists && objPrototype === setIteratorPrototype) {\n\t\t    return 'Set Iterator';\n\t\t  }\n\n\t\t  /* ! Spec Conformance\n\t\t   * (http://www.ecma-international.org/ecma-262/6.0/index.html#sec-%arrayiteratorprototype%-@@tostringtag)\n\t\t   * ES6$22.1.5.2.2 - %ArrayIteratorPrototype%[@@toStringTag] should be \"Array Iterator\":\n\t\t   * Test: `Object.prototype.toString.call([][Symbol.iterator]())``\n\t\t   *  - Edge <=13 === \"[object Object]\"\n\t\t   */\n\t\t  if (arrayIteratorExists && objPrototype === arrayIteratorPrototype) {\n\t\t    return 'Array Iterator';\n\t\t  }\n\n\t\t  /* ! Spec Conformance\n\t\t   * (http://www.ecma-international.org/ecma-262/6.0/index.html#sec-%stringiteratorprototype%-@@tostringtag)\n\t\t   * ES6$21.1.5.2.2 - %StringIteratorPrototype%[@@toStringTag] should be \"String Iterator\":\n\t\t   * Test: `Object.prototype.toString.call(''[Symbol.iterator]())``\n\t\t   *  - Edge <=13 === \"[object Object]\"\n\t\t   */\n\t\t  if (stringIteratorExists && objPrototype === stringIteratorPrototype) {\n\t\t    return 'String Iterator';\n\t\t  }\n\n\t\t  /* ! Speed optimisation\n\t\t  * Pre:\n\t\t  *   object from null   x 2,424,320 ops/sec ±1.67% (76 runs sampled)\n\t\t  * Post:\n\t\t  *   object from null   x 5,838,000 ops/sec ±0.99% (84 runs sampled)\n\t\t  */\n\t\t  if (objPrototype === null) {\n\t\t    return 'Object';\n\t\t  }\n\n\t\t  return Object\n\t\t    .prototype\n\t\t    .toString\n\t\t    .call(obj)\n\t\t    .slice(toStringLeftSliceLength, toStringRightSliceLength);\n\t\t}\n\n\t\treturn typeDetect;\n\n\t\t}))); \n\t} (typeDetect$1));\n\treturn typeDetect$1.exports;\n}\n\nvar typeOf;\nvar hasRequiredTypeOf;\n\nfunction requireTypeOf () {\n\tif (hasRequiredTypeOf) return typeOf;\n\thasRequiredTypeOf = 1;\n\n\tvar type = requireTypeDetect();\n\n\t/**\n\t * Returns the lower-case result of running type from type-detect on the value\n\t * @param  {*} value\n\t * @returns {string}\n\t */\n\ttypeOf = function typeOf(value) {\n\t    return type(value).toLowerCase();\n\t};\n\treturn typeOf;\n}\n\nvar valueToString_1;\nvar hasRequiredValueToString;\n\nfunction requireValueToString () {\n\tif (hasRequiredValueToString) return valueToString_1;\n\thasRequiredValueToString = 1;\n\n\t/**\n\t * Returns a string representation of the value\n\t * @param  {*} value\n\t * @returns {string}\n\t */\n\tfunction valueToString(value) {\n\t    if (value && value.toString) {\n\t        // eslint-disable-next-line @sinonjs/no-prototype-methods/no-prototype-methods\n\t        return value.toString();\n\t    }\n\t    return String(value);\n\t}\n\n\tvalueToString_1 = valueToString;\n\treturn valueToString_1;\n}\n\nvar lib;\nvar hasRequiredLib;\n\nfunction requireLib () {\n\tif (hasRequiredLib) return lib;\n\thasRequiredLib = 1;\n\n\tlib = {\n\t    global: requireGlobal(),\n\t    calledInOrder: requireCalledInOrder(),\n\t    className: requireClassName(),\n\t    deprecated: requireDeprecated(),\n\t    every: requireEvery(),\n\t    functionName: requireFunctionName(),\n\t    orderByFirstCall: requireOrderByFirstCall(),\n\t    prototypes: requirePrototypes(),\n\t    typeOf: requireTypeOf(),\n\t    valueToString: requireValueToString(),\n\t};\n\treturn lib;\n}\n\nvar hasRequiredFakeTimersSrc;\n\nfunction requireFakeTimersSrc () {\n\tif (hasRequiredFakeTimersSrc) return fakeTimersSrc;\n\thasRequiredFakeTimersSrc = 1;\n\n\tconst globalObject = requireLib().global;\n\tlet timersModule, timersPromisesModule;\n\tif (typeof __vitest_required__ !== 'undefined') {\n\t    try {\n\t        timersModule = __vitest_required__.timers;\n\t    } catch (e) {\n\t        // ignored\n\t    }\n\t    try {\n\t        timersPromisesModule = __vitest_required__.timersPromises;\n\t    } catch (e) {\n\t        // ignored\n\t    }\n\t}\n\n\t/**\n\t * @typedef {object} IdleDeadline\n\t * @property {boolean} didTimeout - whether or not the callback was called before reaching the optional timeout\n\t * @property {function():number} timeRemaining - a floating-point value providing an estimate of the number of milliseconds remaining in the current idle period\n\t */\n\n\t/**\n\t * Queues a function to be called during a browser's idle periods\n\t * @callback RequestIdleCallback\n\t * @param {function(IdleDeadline)} callback\n\t * @param {{timeout: number}} options - an options object\n\t * @returns {number} the id\n\t */\n\n\t/**\n\t * @callback NextTick\n\t * @param {VoidVarArgsFunc} callback - the callback to run\n\t * @param {...*} args - optional arguments to call the callback with\n\t * @returns {void}\n\t */\n\n\t/**\n\t * @callback SetImmediate\n\t * @param {VoidVarArgsFunc} callback - the callback to run\n\t * @param {...*} args - optional arguments to call the callback with\n\t * @returns {NodeImmediate}\n\t */\n\n\t/**\n\t * @callback VoidVarArgsFunc\n\t * @param {...*} callback - the callback to run\n\t * @returns {void}\n\t */\n\n\t/**\n\t * @typedef RequestAnimationFrame\n\t * @property {function(number):void} requestAnimationFrame\n\t * @returns {number} - the id\n\t */\n\n\t/**\n\t * @typedef Performance\n\t * @property {function(): number} now\n\t */\n\n\t/* eslint-disable jsdoc/require-property-description */\n\t/**\n\t * @typedef {object} Clock\n\t * @property {number} now - the current time\n\t * @property {Date} Date - the Date constructor\n\t * @property {number} loopLimit - the maximum number of timers before assuming an infinite loop\n\t * @property {RequestIdleCallback} requestIdleCallback\n\t * @property {function(number):void} cancelIdleCallback\n\t * @property {setTimeout} setTimeout\n\t * @property {clearTimeout} clearTimeout\n\t * @property {NextTick} nextTick\n\t * @property {queueMicrotask} queueMicrotask\n\t * @property {setInterval} setInterval\n\t * @property {clearInterval} clearInterval\n\t * @property {SetImmediate} setImmediate\n\t * @property {function(NodeImmediate):void} clearImmediate\n\t * @property {function():number} countTimers\n\t * @property {RequestAnimationFrame} requestAnimationFrame\n\t * @property {function(number):void} cancelAnimationFrame\n\t * @property {function():void} runMicrotasks\n\t * @property {function(string | number): number} tick\n\t * @property {function(string | number): Promise<number>} tickAsync\n\t * @property {function(): number} next\n\t * @property {function(): Promise<number>} nextAsync\n\t * @property {function(): number} runAll\n\t * @property {function(): number} runToFrame\n\t * @property {function(): Promise<number>} runAllAsync\n\t * @property {function(): number} runToLast\n\t * @property {function(): Promise<number>} runToLastAsync\n\t * @property {function(): void} reset\n\t * @property {function(number | Date): void} setSystemTime\n\t * @property {function(number): void} jump\n\t * @property {Performance} performance\n\t * @property {function(number[]): number[]} hrtime - process.hrtime (legacy)\n\t * @property {function(): void} uninstall Uninstall the clock.\n\t * @property {Function[]} methods - the methods that are faked\n\t * @property {boolean} [shouldClearNativeTimers] inherited from config\n\t * @property {{methodName:string, original:any}[] | undefined} timersModuleMethods\n\t * @property {{methodName:string, original:any}[] | undefined} timersPromisesModuleMethods\n\t * @property {Map<function(): void, AbortSignal>} abortListenerMap\n\t */\n\t/* eslint-enable jsdoc/require-property-description */\n\n\t/**\n\t * Configuration object for the `install` method.\n\t * @typedef {object} Config\n\t * @property {number|Date} [now] a number (in milliseconds) or a Date object (default epoch)\n\t * @property {string[]} [toFake] names of the methods that should be faked.\n\t * @property {number} [loopLimit] the maximum number of timers that will be run when calling runAll()\n\t * @property {boolean} [shouldAdvanceTime] tells FakeTimers to increment mocked time automatically (default false)\n\t * @property {number} [advanceTimeDelta] increment mocked time every <<advanceTimeDelta>> ms (default: 20ms)\n\t * @property {boolean} [shouldClearNativeTimers] forwards clear timer calls to native functions if they are not fakes (default: false)\n\t * @property {boolean} [ignoreMissingTimers] default is false, meaning asking to fake timers that are not present will throw an error\n\t */\n\n\t/* eslint-disable jsdoc/require-property-description */\n\t/**\n\t * The internal structure to describe a scheduled fake timer\n\t * @typedef {object} Timer\n\t * @property {Function} func\n\t * @property {*[]} args\n\t * @property {number} delay\n\t * @property {number} callAt\n\t * @property {number} createdAt\n\t * @property {boolean} immediate\n\t * @property {number} id\n\t * @property {Error} [error]\n\t */\n\n\t/**\n\t * A Node timer\n\t * @typedef {object} NodeImmediate\n\t * @property {function(): boolean} hasRef\n\t * @property {function(): NodeImmediate} ref\n\t * @property {function(): NodeImmediate} unref\n\t */\n\t/* eslint-enable jsdoc/require-property-description */\n\n\t/* eslint-disable complexity */\n\n\t/**\n\t * Mocks available features in the specified global namespace.\n\t * @param {*} _global Namespace to mock (e.g. `window`)\n\t * @returns {FakeTimers}\n\t */\n\tfunction withGlobal(_global) {\n\t    const maxTimeout = Math.pow(2, 31) - 1; //see https://heycam.github.io/webidl/#abstract-opdef-converttoint\n\t    const idCounterStart = 1e12; // arbitrarily large number to avoid collisions with native timer IDs\n\t    const NOOP = function () {\n\t        return undefined;\n\t    };\n\t    const NOOP_ARRAY = function () {\n\t        return [];\n\t    };\n\t    const isPresent = {};\n\t    let timeoutResult,\n\t        addTimerReturnsObject = false;\n\n\t    if (_global.setTimeout) {\n\t        isPresent.setTimeout = true;\n\t        timeoutResult = _global.setTimeout(NOOP, 0);\n\t        addTimerReturnsObject = typeof timeoutResult === \"object\";\n\t    }\n\t    isPresent.clearTimeout = Boolean(_global.clearTimeout);\n\t    isPresent.setInterval = Boolean(_global.setInterval);\n\t    isPresent.clearInterval = Boolean(_global.clearInterval);\n\t    isPresent.hrtime =\n\t        _global.process && typeof _global.process.hrtime === \"function\";\n\t    isPresent.hrtimeBigint =\n\t        isPresent.hrtime && typeof _global.process.hrtime.bigint === \"function\";\n\t    isPresent.nextTick =\n\t        _global.process && typeof _global.process.nextTick === \"function\";\n\t    const utilPromisify = _global.process && _global.__vitest_required__ && _global.__vitest_required__.util.promisify;\n\t    isPresent.performance =\n\t        _global.performance && typeof _global.performance.now === \"function\";\n\t    const hasPerformancePrototype =\n\t        _global.Performance &&\n\t        (typeof _global.Performance).match(/^(function|object)$/);\n\t    const hasPerformanceConstructorPrototype =\n\t        _global.performance &&\n\t        _global.performance.constructor &&\n\t        _global.performance.constructor.prototype;\n\t    isPresent.queueMicrotask = _global.hasOwnProperty(\"queueMicrotask\");\n\t    isPresent.requestAnimationFrame =\n\t        _global.requestAnimationFrame &&\n\t        typeof _global.requestAnimationFrame === \"function\";\n\t    isPresent.cancelAnimationFrame =\n\t        _global.cancelAnimationFrame &&\n\t        typeof _global.cancelAnimationFrame === \"function\";\n\t    isPresent.requestIdleCallback =\n\t        _global.requestIdleCallback &&\n\t        typeof _global.requestIdleCallback === \"function\";\n\t    isPresent.cancelIdleCallbackPresent =\n\t        _global.cancelIdleCallback &&\n\t        typeof _global.cancelIdleCallback === \"function\";\n\t    isPresent.setImmediate =\n\t        _global.setImmediate && typeof _global.setImmediate === \"function\";\n\t    isPresent.clearImmediate =\n\t        _global.clearImmediate && typeof _global.clearImmediate === \"function\";\n\t    isPresent.Intl = _global.Intl && typeof _global.Intl === \"object\";\n\n\t    if (_global.clearTimeout) {\n\t        _global.clearTimeout(timeoutResult);\n\t    }\n\n\t    const NativeDate = _global.Date;\n\t    const NativeIntl = isPresent.Intl\n\t        ? Object.defineProperties(\n\t              Object.create(null),\n\t              Object.getOwnPropertyDescriptors(_global.Intl),\n\t          )\n\t        : undefined;\n\t    let uniqueTimerId = idCounterStart;\n\n\t    if (NativeDate === undefined) {\n\t        throw new Error(\n\t            \"The global scope doesn't have a `Date` object\" +\n\t                \" (see https://github.com/sinonjs/sinon/issues/1852#issuecomment-419622780)\",\n\t        );\n\t    }\n\t    isPresent.Date = true;\n\n\t    /**\n\t     * The PerformanceEntry object encapsulates a single performance metric\n\t     * that is part of the browser's performance timeline.\n\t     *\n\t     * This is an object returned by the `mark` and `measure` methods on the Performance prototype\n\t     */\n\t    class FakePerformanceEntry {\n\t        constructor(name, entryType, startTime, duration) {\n\t            this.name = name;\n\t            this.entryType = entryType;\n\t            this.startTime = startTime;\n\t            this.duration = duration;\n\t        }\n\n\t        toJSON() {\n\t            return JSON.stringify({ ...this });\n\t        }\n\t    }\n\n\t    /**\n\t     * @param {number} num\n\t     * @returns {boolean}\n\t     */\n\t    function isNumberFinite(num) {\n\t        if (Number.isFinite) {\n\t            return Number.isFinite(num);\n\t        }\n\n\t        return isFinite(num);\n\t    }\n\n\t    let isNearInfiniteLimit = false;\n\n\t    /**\n\t     * @param {Clock} clock\n\t     * @param {number} i\n\t     */\n\t    function checkIsNearInfiniteLimit(clock, i) {\n\t        if (clock.loopLimit && i === clock.loopLimit - 1) {\n\t            isNearInfiniteLimit = true;\n\t        }\n\t    }\n\n\t    /**\n\t     *\n\t     */\n\t    function resetIsNearInfiniteLimit() {\n\t        isNearInfiniteLimit = false;\n\t    }\n\n\t    /**\n\t     * Parse strings like \"01:10:00\" (meaning 1 hour, 10 minutes, 0 seconds) into\n\t     * number of milliseconds. This is used to support human-readable strings passed\n\t     * to clock.tick()\n\t     * @param {string} str\n\t     * @returns {number}\n\t     */\n\t    function parseTime(str) {\n\t        if (!str) {\n\t            return 0;\n\t        }\n\n\t        const strings = str.split(\":\");\n\t        const l = strings.length;\n\t        let i = l;\n\t        let ms = 0;\n\t        let parsed;\n\n\t        if (l > 3 || !/^(\\d\\d:){0,2}\\d\\d?$/.test(str)) {\n\t            throw new Error(\n\t                \"tick only understands numbers, 'm:s' and 'h:m:s'. Each part must be two digits\",\n\t            );\n\t        }\n\n\t        while (i--) {\n\t            parsed = parseInt(strings[i], 10);\n\n\t            if (parsed >= 60) {\n\t                throw new Error(`Invalid time ${str}`);\n\t            }\n\n\t            ms += parsed * Math.pow(60, l - i - 1);\n\t        }\n\n\t        return ms * 1000;\n\t    }\n\n\t    /**\n\t     * Get the decimal part of the millisecond value as nanoseconds\n\t     * @param {number} msFloat the number of milliseconds\n\t     * @returns {number} an integer number of nanoseconds in the range [0,1e6)\n\t     *\n\t     * Example: nanoRemainer(123.456789) -> 456789\n\t     */\n\t    function nanoRemainder(msFloat) {\n\t        const modulo = 1e6;\n\t        const remainder = (msFloat * 1e6) % modulo;\n\t        const positiveRemainder =\n\t            remainder < 0 ? remainder + modulo : remainder;\n\n\t        return Math.floor(positiveRemainder);\n\t    }\n\n\t    /**\n\t     * Used to grok the `now` parameter to createClock.\n\t     * @param {Date|number} epoch the system time\n\t     * @returns {number}\n\t     */\n\t    function getEpoch(epoch) {\n\t        if (!epoch) {\n\t            return 0;\n\t        }\n\t        if (typeof epoch.getTime === \"function\") {\n\t            return epoch.getTime();\n\t        }\n\t        if (typeof epoch === \"number\") {\n\t            return epoch;\n\t        }\n\t        throw new TypeError(\"now should be milliseconds since UNIX epoch\");\n\t    }\n\n\t    /**\n\t     * @param {number} from\n\t     * @param {number} to\n\t     * @param {Timer} timer\n\t     * @returns {boolean}\n\t     */\n\t    function inRange(from, to, timer) {\n\t        return timer && timer.callAt >= from && timer.callAt <= to;\n\t    }\n\n\t    /**\n\t     * @param {Clock} clock\n\t     * @param {Timer} job\n\t     */\n\t    function getInfiniteLoopError(clock, job) {\n\t        const infiniteLoopError = new Error(\n\t            `Aborting after running ${clock.loopLimit} timers, assuming an infinite loop!`,\n\t        );\n\n\t        if (!job.error) {\n\t            return infiniteLoopError;\n\t        }\n\n\t        // pattern never matched in Node\n\t        const computedTargetPattern = /target\\.*[<|(|[].*?[>|\\]|)]\\s*/;\n\t        let clockMethodPattern = new RegExp(\n\t            String(Object.keys(clock).join(\"|\")),\n\t        );\n\n\t        if (addTimerReturnsObject) {\n\t            // node.js environment\n\t            clockMethodPattern = new RegExp(\n\t                `\\\\s+at (Object\\\\.)?(?:${Object.keys(clock).join(\"|\")})\\\\s+`,\n\t            );\n\t        }\n\n\t        let matchedLineIndex = -1;\n\t        job.error.stack.split(\"\\n\").some(function (line, i) {\n\t            // If we've matched a computed target line (e.g. setTimeout) then we\n\t            // don't need to look any further. Return true to stop iterating.\n\t            const matchedComputedTarget = line.match(computedTargetPattern);\n\t            /* istanbul ignore if */\n\t            if (matchedComputedTarget) {\n\t                matchedLineIndex = i;\n\t                return true;\n\t            }\n\n\t            // If we've matched a clock method line, then there may still be\n\t            // others further down the trace. Return false to keep iterating.\n\t            const matchedClockMethod = line.match(clockMethodPattern);\n\t            if (matchedClockMethod) {\n\t                matchedLineIndex = i;\n\t                return false;\n\t            }\n\n\t            // If we haven't matched anything on this line, but we matched\n\t            // previously and set the matched line index, then we can stop.\n\t            // If we haven't matched previously, then we should keep iterating.\n\t            return matchedLineIndex >= 0;\n\t        });\n\n\t        const stack = `${infiniteLoopError}\\n${job.type || \"Microtask\"} - ${\n\t            job.func.name || \"anonymous\"\n\t        }\\n${job.error.stack\n\t            .split(\"\\n\")\n\t            .slice(matchedLineIndex + 1)\n\t            .join(\"\\n\")}`;\n\n\t        try {\n\t            Object.defineProperty(infiniteLoopError, \"stack\", {\n\t                value: stack,\n\t            });\n\t        } catch (e) {\n\t            // noop\n\t        }\n\n\t        return infiniteLoopError;\n\t    }\n\n\t    //eslint-disable-next-line jsdoc/require-jsdoc\n\t    function createDate() {\n\t        class ClockDate extends NativeDate {\n\t            /**\n\t             * @param {number} year\n\t             * @param {number} month\n\t             * @param {number} date\n\t             * @param {number} hour\n\t             * @param {number} minute\n\t             * @param {number} second\n\t             * @param {number} ms\n\t             * @returns void\n\t             */\n\t            // eslint-disable-next-line no-unused-vars\n\t            constructor(year, month, date, hour, minute, second, ms) {\n\t                // Defensive and verbose to avoid potential harm in passing\n\t                // explicit undefined when user does not pass argument\n\t                if (arguments.length === 0) {\n\t                    super(ClockDate.clock.now);\n\t                } else {\n\t                    super(...arguments);\n\t                }\n\n\t                // ensures identity checks using the constructor prop still works\n\t                // this should have no other functional effect\n\t                Object.defineProperty(this, \"constructor\", {\n\t                    value: NativeDate,\n\t                    enumerable: false,\n\t                });\n\t            }\n\n\t            static [Symbol.hasInstance](instance) {\n\t                return instance instanceof NativeDate;\n\t            }\n\t        }\n\n\t        ClockDate.isFake = true;\n\n\t        if (NativeDate.now) {\n\t            ClockDate.now = function now() {\n\t                return ClockDate.clock.now;\n\t            };\n\t        }\n\n\t        if (NativeDate.toSource) {\n\t            ClockDate.toSource = function toSource() {\n\t                return NativeDate.toSource();\n\t            };\n\t        }\n\n\t        ClockDate.toString = function toString() {\n\t            return NativeDate.toString();\n\t        };\n\n\t        // noinspection UnnecessaryLocalVariableJS\n\t        /**\n\t         * A normal Class constructor cannot be called without `new`, but Date can, so we need\n\t         * to wrap it in a Proxy in order to ensure this functionality of Date is kept intact\n\t         * @type {ClockDate}\n\t         */\n\t        const ClockDateProxy = new Proxy(ClockDate, {\n\t            // handler for [[Call]] invocations (i.e. not using `new`)\n\t            apply() {\n\t                // the Date constructor called as a function, ref Ecma-262 Edition 5.1, section 15.9.2.\n\t                // This remains so in the 10th edition of 2019 as well.\n\t                if (this instanceof ClockDate) {\n\t                    throw new TypeError(\n\t                        \"A Proxy should only capture `new` calls with the `construct` handler. This is not supposed to be possible, so check the logic.\",\n\t                    );\n\t                }\n\n\t                return new NativeDate(ClockDate.clock.now).toString();\n\t            },\n\t        });\n\n\t        return ClockDateProxy;\n\t    }\n\n\t    /**\n\t     * Mirror Intl by default on our fake implementation\n\t     *\n\t     * Most of the properties are the original native ones,\n\t     * but we need to take control of those that have a\n\t     * dependency on the current clock.\n\t     * @returns {object} the partly fake Intl implementation\n\t     */\n\t    function createIntl() {\n\t        const ClockIntl = {};\n\t        /*\n\t         * All properties of Intl are non-enumerable, so we need\n\t         * to do a bit of work to get them out.\n\t         */\n\t        Object.getOwnPropertyNames(NativeIntl).forEach(\n\t            (property) => (ClockIntl[property] = NativeIntl[property]),\n\t        );\n\n\t        ClockIntl.DateTimeFormat = function (...args) {\n\t            const realFormatter = new NativeIntl.DateTimeFormat(...args);\n\t            const formatter = {};\n\n\t            [\"formatRange\", \"formatRangeToParts\", \"resolvedOptions\"].forEach(\n\t                (method) => {\n\t                    formatter[method] =\n\t                        realFormatter[method].bind(realFormatter);\n\t                },\n\t            );\n\n\t            [\"format\", \"formatToParts\"].forEach((method) => {\n\t                formatter[method] = function (date) {\n\t                    return realFormatter[method](date || ClockIntl.clock.now);\n\t                };\n\t            });\n\n\t            return formatter;\n\t        };\n\n\t        ClockIntl.DateTimeFormat.prototype = Object.create(\n\t            NativeIntl.DateTimeFormat.prototype,\n\t        );\n\n\t        ClockIntl.DateTimeFormat.supportedLocalesOf =\n\t            NativeIntl.DateTimeFormat.supportedLocalesOf;\n\n\t        return ClockIntl;\n\t    }\n\n\t    //eslint-disable-next-line jsdoc/require-jsdoc\n\t    function enqueueJob(clock, job) {\n\t        // enqueues a microtick-deferred task - ecma262/#sec-enqueuejob\n\t        if (!clock.jobs) {\n\t            clock.jobs = [];\n\t        }\n\t        clock.jobs.push(job);\n\t    }\n\n\t    //eslint-disable-next-line jsdoc/require-jsdoc\n\t    function runJobs(clock) {\n\t        // runs all microtick-deferred tasks - ecma262/#sec-runjobs\n\t        if (!clock.jobs) {\n\t            return;\n\t        }\n\t        for (let i = 0; i < clock.jobs.length; i++) {\n\t            const job = clock.jobs[i];\n\t            job.func.apply(null, job.args);\n\n\t            checkIsNearInfiniteLimit(clock, i);\n\t            if (clock.loopLimit && i > clock.loopLimit) {\n\t                throw getInfiniteLoopError(clock, job);\n\t            }\n\t        }\n\t        resetIsNearInfiniteLimit();\n\t        clock.jobs = [];\n\t    }\n\n\t    /**\n\t     * @param {Clock} clock\n\t     * @param {Timer} timer\n\t     * @returns {number} id of the created timer\n\t     */\n\t    function addTimer(clock, timer) {\n\t        if (timer.func === undefined) {\n\t            throw new Error(\"Callback must be provided to timer calls\");\n\t        }\n\n\t        if (addTimerReturnsObject) {\n\t            // Node.js environment\n\t            if (typeof timer.func !== \"function\") {\n\t                throw new TypeError(\n\t                    `[ERR_INVALID_CALLBACK]: Callback must be a function. Received ${\n\t                        timer.func\n\t                    } of type ${typeof timer.func}`,\n\t                );\n\t            }\n\t        }\n\n\t        if (isNearInfiniteLimit) {\n\t            timer.error = new Error();\n\t        }\n\n\t        timer.type = timer.immediate ? \"Immediate\" : \"Timeout\";\n\n\t        if (timer.hasOwnProperty(\"delay\")) {\n\t            if (typeof timer.delay !== \"number\") {\n\t                timer.delay = parseInt(timer.delay, 10);\n\t            }\n\n\t            if (!isNumberFinite(timer.delay)) {\n\t                timer.delay = 0;\n\t            }\n\t            timer.delay = timer.delay > maxTimeout ? 1 : timer.delay;\n\t            timer.delay = Math.max(0, timer.delay);\n\t        }\n\n\t        if (timer.hasOwnProperty(\"interval\")) {\n\t            timer.type = \"Interval\";\n\t            timer.interval = timer.interval > maxTimeout ? 1 : timer.interval;\n\t        }\n\n\t        if (timer.hasOwnProperty(\"animation\")) {\n\t            timer.type = \"AnimationFrame\";\n\t            timer.animation = true;\n\t        }\n\n\t        if (timer.hasOwnProperty(\"idleCallback\")) {\n\t            timer.type = \"IdleCallback\";\n\t            timer.idleCallback = true;\n\t        }\n\n\t        if (!clock.timers) {\n\t            clock.timers = {};\n\t        }\n\n\t        timer.id = uniqueTimerId++;\n\t        timer.createdAt = clock.now;\n\t        timer.callAt =\n\t            clock.now + (parseInt(timer.delay) || (clock.duringTick ? 1 : 0));\n\n\t        clock.timers[timer.id] = timer;\n\n\t        if (addTimerReturnsObject) {\n\t            const res = {\n\t                refed: true,\n\t                ref: function () {\n\t                    this.refed = true;\n\t                    return res;\n\t                },\n\t                unref: function () {\n\t                    this.refed = false;\n\t                    return res;\n\t                },\n\t                hasRef: function () {\n\t                    return this.refed;\n\t                },\n\t                refresh: function () {\n\t                    timer.callAt =\n\t                        clock.now +\n\t                        (parseInt(timer.delay) || (clock.duringTick ? 1 : 0));\n\n\t                    // it _might_ have been removed, but if not the assignment is perfectly fine\n\t                    clock.timers[timer.id] = timer;\n\n\t                    return res;\n\t                },\n\t                [Symbol.toPrimitive]: function () {\n\t                    return timer.id;\n\t                },\n\t            };\n\t            return res;\n\t        }\n\n\t        return timer.id;\n\t    }\n\n\t    /* eslint consistent-return: \"off\" */\n\t    /**\n\t     * Timer comparitor\n\t     * @param {Timer} a\n\t     * @param {Timer} b\n\t     * @returns {number}\n\t     */\n\t    function compareTimers(a, b) {\n\t        // Sort first by absolute timing\n\t        if (a.callAt < b.callAt) {\n\t            return -1;\n\t        }\n\t        if (a.callAt > b.callAt) {\n\t            return 1;\n\t        }\n\n\t        // Sort next by immediate, immediate timers take precedence\n\t        if (a.immediate && !b.immediate) {\n\t            return -1;\n\t        }\n\t        if (!a.immediate && b.immediate) {\n\t            return 1;\n\t        }\n\n\t        // Sort next by creation time, earlier-created timers take precedence\n\t        if (a.createdAt < b.createdAt) {\n\t            return -1;\n\t        }\n\t        if (a.createdAt > b.createdAt) {\n\t            return 1;\n\t        }\n\n\t        // Sort next by id, lower-id timers take precedence\n\t        if (a.id < b.id) {\n\t            return -1;\n\t        }\n\t        if (a.id > b.id) {\n\t            return 1;\n\t        }\n\n\t        // As timer ids are unique, no fallback `0` is necessary\n\t    }\n\n\t    /**\n\t     * @param {Clock} clock\n\t     * @param {number} from\n\t     * @param {number} to\n\t     * @returns {Timer}\n\t     */\n\t    function firstTimerInRange(clock, from, to) {\n\t        const timers = clock.timers;\n\t        let timer = null;\n\t        let id, isInRange;\n\n\t        for (id in timers) {\n\t            if (timers.hasOwnProperty(id)) {\n\t                isInRange = inRange(from, to, timers[id]);\n\n\t                if (\n\t                    isInRange &&\n\t                    (!timer || compareTimers(timer, timers[id]) === 1)\n\t                ) {\n\t                    timer = timers[id];\n\t                }\n\t            }\n\t        }\n\n\t        return timer;\n\t    }\n\n\t    /**\n\t     * @param {Clock} clock\n\t     * @returns {Timer}\n\t     */\n\t    function firstTimer(clock) {\n\t        const timers = clock.timers;\n\t        let timer = null;\n\t        let id;\n\n\t        for (id in timers) {\n\t            if (timers.hasOwnProperty(id)) {\n\t                if (!timer || compareTimers(timer, timers[id]) === 1) {\n\t                    timer = timers[id];\n\t                }\n\t            }\n\t        }\n\n\t        return timer;\n\t    }\n\n\t    /**\n\t     * @param {Clock} clock\n\t     * @returns {Timer}\n\t     */\n\t    function lastTimer(clock) {\n\t        const timers = clock.timers;\n\t        let timer = null;\n\t        let id;\n\n\t        for (id in timers) {\n\t            if (timers.hasOwnProperty(id)) {\n\t                if (!timer || compareTimers(timer, timers[id]) === -1) {\n\t                    timer = timers[id];\n\t                }\n\t            }\n\t        }\n\n\t        return timer;\n\t    }\n\n\t    /**\n\t     * @param {Clock} clock\n\t     * @param {Timer} timer\n\t     */\n\t    function callTimer(clock, timer) {\n\t        if (typeof timer.interval === \"number\") {\n\t            clock.timers[timer.id].callAt += timer.interval;\n\t        } else {\n\t            delete clock.timers[timer.id];\n\t        }\n\n\t        if (typeof timer.func === \"function\") {\n\t            timer.func.apply(null, timer.args);\n\t        } else {\n\t            /* eslint no-eval: \"off\" */\n\t            const eval2 = eval;\n\t            (function () {\n\t                eval2(timer.func);\n\t            })();\n\t        }\n\t    }\n\n\t    /**\n\t     * Gets clear handler name for a given timer type\n\t     * @param {string} ttype\n\t     */\n\t    function getClearHandler(ttype) {\n\t        if (ttype === \"IdleCallback\" || ttype === \"AnimationFrame\") {\n\t            return `cancel${ttype}`;\n\t        }\n\t        return `clear${ttype}`;\n\t    }\n\n\t    /**\n\t     * Gets schedule handler name for a given timer type\n\t     * @param {string} ttype\n\t     */\n\t    function getScheduleHandler(ttype) {\n\t        if (ttype === \"IdleCallback\" || ttype === \"AnimationFrame\") {\n\t            return `request${ttype}`;\n\t        }\n\t        return `set${ttype}`;\n\t    }\n\n\t    /**\n\t     * Creates an anonymous function to warn only once\n\t     */\n\t    function createWarnOnce() {\n\t        let calls = 0;\n\t        return function (msg) {\n\t            // eslint-disable-next-line\n\t            !calls++ && console.warn(msg);\n\t        };\n\t    }\n\t    const warnOnce = createWarnOnce();\n\n\t    /**\n\t     * @param {Clock} clock\n\t     * @param {number} timerId\n\t     * @param {string} ttype\n\t     */\n\t    function clearTimer(clock, timerId, ttype) {\n\t        if (!timerId) {\n\t            // null appears to be allowed in most browsers, and appears to be\n\t            // relied upon by some libraries, like Bootstrap carousel\n\t            return;\n\t        }\n\n\t        if (!clock.timers) {\n\t            clock.timers = {};\n\t        }\n\n\t        // in Node, the ID is stored as the primitive value for `Timeout` objects\n\t        // for `Immediate` objects, no ID exists, so it gets coerced to NaN\n\t        const id = Number(timerId);\n\n\t        if (Number.isNaN(id) || id < idCounterStart) {\n\t            const handlerName = getClearHandler(ttype);\n\n\t            if (clock.shouldClearNativeTimers === true) {\n\t                const nativeHandler = clock[`_${handlerName}`];\n\t                return typeof nativeHandler === \"function\"\n\t                    ? nativeHandler(timerId)\n\t                    : undefined;\n\t            }\n\t            warnOnce(\n\t                `FakeTimers: ${handlerName} was invoked to clear a native timer instead of one created by this library.` +\n\t                    \"\\nTo automatically clean-up native timers, use `shouldClearNativeTimers`.\",\n\t            );\n\t        }\n\n\t        if (clock.timers.hasOwnProperty(id)) {\n\t            // check that the ID matches a timer of the correct type\n\t            const timer = clock.timers[id];\n\t            if (\n\t                timer.type === ttype ||\n\t                (timer.type === \"Timeout\" && ttype === \"Interval\") ||\n\t                (timer.type === \"Interval\" && ttype === \"Timeout\")\n\t            ) {\n\t                delete clock.timers[id];\n\t            } else {\n\t                const clear = getClearHandler(ttype);\n\t                const schedule = getScheduleHandler(timer.type);\n\t                throw new Error(\n\t                    `Cannot clear timer: timer created with ${schedule}() but cleared with ${clear}()`,\n\t                );\n\t            }\n\t        }\n\t    }\n\n\t    /**\n\t     * @param {Clock} clock\n\t     * @param {Config} config\n\t     * @returns {Timer[]}\n\t     */\n\t    function uninstall(clock, config) {\n\t        let method, i, l;\n\t        const installedHrTime = \"_hrtime\";\n\t        const installedNextTick = \"_nextTick\";\n\n\t        for (i = 0, l = clock.methods.length; i < l; i++) {\n\t            method = clock.methods[i];\n\t            if (method === \"hrtime\" && _global.process) {\n\t                _global.process.hrtime = clock[installedHrTime];\n\t            } else if (method === \"nextTick\" && _global.process) {\n\t                _global.process.nextTick = clock[installedNextTick];\n\t            } else if (method === \"performance\") {\n\t                const originalPerfDescriptor = Object.getOwnPropertyDescriptor(\n\t                    clock,\n\t                    `_${method}`,\n\t                );\n\t                if (\n\t                    originalPerfDescriptor &&\n\t                    originalPerfDescriptor.get &&\n\t                    !originalPerfDescriptor.set\n\t                ) {\n\t                    Object.defineProperty(\n\t                        _global,\n\t                        method,\n\t                        originalPerfDescriptor,\n\t                    );\n\t                } else if (originalPerfDescriptor.configurable) {\n\t                    _global[method] = clock[`_${method}`];\n\t                }\n\t            } else {\n\t                if (_global[method] && _global[method].hadOwnProperty) {\n\t                    _global[method] = clock[`_${method}`];\n\t                } else {\n\t                    try {\n\t                        delete _global[method];\n\t                    } catch (ignore) {\n\t                        /* eslint no-empty: \"off\" */\n\t                    }\n\t                }\n\t            }\n\t            if (clock.timersModuleMethods !== undefined) {\n\t                for (let j = 0; j < clock.timersModuleMethods.length; j++) {\n\t                    const entry = clock.timersModuleMethods[j];\n\t                    timersModule[entry.methodName] = entry.original;\n\t                }\n\t            }\n\t            if (clock.timersPromisesModuleMethods !== undefined) {\n\t                for (\n\t                    let j = 0;\n\t                    j < clock.timersPromisesModuleMethods.length;\n\t                    j++\n\t                ) {\n\t                    const entry = clock.timersPromisesModuleMethods[j];\n\t                    timersPromisesModule[entry.methodName] = entry.original;\n\t                }\n\t            }\n\t        }\n\n\t        if (config.shouldAdvanceTime === true) {\n\t            _global.clearInterval(clock.attachedInterval);\n\t        }\n\n\t        // Prevent multiple executions which will completely remove these props\n\t        clock.methods = [];\n\n\t        for (const [listener, signal] of clock.abortListenerMap.entries()) {\n\t            signal.removeEventListener(\"abort\", listener);\n\t            clock.abortListenerMap.delete(listener);\n\t        }\n\n\t        // return pending timers, to enable checking what timers remained on uninstall\n\t        if (!clock.timers) {\n\t            return [];\n\t        }\n\t        return Object.keys(clock.timers).map(function mapper(key) {\n\t            return clock.timers[key];\n\t        });\n\t    }\n\n\t    /**\n\t     * @param {object} target the target containing the method to replace\n\t     * @param {string} method the keyname of the method on the target\n\t     * @param {Clock} clock\n\t     */\n\t    function hijackMethod(target, method, clock) {\n\t        clock[method].hadOwnProperty = Object.prototype.hasOwnProperty.call(\n\t            target,\n\t            method,\n\t        );\n\t        clock[`_${method}`] = target[method];\n\n\t        if (method === \"Date\") {\n\t            target[method] = clock[method];\n\t        } else if (method === \"Intl\") {\n\t            target[method] = clock[method];\n\t        } else if (method === \"performance\") {\n\t            const originalPerfDescriptor = Object.getOwnPropertyDescriptor(\n\t                target,\n\t                method,\n\t            );\n\t            // JSDOM has a read only performance field so we have to save/copy it differently\n\t            if (\n\t                originalPerfDescriptor &&\n\t                originalPerfDescriptor.get &&\n\t                !originalPerfDescriptor.set\n\t            ) {\n\t                Object.defineProperty(\n\t                    clock,\n\t                    `_${method}`,\n\t                    originalPerfDescriptor,\n\t                );\n\n\t                const perfDescriptor = Object.getOwnPropertyDescriptor(\n\t                    clock,\n\t                    method,\n\t                );\n\t                Object.defineProperty(target, method, perfDescriptor);\n\t            } else {\n\t                target[method] = clock[method];\n\t            }\n\t        } else {\n\t            target[method] = function () {\n\t                return clock[method].apply(clock, arguments);\n\t            };\n\n\t            Object.defineProperties(\n\t                target[method],\n\t                Object.getOwnPropertyDescriptors(clock[method]),\n\t            );\n\t        }\n\n\t        target[method].clock = clock;\n\t    }\n\n\t    /**\n\t     * @param {Clock} clock\n\t     * @param {number} advanceTimeDelta\n\t     */\n\t    function doIntervalTick(clock, advanceTimeDelta) {\n\t        clock.tick(advanceTimeDelta);\n\t    }\n\n\t    /**\n\t     * @typedef {object} Timers\n\t     * @property {setTimeout} setTimeout\n\t     * @property {clearTimeout} clearTimeout\n\t     * @property {setInterval} setInterval\n\t     * @property {clearInterval} clearInterval\n\t     * @property {Date} Date\n\t     * @property {Intl} Intl\n\t     * @property {SetImmediate=} setImmediate\n\t     * @property {function(NodeImmediate): void=} clearImmediate\n\t     * @property {function(number[]):number[]=} hrtime\n\t     * @property {NextTick=} nextTick\n\t     * @property {Performance=} performance\n\t     * @property {RequestAnimationFrame=} requestAnimationFrame\n\t     * @property {boolean=} queueMicrotask\n\t     * @property {function(number): void=} cancelAnimationFrame\n\t     * @property {RequestIdleCallback=} requestIdleCallback\n\t     * @property {function(number): void=} cancelIdleCallback\n\t     */\n\n\t    /** @type {Timers} */\n\t    const timers = {\n\t        setTimeout: _global.setTimeout,\n\t        clearTimeout: _global.clearTimeout,\n\t        setInterval: _global.setInterval,\n\t        clearInterval: _global.clearInterval,\n\t        Date: _global.Date,\n\t    };\n\n\t    if (isPresent.setImmediate) {\n\t        timers.setImmediate = _global.setImmediate;\n\t    }\n\n\t    if (isPresent.clearImmediate) {\n\t        timers.clearImmediate = _global.clearImmediate;\n\t    }\n\n\t    if (isPresent.hrtime) {\n\t        timers.hrtime = _global.process.hrtime;\n\t    }\n\n\t    if (isPresent.nextTick) {\n\t        timers.nextTick = _global.process.nextTick;\n\t    }\n\n\t    if (isPresent.performance) {\n\t        timers.performance = _global.performance;\n\t    }\n\n\t    if (isPresent.requestAnimationFrame) {\n\t        timers.requestAnimationFrame = _global.requestAnimationFrame;\n\t    }\n\n\t    if (isPresent.queueMicrotask) {\n\t        timers.queueMicrotask = _global.queueMicrotask;\n\t    }\n\n\t    if (isPresent.cancelAnimationFrame) {\n\t        timers.cancelAnimationFrame = _global.cancelAnimationFrame;\n\t    }\n\n\t    if (isPresent.requestIdleCallback) {\n\t        timers.requestIdleCallback = _global.requestIdleCallback;\n\t    }\n\n\t    if (isPresent.cancelIdleCallback) {\n\t        timers.cancelIdleCallback = _global.cancelIdleCallback;\n\t    }\n\n\t    if (isPresent.Intl) {\n\t        timers.Intl = NativeIntl;\n\t    }\n\n\t    const originalSetTimeout = _global.setImmediate || _global.setTimeout;\n\n\t    /**\n\t     * @param {Date|number} [start] the system time - non-integer values are floored\n\t     * @param {number} [loopLimit] maximum number of timers that will be run when calling runAll()\n\t     * @returns {Clock}\n\t     */\n\t    function createClock(start, loopLimit) {\n\t        // eslint-disable-next-line no-param-reassign\n\t        start = Math.floor(getEpoch(start));\n\t        // eslint-disable-next-line no-param-reassign\n\t        loopLimit = loopLimit || 1000;\n\t        let nanos = 0;\n\t        const adjustedSystemTime = [0, 0]; // [millis, nanoremainder]\n\n\t        const clock = {\n\t            now: start,\n\t            Date: createDate(),\n\t            loopLimit: loopLimit,\n\t        };\n\n\t        clock.Date.clock = clock;\n\n\t        //eslint-disable-next-line jsdoc/require-jsdoc\n\t        function getTimeToNextFrame() {\n\t            return 16 - ((clock.now - start) % 16);\n\t        }\n\n\t        //eslint-disable-next-line jsdoc/require-jsdoc\n\t        function hrtime(prev) {\n\t            const millisSinceStart = clock.now - adjustedSystemTime[0] - start;\n\t            const secsSinceStart = Math.floor(millisSinceStart / 1000);\n\t            const remainderInNanos =\n\t                (millisSinceStart - secsSinceStart * 1e3) * 1e6 +\n\t                nanos -\n\t                adjustedSystemTime[1];\n\n\t            if (Array.isArray(prev)) {\n\t                if (prev[1] > 1e9) {\n\t                    throw new TypeError(\n\t                        \"Number of nanoseconds can't exceed a billion\",\n\t                    );\n\t                }\n\n\t                const oldSecs = prev[0];\n\t                let nanoDiff = remainderInNanos - prev[1];\n\t                let secDiff = secsSinceStart - oldSecs;\n\n\t                if (nanoDiff < 0) {\n\t                    nanoDiff += 1e9;\n\t                    secDiff -= 1;\n\t                }\n\n\t                return [secDiff, nanoDiff];\n\t            }\n\t            return [secsSinceStart, remainderInNanos];\n\t        }\n\n\t        /**\n\t         * A high resolution timestamp in milliseconds.\n\t         * @typedef {number} DOMHighResTimeStamp\n\t         */\n\n\t        /**\n\t         * performance.now()\n\t         * @returns {DOMHighResTimeStamp}\n\t         */\n\t        function fakePerformanceNow() {\n\t            const hrt = hrtime();\n\t            const millis = hrt[0] * 1000 + hrt[1] / 1e6;\n\t            return millis;\n\t        }\n\n\t        if (isPresent.hrtimeBigint) {\n\t            hrtime.bigint = function () {\n\t                const parts = hrtime();\n\t                return BigInt(parts[0]) * BigInt(1e9) + BigInt(parts[1]); // eslint-disable-line\n\t            };\n\t        }\n\n\t        if (isPresent.Intl) {\n\t            clock.Intl = createIntl();\n\t            clock.Intl.clock = clock;\n\t        }\n\n\t        clock.requestIdleCallback = function requestIdleCallback(\n\t            func,\n\t            timeout,\n\t        ) {\n\t            let timeToNextIdlePeriod = 0;\n\n\t            if (clock.countTimers() > 0) {\n\t                timeToNextIdlePeriod = 50; // const for now\n\t            }\n\n\t            const result = addTimer(clock, {\n\t                func: func,\n\t                args: Array.prototype.slice.call(arguments, 2),\n\t                delay:\n\t                    typeof timeout === \"undefined\"\n\t                        ? timeToNextIdlePeriod\n\t                        : Math.min(timeout, timeToNextIdlePeriod),\n\t                idleCallback: true,\n\t            });\n\n\t            return Number(result);\n\t        };\n\n\t        clock.cancelIdleCallback = function cancelIdleCallback(timerId) {\n\t            return clearTimer(clock, timerId, \"IdleCallback\");\n\t        };\n\n\t        clock.setTimeout = function setTimeout(func, timeout) {\n\t            return addTimer(clock, {\n\t                func: func,\n\t                args: Array.prototype.slice.call(arguments, 2),\n\t                delay: timeout,\n\t            });\n\t        };\n\t        if (typeof _global.Promise !== \"undefined\" && utilPromisify) {\n\t            clock.setTimeout[utilPromisify.custom] =\n\t                function promisifiedSetTimeout(timeout, arg) {\n\t                    return new _global.Promise(function setTimeoutExecutor(\n\t                        resolve,\n\t                    ) {\n\t                        addTimer(clock, {\n\t                            func: resolve,\n\t                            args: [arg],\n\t                            delay: timeout,\n\t                        });\n\t                    });\n\t                };\n\t        }\n\n\t        clock.clearTimeout = function clearTimeout(timerId) {\n\t            return clearTimer(clock, timerId, \"Timeout\");\n\t        };\n\n\t        clock.nextTick = function nextTick(func) {\n\t            return enqueueJob(clock, {\n\t                func: func,\n\t                args: Array.prototype.slice.call(arguments, 1),\n\t                error: isNearInfiniteLimit ? new Error() : null,\n\t            });\n\t        };\n\n\t        clock.queueMicrotask = function queueMicrotask(func) {\n\t            return clock.nextTick(func); // explicitly drop additional arguments\n\t        };\n\n\t        clock.setInterval = function setInterval(func, timeout) {\n\t            // eslint-disable-next-line no-param-reassign\n\t            timeout = parseInt(timeout, 10);\n\t            return addTimer(clock, {\n\t                func: func,\n\t                args: Array.prototype.slice.call(arguments, 2),\n\t                delay: timeout,\n\t                interval: timeout,\n\t            });\n\t        };\n\n\t        clock.clearInterval = function clearInterval(timerId) {\n\t            return clearTimer(clock, timerId, \"Interval\");\n\t        };\n\n\t        if (isPresent.setImmediate) {\n\t            clock.setImmediate = function setImmediate(func) {\n\t                return addTimer(clock, {\n\t                    func: func,\n\t                    args: Array.prototype.slice.call(arguments, 1),\n\t                    immediate: true,\n\t                });\n\t            };\n\n\t            if (typeof _global.Promise !== \"undefined\" && utilPromisify) {\n\t                clock.setImmediate[utilPromisify.custom] =\n\t                    function promisifiedSetImmediate(arg) {\n\t                        return new _global.Promise(\n\t                            function setImmediateExecutor(resolve) {\n\t                                addTimer(clock, {\n\t                                    func: resolve,\n\t                                    args: [arg],\n\t                                    immediate: true,\n\t                                });\n\t                            },\n\t                        );\n\t                    };\n\t            }\n\n\t            clock.clearImmediate = function clearImmediate(timerId) {\n\t                return clearTimer(clock, timerId, \"Immediate\");\n\t            };\n\t        }\n\n\t        clock.countTimers = function countTimers() {\n\t            return (\n\t                Object.keys(clock.timers || {}).length +\n\t                (clock.jobs || []).length\n\t            );\n\t        };\n\n\t        clock.requestAnimationFrame = function requestAnimationFrame(func) {\n\t            const result = addTimer(clock, {\n\t                func: func,\n\t                delay: getTimeToNextFrame(),\n\t                get args() {\n\t                    return [fakePerformanceNow()];\n\t                },\n\t                animation: true,\n\t            });\n\n\t            return Number(result);\n\t        };\n\n\t        clock.cancelAnimationFrame = function cancelAnimationFrame(timerId) {\n\t            return clearTimer(clock, timerId, \"AnimationFrame\");\n\t        };\n\n\t        clock.runMicrotasks = function runMicrotasks() {\n\t            runJobs(clock);\n\t        };\n\n\t        /**\n\t         * @param {number|string} tickValue milliseconds or a string parseable by parseTime\n\t         * @param {boolean} isAsync\n\t         * @param {Function} resolve\n\t         * @param {Function} reject\n\t         * @returns {number|undefined} will return the new `now` value or nothing for async\n\t         */\n\t        function doTick(tickValue, isAsync, resolve, reject) {\n\t            const msFloat =\n\t                typeof tickValue === \"number\"\n\t                    ? tickValue\n\t                    : parseTime(tickValue);\n\t            const ms = Math.floor(msFloat);\n\t            const remainder = nanoRemainder(msFloat);\n\t            let nanosTotal = nanos + remainder;\n\t            let tickTo = clock.now + ms;\n\n\t            if (msFloat < 0) {\n\t                throw new TypeError(\"Negative ticks are not supported\");\n\t            }\n\n\t            // adjust for positive overflow\n\t            if (nanosTotal >= 1e6) {\n\t                tickTo += 1;\n\t                nanosTotal -= 1e6;\n\t            }\n\n\t            nanos = nanosTotal;\n\t            let tickFrom = clock.now;\n\t            let previous = clock.now;\n\t            // ESLint fails to detect this correctly\n\t            /* eslint-disable prefer-const */\n\t            let timer,\n\t                firstException,\n\t                oldNow,\n\t                nextPromiseTick,\n\t                compensationCheck,\n\t                postTimerCall;\n\t            /* eslint-enable prefer-const */\n\n\t            clock.duringTick = true;\n\n\t            // perform microtasks\n\t            oldNow = clock.now;\n\t            runJobs(clock);\n\t            if (oldNow !== clock.now) {\n\t                // compensate for any setSystemTime() call during microtask callback\n\t                tickFrom += clock.now - oldNow;\n\t                tickTo += clock.now - oldNow;\n\t            }\n\n\t            //eslint-disable-next-line jsdoc/require-jsdoc\n\t            function doTickInner() {\n\t                // perform each timer in the requested range\n\t                timer = firstTimerInRange(clock, tickFrom, tickTo);\n\t                // eslint-disable-next-line no-unmodified-loop-condition\n\t                while (timer && tickFrom <= tickTo) {\n\t                    if (clock.timers[timer.id]) {\n\t                        tickFrom = timer.callAt;\n\t                        clock.now = timer.callAt;\n\t                        oldNow = clock.now;\n\t                        try {\n\t                            runJobs(clock);\n\t                            callTimer(clock, timer);\n\t                        } catch (e) {\n\t                            firstException = firstException || e;\n\t                        }\n\n\t                        if (isAsync) {\n\t                            // finish up after native setImmediate callback to allow\n\t                            // all native es6 promises to process their callbacks after\n\t                            // each timer fires.\n\t                            originalSetTimeout(nextPromiseTick);\n\t                            return;\n\t                        }\n\n\t                        compensationCheck();\n\t                    }\n\n\t                    postTimerCall();\n\t                }\n\n\t                // perform process.nextTick()s again\n\t                oldNow = clock.now;\n\t                runJobs(clock);\n\t                if (oldNow !== clock.now) {\n\t                    // compensate for any setSystemTime() call during process.nextTick() callback\n\t                    tickFrom += clock.now - oldNow;\n\t                    tickTo += clock.now - oldNow;\n\t                }\n\t                clock.duringTick = false;\n\n\t                // corner case: during runJobs new timers were scheduled which could be in the range [clock.now, tickTo]\n\t                timer = firstTimerInRange(clock, tickFrom, tickTo);\n\t                if (timer) {\n\t                    try {\n\t                        clock.tick(tickTo - clock.now); // do it all again - for the remainder of the requested range\n\t                    } catch (e) {\n\t                        firstException = firstException || e;\n\t                    }\n\t                } else {\n\t                    // no timers remaining in the requested range: move the clock all the way to the end\n\t                    clock.now = tickTo;\n\n\t                    // update nanos\n\t                    nanos = nanosTotal;\n\t                }\n\t                if (firstException) {\n\t                    throw firstException;\n\t                }\n\n\t                if (isAsync) {\n\t                    resolve(clock.now);\n\t                } else {\n\t                    return clock.now;\n\t                }\n\t            }\n\n\t            nextPromiseTick =\n\t                isAsync &&\n\t                function () {\n\t                    try {\n\t                        compensationCheck();\n\t                        postTimerCall();\n\t                        doTickInner();\n\t                    } catch (e) {\n\t                        reject(e);\n\t                    }\n\t                };\n\n\t            compensationCheck = function () {\n\t                // compensate for any setSystemTime() call during timer callback\n\t                if (oldNow !== clock.now) {\n\t                    tickFrom += clock.now - oldNow;\n\t                    tickTo += clock.now - oldNow;\n\t                    previous += clock.now - oldNow;\n\t                }\n\t            };\n\n\t            postTimerCall = function () {\n\t                timer = firstTimerInRange(clock, previous, tickTo);\n\t                previous = tickFrom;\n\t            };\n\n\t            return doTickInner();\n\t        }\n\n\t        /**\n\t         * @param {string|number} tickValue number of milliseconds or a human-readable value like \"01:11:15\"\n\t         * @returns {number} will return the new `now` value\n\t         */\n\t        clock.tick = function tick(tickValue) {\n\t            return doTick(tickValue, false);\n\t        };\n\n\t        if (typeof _global.Promise !== \"undefined\") {\n\t            /**\n\t             * @param {string|number} tickValue number of milliseconds or a human-readable value like \"01:11:15\"\n\t             * @returns {Promise}\n\t             */\n\t            clock.tickAsync = function tickAsync(tickValue) {\n\t                return new _global.Promise(function (resolve, reject) {\n\t                    originalSetTimeout(function () {\n\t                        try {\n\t                            doTick(tickValue, true, resolve, reject);\n\t                        } catch (e) {\n\t                            reject(e);\n\t                        }\n\t                    });\n\t                });\n\t            };\n\t        }\n\n\t        clock.next = function next() {\n\t            runJobs(clock);\n\t            const timer = firstTimer(clock);\n\t            if (!timer) {\n\t                return clock.now;\n\t            }\n\n\t            clock.duringTick = true;\n\t            try {\n\t                clock.now = timer.callAt;\n\t                callTimer(clock, timer);\n\t                runJobs(clock);\n\t                return clock.now;\n\t            } finally {\n\t                clock.duringTick = false;\n\t            }\n\t        };\n\n\t        if (typeof _global.Promise !== \"undefined\") {\n\t            clock.nextAsync = function nextAsync() {\n\t                return new _global.Promise(function (resolve, reject) {\n\t                    originalSetTimeout(function () {\n\t                        try {\n\t                            const timer = firstTimer(clock);\n\t                            if (!timer) {\n\t                                resolve(clock.now);\n\t                                return;\n\t                            }\n\n\t                            let err;\n\t                            clock.duringTick = true;\n\t                            clock.now = timer.callAt;\n\t                            try {\n\t                                callTimer(clock, timer);\n\t                            } catch (e) {\n\t                                err = e;\n\t                            }\n\t                            clock.duringTick = false;\n\n\t                            originalSetTimeout(function () {\n\t                                if (err) {\n\t                                    reject(err);\n\t                                } else {\n\t                                    resolve(clock.now);\n\t                                }\n\t                            });\n\t                        } catch (e) {\n\t                            reject(e);\n\t                        }\n\t                    });\n\t                });\n\t            };\n\t        }\n\n\t        clock.runAll = function runAll() {\n\t            let numTimers, i;\n\t            runJobs(clock);\n\t            for (i = 0; i < clock.loopLimit; i++) {\n\t                if (!clock.timers) {\n\t                    resetIsNearInfiniteLimit();\n\t                    return clock.now;\n\t                }\n\n\t                numTimers = Object.keys(clock.timers).length;\n\t                if (numTimers === 0) {\n\t                    resetIsNearInfiniteLimit();\n\t                    return clock.now;\n\t                }\n\n\t                clock.next();\n\t                checkIsNearInfiniteLimit(clock, i);\n\t            }\n\n\t            const excessJob = firstTimer(clock);\n\t            throw getInfiniteLoopError(clock, excessJob);\n\t        };\n\n\t        clock.runToFrame = function runToFrame() {\n\t            return clock.tick(getTimeToNextFrame());\n\t        };\n\n\t        if (typeof _global.Promise !== \"undefined\") {\n\t            clock.runAllAsync = function runAllAsync() {\n\t                return new _global.Promise(function (resolve, reject) {\n\t                    let i = 0;\n\t                    /**\n\t                     *\n\t                     */\n\t                    function doRun() {\n\t                        originalSetTimeout(function () {\n\t                            try {\n\t                                runJobs(clock);\n\n\t                                let numTimers;\n\t                                if (i < clock.loopLimit) {\n\t                                    if (!clock.timers) {\n\t                                        resetIsNearInfiniteLimit();\n\t                                        resolve(clock.now);\n\t                                        return;\n\t                                    }\n\n\t                                    numTimers = Object.keys(\n\t                                        clock.timers,\n\t                                    ).length;\n\t                                    if (numTimers === 0) {\n\t                                        resetIsNearInfiniteLimit();\n\t                                        resolve(clock.now);\n\t                                        return;\n\t                                    }\n\n\t                                    clock.next();\n\n\t                                    i++;\n\n\t                                    doRun();\n\t                                    checkIsNearInfiniteLimit(clock, i);\n\t                                    return;\n\t                                }\n\n\t                                const excessJob = firstTimer(clock);\n\t                                reject(getInfiniteLoopError(clock, excessJob));\n\t                            } catch (e) {\n\t                                reject(e);\n\t                            }\n\t                        });\n\t                    }\n\t                    doRun();\n\t                });\n\t            };\n\t        }\n\n\t        clock.runToLast = function runToLast() {\n\t            const timer = lastTimer(clock);\n\t            if (!timer) {\n\t                runJobs(clock);\n\t                return clock.now;\n\t            }\n\n\t            return clock.tick(timer.callAt - clock.now);\n\t        };\n\n\t        if (typeof _global.Promise !== \"undefined\") {\n\t            clock.runToLastAsync = function runToLastAsync() {\n\t                return new _global.Promise(function (resolve, reject) {\n\t                    originalSetTimeout(function () {\n\t                        try {\n\t                            const timer = lastTimer(clock);\n\t                            if (!timer) {\n\t                                runJobs(clock);\n\t                                resolve(clock.now);\n\t                            }\n\n\t                            resolve(clock.tickAsync(timer.callAt - clock.now));\n\t                        } catch (e) {\n\t                            reject(e);\n\t                        }\n\t                    });\n\t                });\n\t            };\n\t        }\n\n\t        clock.reset = function reset() {\n\t            nanos = 0;\n\t            clock.timers = {};\n\t            clock.jobs = [];\n\t            clock.now = start;\n\t        };\n\n\t        clock.setSystemTime = function setSystemTime(systemTime) {\n\t            // determine time difference\n\t            const newNow = getEpoch(systemTime);\n\t            const difference = newNow - clock.now;\n\t            let id, timer;\n\n\t            adjustedSystemTime[0] = adjustedSystemTime[0] + difference;\n\t            adjustedSystemTime[1] = adjustedSystemTime[1] + nanos;\n\t            // update 'system clock'\n\t            clock.now = newNow;\n\t            nanos = 0;\n\n\t            // update timers and intervals to keep them stable\n\t            for (id in clock.timers) {\n\t                if (clock.timers.hasOwnProperty(id)) {\n\t                    timer = clock.timers[id];\n\t                    timer.createdAt += difference;\n\t                    timer.callAt += difference;\n\t                }\n\t            }\n\t        };\n\n\t        /**\n\t         * @param {string|number} tickValue number of milliseconds or a human-readable value like \"01:11:15\"\n\t         * @returns {number} will return the new `now` value\n\t         */\n\t        clock.jump = function jump(tickValue) {\n\t            const msFloat =\n\t                typeof tickValue === \"number\"\n\t                    ? tickValue\n\t                    : parseTime(tickValue);\n\t            const ms = Math.floor(msFloat);\n\n\t            for (const timer of Object.values(clock.timers)) {\n\t                if (clock.now + ms > timer.callAt) {\n\t                    timer.callAt = clock.now + ms;\n\t                }\n\t            }\n\t            clock.tick(ms);\n\t        };\n\n\t        if (isPresent.performance) {\n\t            clock.performance = Object.create(null);\n\t            clock.performance.now = fakePerformanceNow;\n\t        }\n\n\t        if (isPresent.hrtime) {\n\t            clock.hrtime = hrtime;\n\t        }\n\n\t        return clock;\n\t    }\n\n\t    /* eslint-disable complexity */\n\n\t    /**\n\t     * @param {Config=} [config] Optional config\n\t     * @returns {Clock}\n\t     */\n\t    function install(config) {\n\t        if (\n\t            arguments.length > 1 ||\n\t            config instanceof Date ||\n\t            Array.isArray(config) ||\n\t            typeof config === \"number\"\n\t        ) {\n\t            throw new TypeError(\n\t                `FakeTimers.install called with ${String(\n\t                    config,\n\t                )} install requires an object parameter`,\n\t            );\n\t        }\n\n\t        if (_global.Date.isFake === true) {\n\t            // Timers are already faked; this is a problem.\n\t            // Make the user reset timers before continuing.\n\t            throw new TypeError(\n\t                \"Can't install fake timers twice on the same global object.\",\n\t            );\n\t        }\n\n\t        // eslint-disable-next-line no-param-reassign\n\t        config = typeof config !== \"undefined\" ? config : {};\n\t        config.shouldAdvanceTime = config.shouldAdvanceTime || false;\n\t        config.advanceTimeDelta = config.advanceTimeDelta || 20;\n\t        config.shouldClearNativeTimers =\n\t            config.shouldClearNativeTimers || false;\n\n\t        if (config.target) {\n\t            throw new TypeError(\n\t                \"config.target is no longer supported. Use `withGlobal(target)` instead.\",\n\t            );\n\t        }\n\n\t        /**\n\t         * @param {string} timer/object the name of the thing that is not present\n\t         * @param timer\n\t         */\n\t        function handleMissingTimer(timer) {\n\t            if (config.ignoreMissingTimers) {\n\t                return;\n\t            }\n\n\t            throw new ReferenceError(\n\t                `non-existent timers and/or objects cannot be faked: '${timer}'`,\n\t            );\n\t        }\n\n\t        let i, l;\n\t        const clock = createClock(config.now, config.loopLimit);\n\t        clock.shouldClearNativeTimers = config.shouldClearNativeTimers;\n\n\t        clock.uninstall = function () {\n\t            return uninstall(clock, config);\n\t        };\n\n\t        clock.abortListenerMap = new Map();\n\n\t        clock.methods = config.toFake || [];\n\n\t        if (clock.methods.length === 0) {\n\t            clock.methods = Object.keys(timers);\n\t        }\n\n\t        if (config.shouldAdvanceTime === true) {\n\t            const intervalTick = doIntervalTick.bind(\n\t                null,\n\t                clock,\n\t                config.advanceTimeDelta,\n\t            );\n\t            const intervalId = _global.setInterval(\n\t                intervalTick,\n\t                config.advanceTimeDelta,\n\t            );\n\t            clock.attachedInterval = intervalId;\n\t        }\n\n\t        if (clock.methods.includes(\"performance\")) {\n\t            const proto = (() => {\n\t                if (hasPerformanceConstructorPrototype) {\n\t                    return _global.performance.constructor.prototype;\n\t                }\n\t                if (hasPerformancePrototype) {\n\t                    return _global.Performance.prototype;\n\t                }\n\t            })();\n\t            if (proto) {\n\t                Object.getOwnPropertyNames(proto).forEach(function (name) {\n\t                    if (name !== \"now\") {\n\t                        clock.performance[name] =\n\t                            name.indexOf(\"getEntries\") === 0\n\t                                ? NOOP_ARRAY\n\t                                : NOOP;\n\t                    }\n\t                });\n\t                // ensure `mark` returns a value that is valid\n\t                clock.performance.mark = (name) =>\n\t                    new FakePerformanceEntry(name, \"mark\", 0, 0);\n\t                clock.performance.measure = (name) =>\n\t                    new FakePerformanceEntry(name, \"measure\", 0, 100);\n\t                // `timeOrigin` should return the time of when the Window session started\n\t                // (or the Worker was installed)\n\t                clock.performance.timeOrigin = getEpoch(config.now);\n\t            } else if ((config.toFake || []).includes(\"performance\")) {\n\t                return handleMissingTimer(\"performance\");\n\t            }\n\t        }\n\t        if (_global === globalObject && timersModule) {\n\t            clock.timersModuleMethods = [];\n\t        }\n\t        if (_global === globalObject && timersPromisesModule) {\n\t            clock.timersPromisesModuleMethods = [];\n\t        }\n\t        for (i = 0, l = clock.methods.length; i < l; i++) {\n\t            const nameOfMethodToReplace = clock.methods[i];\n\n\t            if (!isPresent[nameOfMethodToReplace]) {\n\t                handleMissingTimer(nameOfMethodToReplace);\n\t                // eslint-disable-next-line\n\t                continue;\n\t            }\n\n\t            if (nameOfMethodToReplace === \"hrtime\") {\n\t                if (\n\t                    _global.process &&\n\t                    typeof _global.process.hrtime === \"function\"\n\t                ) {\n\t                    hijackMethod(_global.process, nameOfMethodToReplace, clock);\n\t                }\n\t            } else if (nameOfMethodToReplace === \"nextTick\") {\n\t                if (\n\t                    _global.process &&\n\t                    typeof _global.process.nextTick === \"function\"\n\t                ) {\n\t                    hijackMethod(_global.process, nameOfMethodToReplace, clock);\n\t                }\n\t            } else {\n\t                hijackMethod(_global, nameOfMethodToReplace, clock);\n\t            }\n\t            if (\n\t                clock.timersModuleMethods !== undefined &&\n\t                timersModule[nameOfMethodToReplace]\n\t            ) {\n\t                const original = timersModule[nameOfMethodToReplace];\n\t                clock.timersModuleMethods.push({\n\t                    methodName: nameOfMethodToReplace,\n\t                    original: original,\n\t                });\n\t                timersModule[nameOfMethodToReplace] =\n\t                    _global[nameOfMethodToReplace];\n\t            }\n\t            if (clock.timersPromisesModuleMethods !== undefined) {\n\t                if (nameOfMethodToReplace === \"setTimeout\") {\n\t                    clock.timersPromisesModuleMethods.push({\n\t                        methodName: \"setTimeout\",\n\t                        original: timersPromisesModule.setTimeout,\n\t                    });\n\n\t                    timersPromisesModule.setTimeout = (\n\t                        delay,\n\t                        value,\n\t                        options = {},\n\t                    ) =>\n\t                        new Promise((resolve, reject) => {\n\t                            const abort = () => {\n\t                                options.signal.removeEventListener(\n\t                                    \"abort\",\n\t                                    abort,\n\t                                );\n\t                                clock.abortListenerMap.delete(abort);\n\n\t                                // This is safe, there is no code path that leads to this function\n\t                                // being invoked before handle has been assigned.\n\t                                // eslint-disable-next-line no-use-before-define\n\t                                clock.clearTimeout(handle);\n\t                                reject(options.signal.reason);\n\t                            };\n\n\t                            const handle = clock.setTimeout(() => {\n\t                                if (options.signal) {\n\t                                    options.signal.removeEventListener(\n\t                                        \"abort\",\n\t                                        abort,\n\t                                    );\n\t                                    clock.abortListenerMap.delete(abort);\n\t                                }\n\n\t                                resolve(value);\n\t                            }, delay);\n\n\t                            if (options.signal) {\n\t                                if (options.signal.aborted) {\n\t                                    abort();\n\t                                } else {\n\t                                    options.signal.addEventListener(\n\t                                        \"abort\",\n\t                                        abort,\n\t                                    );\n\t                                    clock.abortListenerMap.set(\n\t                                        abort,\n\t                                        options.signal,\n\t                                    );\n\t                                }\n\t                            }\n\t                        });\n\t                } else if (nameOfMethodToReplace === \"setImmediate\") {\n\t                    clock.timersPromisesModuleMethods.push({\n\t                        methodName: \"setImmediate\",\n\t                        original: timersPromisesModule.setImmediate,\n\t                    });\n\n\t                    timersPromisesModule.setImmediate = (value, options = {}) =>\n\t                        new Promise((resolve, reject) => {\n\t                            const abort = () => {\n\t                                options.signal.removeEventListener(\n\t                                    \"abort\",\n\t                                    abort,\n\t                                );\n\t                                clock.abortListenerMap.delete(abort);\n\n\t                                // This is safe, there is no code path that leads to this function\n\t                                // being invoked before handle has been assigned.\n\t                                // eslint-disable-next-line no-use-before-define\n\t                                clock.clearImmediate(handle);\n\t                                reject(options.signal.reason);\n\t                            };\n\n\t                            const handle = clock.setImmediate(() => {\n\t                                if (options.signal) {\n\t                                    options.signal.removeEventListener(\n\t                                        \"abort\",\n\t                                        abort,\n\t                                    );\n\t                                    clock.abortListenerMap.delete(abort);\n\t                                }\n\n\t                                resolve(value);\n\t                            });\n\n\t                            if (options.signal) {\n\t                                if (options.signal.aborted) {\n\t                                    abort();\n\t                                } else {\n\t                                    options.signal.addEventListener(\n\t                                        \"abort\",\n\t                                        abort,\n\t                                    );\n\t                                    clock.abortListenerMap.set(\n\t                                        abort,\n\t                                        options.signal,\n\t                                    );\n\t                                }\n\t                            }\n\t                        });\n\t                } else if (nameOfMethodToReplace === \"setInterval\") {\n\t                    clock.timersPromisesModuleMethods.push({\n\t                        methodName: \"setInterval\",\n\t                        original: timersPromisesModule.setInterval,\n\t                    });\n\n\t                    timersPromisesModule.setInterval = (\n\t                        delay,\n\t                        value,\n\t                        options = {},\n\t                    ) => ({\n\t                        [Symbol.asyncIterator]: () => {\n\t                            const createResolvable = () => {\n\t                                let resolve, reject;\n\t                                const promise = new Promise((res, rej) => {\n\t                                    resolve = res;\n\t                                    reject = rej;\n\t                                });\n\t                                promise.resolve = resolve;\n\t                                promise.reject = reject;\n\t                                return promise;\n\t                            };\n\n\t                            let done = false;\n\t                            let hasThrown = false;\n\t                            let returnCall;\n\t                            let nextAvailable = 0;\n\t                            const nextQueue = [];\n\n\t                            const handle = clock.setInterval(() => {\n\t                                if (nextQueue.length > 0) {\n\t                                    nextQueue.shift().resolve();\n\t                                } else {\n\t                                    nextAvailable++;\n\t                                }\n\t                            }, delay);\n\n\t                            const abort = () => {\n\t                                options.signal.removeEventListener(\n\t                                    \"abort\",\n\t                                    abort,\n\t                                );\n\t                                clock.abortListenerMap.delete(abort);\n\n\t                                clock.clearInterval(handle);\n\t                                done = true;\n\t                                for (const resolvable of nextQueue) {\n\t                                    resolvable.resolve();\n\t                                }\n\t                            };\n\n\t                            if (options.signal) {\n\t                                if (options.signal.aborted) {\n\t                                    done = true;\n\t                                } else {\n\t                                    options.signal.addEventListener(\n\t                                        \"abort\",\n\t                                        abort,\n\t                                    );\n\t                                    clock.abortListenerMap.set(\n\t                                        abort,\n\t                                        options.signal,\n\t                                    );\n\t                                }\n\t                            }\n\n\t                            return {\n\t                                next: async () => {\n\t                                    if (options.signal?.aborted && !hasThrown) {\n\t                                        hasThrown = true;\n\t                                        throw options.signal.reason;\n\t                                    }\n\n\t                                    if (done) {\n\t                                        return { done: true, value: undefined };\n\t                                    }\n\n\t                                    if (nextAvailable > 0) {\n\t                                        nextAvailable--;\n\t                                        return { done: false, value: value };\n\t                                    }\n\n\t                                    const resolvable = createResolvable();\n\t                                    nextQueue.push(resolvable);\n\n\t                                    await resolvable;\n\n\t                                    if (returnCall && nextQueue.length === 0) {\n\t                                        returnCall.resolve();\n\t                                    }\n\n\t                                    if (options.signal?.aborted && !hasThrown) {\n\t                                        hasThrown = true;\n\t                                        throw options.signal.reason;\n\t                                    }\n\n\t                                    if (done) {\n\t                                        return { done: true, value: undefined };\n\t                                    }\n\n\t                                    return { done: false, value: value };\n\t                                },\n\t                                return: async () => {\n\t                                    if (done) {\n\t                                        return { done: true, value: undefined };\n\t                                    }\n\n\t                                    if (nextQueue.length > 0) {\n\t                                        returnCall = createResolvable();\n\t                                        await returnCall;\n\t                                    }\n\n\t                                    clock.clearInterval(handle);\n\t                                    done = true;\n\n\t                                    if (options.signal) {\n\t                                        options.signal.removeEventListener(\n\t                                            \"abort\",\n\t                                            abort,\n\t                                        );\n\t                                        clock.abortListenerMap.delete(abort);\n\t                                    }\n\n\t                                    return { done: true, value: undefined };\n\t                                },\n\t                            };\n\t                        },\n\t                    });\n\t                }\n\t            }\n\t        }\n\n\t        return clock;\n\t    }\n\n\t    /* eslint-enable complexity */\n\n\t    return {\n\t        timers: timers,\n\t        createClock: createClock,\n\t        install: install,\n\t        withGlobal: withGlobal,\n\t    };\n\t}\n\n\t/**\n\t * @typedef {object} FakeTimers\n\t * @property {Timers} timers\n\t * @property {createClock} createClock\n\t * @property {Function} install\n\t * @property {withGlobal} withGlobal\n\t */\n\n\t/* eslint-enable complexity */\n\n\t/** @type {FakeTimers} */\n\tconst defaultImplementation = withGlobal(globalObject);\n\n\tfakeTimersSrc.timers = defaultImplementation.timers;\n\tfakeTimersSrc.createClock = defaultImplementation.createClock;\n\tfakeTimersSrc.install = defaultImplementation.install;\n\tfakeTimersSrc.withGlobal = withGlobal;\n\treturn fakeTimersSrc;\n}\n\nvar fakeTimersSrcExports = requireFakeTimersSrc();\n\nclass FakeTimers {\n\t_global;\n\t_clock;\n\t// | _fakingTime | _fakingDate |\n\t// +-------------+-------------+\n\t// | false       | falsy       | initial\n\t// | false       | truthy     | vi.setSystemTime called first (for mocking only Date without fake timers)\n\t// | true        | falsy       | vi.useFakeTimers called first\n\t// | true        | truthy     | unreachable\n\t_fakingTime;\n\t_fakingDate;\n\t_fakeTimers;\n\t_userConfig;\n\t_now = RealDate.now;\n\tconstructor({ global, config }) {\n\t\tthis._userConfig = config;\n\t\tthis._fakingDate = null;\n\t\tthis._fakingTime = false;\n\t\tthis._fakeTimers = fakeTimersSrcExports.withGlobal(global);\n\t\tthis._global = global;\n\t}\n\tclearAllTimers() {\n\t\tif (this._fakingTime) this._clock.reset();\n\t}\n\tdispose() {\n\t\tthis.useRealTimers();\n\t}\n\trunAllTimers() {\n\t\tif (this._checkFakeTimers()) this._clock.runAll();\n\t}\n\tasync runAllTimersAsync() {\n\t\tif (this._checkFakeTimers()) await this._clock.runAllAsync();\n\t}\n\trunOnlyPendingTimers() {\n\t\tif (this._checkFakeTimers()) this._clock.runToLast();\n\t}\n\tasync runOnlyPendingTimersAsync() {\n\t\tif (this._checkFakeTimers()) await this._clock.runToLastAsync();\n\t}\n\tadvanceTimersToNextTimer(steps = 1) {\n\t\tif (this._checkFakeTimers()) for (let i = steps; i > 0; i--) {\n\t\t\tthis._clock.next();\n\t\t\t// Fire all timers at this point: https://github.com/sinonjs/fake-timers/issues/250\n\t\t\tthis._clock.tick(0);\n\t\t\tif (this._clock.countTimers() === 0) break;\n\t\t}\n\t}\n\tasync advanceTimersToNextTimerAsync(steps = 1) {\n\t\tif (this._checkFakeTimers()) for (let i = steps; i > 0; i--) {\n\t\t\tawait this._clock.nextAsync();\n\t\t\t// Fire all timers at this point: https://github.com/sinonjs/fake-timers/issues/250\n\t\t\tthis._clock.tick(0);\n\t\t\tif (this._clock.countTimers() === 0) break;\n\t\t}\n\t}\n\tadvanceTimersByTime(msToRun) {\n\t\tif (this._checkFakeTimers()) this._clock.tick(msToRun);\n\t}\n\tasync advanceTimersByTimeAsync(msToRun) {\n\t\tif (this._checkFakeTimers()) await this._clock.tickAsync(msToRun);\n\t}\n\tadvanceTimersToNextFrame() {\n\t\tif (this._checkFakeTimers()) this._clock.runToFrame();\n\t}\n\trunAllTicks() {\n\t\tif (this._checkFakeTimers())\n // @ts-expect-error method not exposed\n\t\tthis._clock.runMicrotasks();\n\t}\n\tuseRealTimers() {\n\t\tif (this._fakingDate) {\n\t\t\tresetDate();\n\t\t\tthis._fakingDate = null;\n\t\t}\n\t\tif (this._fakingTime) {\n\t\t\tthis._clock.uninstall();\n\t\t\tthis._fakingTime = false;\n\t\t}\n\t}\n\tuseFakeTimers() {\n\t\tif (this._fakingDate) throw new Error(\"\\\"setSystemTime\\\" was called already and date was mocked. Reset timers using `vi.useRealTimers()` if you want to use fake timers again.\");\n\t\tif (!this._fakingTime) {\n\t\t\tconst toFake = Object.keys(this._fakeTimers.timers).filter((timer) => timer !== \"nextTick\" && timer !== \"queueMicrotask\");\n\t\t\tif (this._userConfig?.toFake?.includes(\"nextTick\") && isChildProcess()) throw new Error(\"process.nextTick cannot be mocked inside child_process\");\n\t\t\tthis._clock = this._fakeTimers.install({\n\t\t\t\tnow: Date.now(),\n\t\t\t\t...this._userConfig,\n\t\t\t\ttoFake: this._userConfig?.toFake || toFake,\n\t\t\t\tignoreMissingTimers: true\n\t\t\t});\n\t\t\tthis._fakingTime = true;\n\t\t}\n\t}\n\treset() {\n\t\tif (this._checkFakeTimers()) {\n\t\t\tconst { now } = this._clock;\n\t\t\tthis._clock.reset();\n\t\t\tthis._clock.setSystemTime(now);\n\t\t}\n\t}\n\tsetSystemTime(now) {\n\t\tconst date = typeof now === \"undefined\" || now instanceof Date ? now : new Date(now);\n\t\tif (this._fakingTime) this._clock.setSystemTime(date);\n\t\telse {\n\t\t\tthis._fakingDate = date ?? new Date(this.getRealSystemTime());\n\t\t\tmockDate(this._fakingDate);\n\t\t}\n\t}\n\tgetMockedSystemTime() {\n\t\treturn this._fakingTime ? new Date(this._clock.now) : this._fakingDate;\n\t}\n\tgetRealSystemTime() {\n\t\treturn this._now();\n\t}\n\tgetTimerCount() {\n\t\tif (this._checkFakeTimers()) return this._clock.countTimers();\n\t\treturn 0;\n\t}\n\tconfigure(config) {\n\t\tthis._userConfig = config;\n\t}\n\tisFakeTimers() {\n\t\treturn this._fakingTime;\n\t}\n\t_checkFakeTimers() {\n\t\tif (!this._fakingTime) throw new Error(\"Timers are not mocked. Try calling \\\"vi.useFakeTimers()\\\" first.\");\n\t\treturn this._fakingTime;\n\t}\n}\n\nfunction copyStackTrace(target, source) {\n\tif (source.stack !== void 0) target.stack = source.stack.replace(source.message, target.message);\n\treturn target;\n}\nfunction waitFor(callback, options = {}) {\n\tconst { setTimeout, setInterval, clearTimeout, clearInterval } = getSafeTimers();\n\tconst { interval = 50, timeout = 1e3 } = typeof options === \"number\" ? { timeout: options } : options;\n\tconst STACK_TRACE_ERROR = new Error(\"STACK_TRACE_ERROR\");\n\treturn new Promise((resolve, reject) => {\n\t\tlet lastError;\n\t\tlet promiseStatus = \"idle\";\n\t\tlet timeoutId;\n\t\tlet intervalId;\n\t\tconst onResolve = (result) => {\n\t\t\tif (timeoutId) clearTimeout(timeoutId);\n\t\t\tif (intervalId) clearInterval(intervalId);\n\t\t\tresolve(result);\n\t\t};\n\t\tconst handleTimeout = () => {\n\t\t\tif (intervalId) clearInterval(intervalId);\n\t\t\tlet error = lastError;\n\t\t\tif (!error) error = copyStackTrace(new Error(\"Timed out in waitFor!\"), STACK_TRACE_ERROR);\n\t\t\treject(error);\n\t\t};\n\t\tconst checkCallback = () => {\n\t\t\tif (vi.isFakeTimers()) vi.advanceTimersByTime(interval);\n\t\t\tif (promiseStatus === \"pending\") return;\n\t\t\ttry {\n\t\t\t\tconst result = callback();\n\t\t\t\tif (result !== null && typeof result === \"object\" && typeof result.then === \"function\") {\n\t\t\t\t\tconst thenable = result;\n\t\t\t\t\tpromiseStatus = \"pending\";\n\t\t\t\t\tthenable.then((resolvedValue) => {\n\t\t\t\t\t\tpromiseStatus = \"resolved\";\n\t\t\t\t\t\tonResolve(resolvedValue);\n\t\t\t\t\t}, (rejectedValue) => {\n\t\t\t\t\t\tpromiseStatus = \"rejected\";\n\t\t\t\t\t\tlastError = rejectedValue;\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tonResolve(result);\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tlastError = error;\n\t\t\t}\n\t\t};\n\t\tif (checkCallback() === true) return;\n\t\ttimeoutId = setTimeout(handleTimeout, timeout);\n\t\tintervalId = setInterval(checkCallback, interval);\n\t});\n}\nfunction waitUntil(callback, options = {}) {\n\tconst { setTimeout, setInterval, clearTimeout, clearInterval } = getSafeTimers();\n\tconst { interval = 50, timeout = 1e3 } = typeof options === \"number\" ? { timeout: options } : options;\n\tconst STACK_TRACE_ERROR = new Error(\"STACK_TRACE_ERROR\");\n\treturn new Promise((resolve, reject) => {\n\t\tlet promiseStatus = \"idle\";\n\t\tlet timeoutId;\n\t\tlet intervalId;\n\t\tconst onReject = (error) => {\n\t\t\tif (intervalId) clearInterval(intervalId);\n\t\t\tif (!error) error = copyStackTrace(new Error(\"Timed out in waitUntil!\"), STACK_TRACE_ERROR);\n\t\t\treject(error);\n\t\t};\n\t\tconst onResolve = (result) => {\n\t\t\tif (!result) return;\n\t\t\tif (timeoutId) clearTimeout(timeoutId);\n\t\t\tif (intervalId) clearInterval(intervalId);\n\t\t\tresolve(result);\n\t\t\treturn true;\n\t\t};\n\t\tconst checkCallback = () => {\n\t\t\tif (vi.isFakeTimers()) vi.advanceTimersByTime(interval);\n\t\t\tif (promiseStatus === \"pending\") return;\n\t\t\ttry {\n\t\t\t\tconst result = callback();\n\t\t\t\tif (result !== null && typeof result === \"object\" && typeof result.then === \"function\") {\n\t\t\t\t\tconst thenable = result;\n\t\t\t\t\tpromiseStatus = \"pending\";\n\t\t\t\t\tthenable.then((resolvedValue) => {\n\t\t\t\t\t\tpromiseStatus = \"resolved\";\n\t\t\t\t\t\tonResolve(resolvedValue);\n\t\t\t\t\t}, (rejectedValue) => {\n\t\t\t\t\t\tpromiseStatus = \"rejected\";\n\t\t\t\t\t\tonReject(rejectedValue);\n\t\t\t\t\t});\n\t\t\t\t} else return onResolve(result);\n\t\t\t} catch (error) {\n\t\t\t\tonReject(error);\n\t\t\t}\n\t\t};\n\t\tif (checkCallback() === true) return;\n\t\ttimeoutId = setTimeout(onReject, timeout);\n\t\tintervalId = setInterval(checkCallback, interval);\n\t});\n}\n\nfunction createVitest() {\n\tlet _config = null;\n\tconst workerState = getWorkerState();\n\tlet _timers;\n\tconst timers = () => _timers ||= new FakeTimers({\n\t\tglobal: globalThis,\n\t\tconfig: workerState.config.fakeTimers\n\t});\n\tconst _stubsGlobal = /* @__PURE__ */ new Map();\n\tconst _stubsEnv = /* @__PURE__ */ new Map();\n\tconst _envBooleans = [\n\t\t\"PROD\",\n\t\t\"DEV\",\n\t\t\"SSR\"\n\t];\n\tconst utils = {\n\t\tuseFakeTimers(config) {\n\t\t\tif (isChildProcess()) {\n\t\t\t\tif (config?.toFake?.includes(\"nextTick\") || workerState.config?.fakeTimers?.toFake?.includes(\"nextTick\")) throw new Error(\"vi.useFakeTimers({ toFake: [\\\"nextTick\\\"] }) is not supported in node:child_process. Use --pool=threads if mocking nextTick is required.\");\n\t\t\t}\n\t\t\tif (config) timers().configure({\n\t\t\t\t...workerState.config.fakeTimers,\n\t\t\t\t...config\n\t\t\t});\n\t\t\telse timers().configure(workerState.config.fakeTimers);\n\t\t\ttimers().useFakeTimers();\n\t\t\treturn utils;\n\t\t},\n\t\tisFakeTimers() {\n\t\t\treturn timers().isFakeTimers();\n\t\t},\n\t\tuseRealTimers() {\n\t\t\ttimers().useRealTimers();\n\t\t\treturn utils;\n\t\t},\n\t\trunOnlyPendingTimers() {\n\t\t\ttimers().runOnlyPendingTimers();\n\t\t\treturn utils;\n\t\t},\n\t\tasync runOnlyPendingTimersAsync() {\n\t\t\tawait timers().runOnlyPendingTimersAsync();\n\t\t\treturn utils;\n\t\t},\n\t\trunAllTimers() {\n\t\t\ttimers().runAllTimers();\n\t\t\treturn utils;\n\t\t},\n\t\tasync runAllTimersAsync() {\n\t\t\tawait timers().runAllTimersAsync();\n\t\t\treturn utils;\n\t\t},\n\t\trunAllTicks() {\n\t\t\ttimers().runAllTicks();\n\t\t\treturn utils;\n\t\t},\n\t\tadvanceTimersByTime(ms) {\n\t\t\ttimers().advanceTimersByTime(ms);\n\t\t\treturn utils;\n\t\t},\n\t\tasync advanceTimersByTimeAsync(ms) {\n\t\t\tawait timers().advanceTimersByTimeAsync(ms);\n\t\t\treturn utils;\n\t\t},\n\t\tadvanceTimersToNextTimer() {\n\t\t\ttimers().advanceTimersToNextTimer();\n\t\t\treturn utils;\n\t\t},\n\t\tasync advanceTimersToNextTimerAsync() {\n\t\t\tawait timers().advanceTimersToNextTimerAsync();\n\t\t\treturn utils;\n\t\t},\n\t\tadvanceTimersToNextFrame() {\n\t\t\ttimers().advanceTimersToNextFrame();\n\t\t\treturn utils;\n\t\t},\n\t\tgetTimerCount() {\n\t\t\treturn timers().getTimerCount();\n\t\t},\n\t\tsetSystemTime(time) {\n\t\t\ttimers().setSystemTime(time);\n\t\t\treturn utils;\n\t\t},\n\t\tgetMockedSystemTime() {\n\t\t\treturn timers().getMockedSystemTime();\n\t\t},\n\t\tgetRealSystemTime() {\n\t\t\treturn timers().getRealSystemTime();\n\t\t},\n\t\tclearAllTimers() {\n\t\t\ttimers().clearAllTimers();\n\t\t\treturn utils;\n\t\t},\n\t\tspyOn,\n\t\tfn,\n\t\twaitFor,\n\t\twaitUntil,\n\t\thoisted(factory) {\n\t\t\tassertTypes(factory, \"\\\"vi.hoisted\\\" factory\", [\"function\"]);\n\t\t\treturn factory();\n\t\t},\n\t\tmock(path, factory) {\n\t\t\tif (typeof path !== \"string\") throw new TypeError(`vi.mock() expects a string path, but received a ${typeof path}`);\n\t\t\tconst importer = getImporter(\"mock\");\n\t\t\t_mocker().queueMock(path, importer, typeof factory === \"function\" ? () => factory(() => _mocker().importActual(path, importer, _mocker().getMockContext().callstack)) : factory);\n\t\t},\n\t\tunmock(path) {\n\t\t\tif (typeof path !== \"string\") throw new TypeError(`vi.unmock() expects a string path, but received a ${typeof path}`);\n\t\t\t_mocker().queueUnmock(path, getImporter(\"unmock\"));\n\t\t},\n\t\tdoMock(path, factory) {\n\t\t\tif (typeof path !== \"string\") throw new TypeError(`vi.doMock() expects a string path, but received a ${typeof path}`);\n\t\t\tconst importer = getImporter(\"doMock\");\n\t\t\t_mocker().queueMock(path, importer, typeof factory === \"function\" ? () => factory(() => _mocker().importActual(path, importer, _mocker().getMockContext().callstack)) : factory);\n\t\t},\n\t\tdoUnmock(path) {\n\t\t\tif (typeof path !== \"string\") throw new TypeError(`vi.doUnmock() expects a string path, but received a ${typeof path}`);\n\t\t\t_mocker().queueUnmock(path, getImporter(\"doUnmock\"));\n\t\t},\n\t\tasync importActual(path) {\n\t\t\treturn _mocker().importActual(path, getImporter(\"importActual\"), _mocker().getMockContext().callstack);\n\t\t},\n\t\tasync importMock(path) {\n\t\t\treturn _mocker().importMock(path, getImporter(\"importMock\"));\n\t\t},\n\t\tmockObject(value) {\n\t\t\treturn _mocker().mockObject({ value }).value;\n\t\t},\n\t\tmocked(item, _options = {}) {\n\t\t\treturn item;\n\t\t},\n\t\tisMockFunction(fn) {\n\t\t\treturn isMockFunction(fn);\n\t\t},\n\t\tclearAllMocks() {\n\t\t\t[...mocks].reverse().forEach((spy) => spy.mockClear());\n\t\t\treturn utils;\n\t\t},\n\t\tresetAllMocks() {\n\t\t\t[...mocks].reverse().forEach((spy) => spy.mockReset());\n\t\t\treturn utils;\n\t\t},\n\t\trestoreAllMocks() {\n\t\t\t[...mocks].reverse().forEach((spy) => spy.mockRestore());\n\t\t\treturn utils;\n\t\t},\n\t\tstubGlobal(name, value) {\n\t\t\tif (!_stubsGlobal.has(name)) _stubsGlobal.set(name, Object.getOwnPropertyDescriptor(globalThis, name));\n\t\t\tObject.defineProperty(globalThis, name, {\n\t\t\t\tvalue,\n\t\t\t\twritable: true,\n\t\t\t\tconfigurable: true,\n\t\t\t\tenumerable: true\n\t\t\t});\n\t\t\treturn utils;\n\t\t},\n\t\tstubEnv(name, value) {\n\t\t\tif (!_stubsEnv.has(name)) _stubsEnv.set(name, process.env[name]);\n\t\t\tif (_envBooleans.includes(name)) process.env[name] = value ? \"1\" : \"\";\n\t\t\telse if (value === void 0) delete process.env[name];\n\t\t\telse process.env[name] = String(value);\n\t\t\treturn utils;\n\t\t},\n\t\tunstubAllGlobals() {\n\t\t\t_stubsGlobal.forEach((original, name) => {\n\t\t\t\tif (!original) Reflect.deleteProperty(globalThis, name);\n\t\t\t\telse Object.defineProperty(globalThis, name, original);\n\t\t\t});\n\t\t\t_stubsGlobal.clear();\n\t\t\treturn utils;\n\t\t},\n\t\tunstubAllEnvs() {\n\t\t\t_stubsEnv.forEach((original, name) => {\n\t\t\t\tif (original === void 0) delete process.env[name];\n\t\t\t\telse process.env[name] = original;\n\t\t\t});\n\t\t\t_stubsEnv.clear();\n\t\t\treturn utils;\n\t\t},\n\t\tresetModules() {\n\t\t\tresetModules(workerState.moduleCache);\n\t\t\treturn utils;\n\t\t},\n\t\tasync dynamicImportSettled() {\n\t\t\treturn waitForImportsToResolve();\n\t\t},\n\t\tsetConfig(config) {\n\t\t\tif (!_config) _config = { ...workerState.config };\n\t\t\tObject.assign(workerState.config, config);\n\t\t},\n\t\tresetConfig() {\n\t\t\tif (_config) Object.assign(workerState.config, _config);\n\t\t}\n\t};\n\treturn utils;\n}\nconst vitest = createVitest();\nconst vi = vitest;\nfunction _mocker() {\n\t// @ts-expect-error injected by vite-nide\n\treturn typeof __vitest_mocker__ !== \"undefined\" ? __vitest_mocker__ : new Proxy({}, { get(_, name) {\n\t\tthrow new Error(`Vitest mocker was not initialized in this environment. vi.${String(name)}() is forbidden.`);\n\t} });\n}\nfunction getImporter(name) {\n\tconst stackTrace = createSimpleStackTrace({ stackTraceLimit: 5 });\n\tconst stackArray = stackTrace.split(\"\\n\");\n\t// if there is no message in a stack trace, use the item - 1\n\tconst importerStackIndex = stackArray.findIndex((stack) => {\n\t\treturn stack.includes(` at Object.${name}`) || stack.includes(`${name}@`);\n\t});\n\tconst stack = parseSingleStack(stackArray[importerStackIndex + 1]);\n\treturn stack?.file || \"\";\n}\n\nexport { globalExpect as a, vitest as b, createExpect as c, getSnapshotClient as g, inject as i, vi as v };\n", "import { getSafeTimers } from '@vitest/utils';\n\nconst NAME_WORKER_STATE = \"__vitest_worker__\";\nfunction getWorkerState() {\n\t// @ts-expect-error untyped global\n\tconst workerState = globalThis[NAME_WORKER_STATE];\n\tif (!workerState) {\n\t\tconst errorMsg = \"Vitest failed to access its internal state.\\n\\nOne of the following is possible:\\n- \\\"vitest\\\" is imported directly without running \\\"vitest\\\" command\\n- \\\"vitest\\\" is imported inside \\\"globalSetup\\\" (to fix this, use \\\"setupFiles\\\" instead, because \\\"globalSetup\\\" runs in a different context)\\n- \\\"vitest\\\" is imported inside Vite / Vitest config file\\n- Otherwise, it might be a Vitest bug. Please report it to https://github.com/vitest-dev/vitest/issues\\n\";\n\t\tthrow new Error(errorMsg);\n\t}\n\treturn workerState;\n}\nfunction provideWorkerState(context, state) {\n\tObject.defineProperty(context, NAME_WORKER_STATE, {\n\t\tvalue: state,\n\t\tconfigurable: true,\n\t\twritable: true,\n\t\tenumerable: false\n\t});\n\treturn state;\n}\nfunction getCurrentEnvironment() {\n\tconst state = getWorkerState();\n\treturn state?.environment.name;\n}\nfunction isChildProcess() {\n\treturn typeof process !== \"undefined\" && !!process.send;\n}\nfunction setProcessTitle(title) {\n\ttry {\n\t\tprocess.title = `node (${title})`;\n\t} catch {}\n}\nfunction resetModules(modules, resetMocks = false) {\n\tconst skipPaths = [\n\t\t/\\/vitest\\/dist\\//,\n\t\t/\\/vite-node\\/dist\\//,\n\t\t/vitest-virtual-\\w+\\/dist/,\n\t\t/@vitest\\/dist/,\n\t\t...!resetMocks ? [/^mock:/] : []\n\t];\n\tmodules.forEach((mod, path) => {\n\t\tif (skipPaths.some((re) => re.test(path))) return;\n\t\tmodules.invalidateModule(mod);\n\t});\n}\nfunction waitNextTick() {\n\tconst { setTimeout } = getSafeTimers();\n\treturn new Promise((resolve) => setTimeout(resolve, 0));\n}\nasync function waitForImportsToResolve() {\n\tawait waitNextTick();\n\tconst state = getWorkerState();\n\tconst promises = [];\n\tlet resolvingCount = 0;\n\tfor (const mod of state.moduleCache.values()) {\n\t\tif (mod.promise && !mod.evaluated) promises.push(mod.promise);\n\t\tif (mod.resolving) resolvingCount++;\n\t}\n\tif (!promises.length && !resolvingCount) return;\n\tawait Promise.allSettled(promises);\n\tawait waitForImportsToResolve();\n}\n\nexport { getCurrentEnvironment as a, getWorkerState as g, isChildProcess as i, provideWorkerState as p, resetModules as r, setProcessTitle as s, waitForImportsToResolve as w };\n", "var commonjsGlobal = typeof globalThis !== 'undefined' ? globalThis : typeof window !== 'undefined' ? window : typeof global !== 'undefined' ? global : typeof self !== 'undefined' ? self : {};\n\nfunction getDefaultExportFromCjs (x) {\n\treturn x && x.__esModule && Object.prototype.hasOwnProperty.call(x, 'default') ? x['default'] : x;\n}\n\nexport { commonjsGlobal as c, getDefaultExportFromCjs as g };\n", "/* Ported from https://github.com/boblauer/MockDate/blob/master/src/mockdate.ts */\n/*\nThe MIT License (MIT)\n\nCopyright (c) 2014 Bob <PERSON>er\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n*/\nconst RealDate = Date;\nlet now = null;\nclass MockDate extends RealDate {\n\tconstructor(y, m, d, h, M, s, ms) {\n\t\tsuper();\n\t\tlet date;\n\t\tswitch (arguments.length) {\n\t\t\tcase 0:\n\t\t\t\tif (now !== null) date = new RealDate(now.valueOf());\n\t\t\t\telse date = new RealDate();\n\t\t\t\tbreak;\n\t\t\tcase 1:\n\t\t\t\tdate = new RealDate(y);\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\td = typeof d === \"undefined\" ? 1 : d;\n\t\t\t\th = h || 0;\n\t\t\t\tM = M || 0;\n\t\t\t\ts = s || 0;\n\t\t\t\tms = ms || 0;\n\t\t\t\tdate = new RealDate(y, m, d, h, M, s, ms);\n\t\t\t\tbreak;\n\t\t}\n\t\tObject.setPrototypeOf(date, MockDate.prototype);\n\t\treturn date;\n\t}\n}\nMockDate.UTC = RealDate.UTC;\nMockDate.now = function() {\n\treturn new MockDate().valueOf();\n};\nMockDate.parse = function(dateString) {\n\treturn RealDate.parse(dateString);\n};\nMockDate.toString = function() {\n\treturn RealDate.toString();\n};\nfunction mockDate(date) {\n\tconst dateObj = new RealDate(date.valueOf());\n\tif (Number.isNaN(dateObj.getTime())) throw new TypeError(`mockdate: The time set is an invalid date: ${date}`);\n\t// @ts-expect-error global\n\tglobalThis.Date = MockDate;\n\tnow = dateObj.valueOf();\n}\nfunction resetDate() {\n\tglobalThis.Date = RealDate;\n}\n\nexport { RealDate as R, mockDate as m, resetDate as r };\n", "export { c as createExpect, a as expect, i as inject, v as vi, b as vitest } from './chunks/vi.bdSIJ99Y.js';\nexport { b as bench } from './chunks/benchmark.CYdenmiT.js';\nexport { a as assertType } from './chunks/index.CdQS2e2Q.js';\nexport { expectTypeOf } from 'expect-type';\nexport { afterAll, afterEach, beforeAll, beforeEach, describe, it, onTestFailed, onTestFinished, suite, test } from '@vitest/runner';\nimport * as chai from 'chai';\nexport { chai };\nexport { assert, should } from 'chai';\nimport '@vitest/expect';\nimport '@vitest/runner/utils';\nimport './chunks/utils.XdZDrNZV.js';\nimport '@vitest/utils';\nimport './chunks/_commonjsHelpers.BFTU3MAI.js';\nimport '@vitest/snapshot';\nimport '@vitest/utils/error';\nimport '@vitest/spy';\nimport '@vitest/utils/source-map';\nimport './chunks/date.Bq6ZW5rf.js';\n", "// This file contains setup code for tests\nimport { vi } from \"vitest\";\n\n// Set up global mocks if needed\n// Using a fixed date to avoid recursive calls\nconst OriginalDate = global.Date;\nconst fixedDate = new OriginalDate(\"2023-01-01\");\nglobal.Date = vi.fn(() => fixedDate) as any;\nglobal.Date.now = vi.fn(() => fixedDate.getTime());\n\n// Add any other global setup needed for tests\n"], "mappings": ";AAAA,SAAS,QAAQ,kBAAkB,gBAAgB,YAAY,gBAAgB,wBAAwB,eAAe,4BAA4B,UAAU,UAAU,0BAA0B,sBAAsB;AACtN,SAAS,sBAAsB;AAC/B,SAAS,UAAU,mBAAmB;AACtC,YAAY,YAAY;;;ACHxB,SAAS,qBAAqB;AAE9B,IAAM,oBAAoB;AAC1B,SAAS,iBAAiB;AAEzB,QAAM,cAAc,WAAW,iBAAiB;AAChD,MAAI,CAAC,aAAa;AACjB,UAAM,WAAW;AACjB,UAAM,IAAI,MAAM,QAAQ;AAAA,EACzB;AACA,SAAO;AACR;AAUA,SAAS,wBAAwB;AAChC,QAAM,QAAQ,eAAe;AAC7B,SAAO,OAAO,YAAY;AAC3B;AACA,SAAS,iBAAiB;AACzB,SAAO,OAAO,YAAY,eAAe,CAAC,CAAC,QAAQ;AACpD;AAMA,SAAS,aAAa,SAAS,aAAa,OAAO;AAClD,QAAM,YAAY;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG,CAAC,aAAa,CAAC,QAAQ,IAAI,CAAC;AAAA,EAChC;AACA,UAAQ,QAAQ,CAAC,KAAK,SAAS;AAC9B,QAAI,UAAU,KAAK,CAAC,OAAO,GAAG,KAAK,IAAI,CAAC,EAAG;AAC3C,YAAQ,iBAAiB,GAAG;AAAA,EAC7B,CAAC;AACF;AACA,SAAS,eAAe;AACvB,QAAM,EAAE,WAAW,IAAI,cAAc;AACrC,SAAO,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,CAAC,CAAC;AACvD;AACA,eAAe,0BAA0B;AACxC,QAAM,aAAa;AACnB,QAAM,QAAQ,eAAe;AAC7B,QAAM,WAAW,CAAC;AAClB,MAAI,iBAAiB;AACrB,aAAW,OAAO,MAAM,YAAY,OAAO,GAAG;AAC7C,QAAI,IAAI,WAAW,CAAC,IAAI,UAAW,UAAS,KAAK,IAAI,OAAO;AAC5D,QAAI,IAAI,UAAW;AAAA,EACpB;AACA,MAAI,CAAC,SAAS,UAAU,CAAC,eAAgB;AACzC,QAAM,QAAQ,WAAW,QAAQ;AACjC,QAAM,wBAAwB;AAC/B;;;ADzDA,SAAS,iBAAAA,gBAAe,aAAa,8BAA8B;;;AELnE,IAAI,iBAAiB,OAAO,eAAe,cAAc,aAAa,OAAO,WAAW,cAAc,SAAS,OAAO,WAAW,cAAc,SAAS,OAAO,SAAS,cAAc,OAAO,CAAC;AAE9L,SAAS,wBAAyB,GAAG;AACpC,SAAO,KAAK,EAAE,cAAc,OAAO,UAAU,eAAe,KAAK,GAAG,SAAS,IAAI,EAAE,SAAS,IAAI;AACjG;;;AFGA,SAAS,0BAA0B,eAAe,sBAAsB;AACxE,OAAO;AACP,SAAS,IAAI,OAAO,OAAO,sBAAsB;AACjD,SAAS,wBAAwB;;;AGcjC,IAAM,WAAW;AACjB,IAAI,MAAM;AACV,IAAM,WAAN,MAAM,kBAAiB,SAAS;AAAA,EAC/B,YAAY,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI;AACjC,UAAM;AACN,QAAI;AACJ,YAAQ,UAAU,QAAQ;AAAA,MACzB,KAAK;AACJ,YAAI,QAAQ,KAAM,QAAO,IAAI,SAAS,IAAI,QAAQ,CAAC;AAAA,YAC9C,QAAO,IAAI,SAAS;AACzB;AAAA,MACD,KAAK;AACJ,eAAO,IAAI,SAAS,CAAC;AACrB;AAAA,MACD;AACC,YAAI,OAAO,MAAM,cAAc,IAAI;AACnC,YAAI,KAAK;AACT,YAAI,KAAK;AACT,YAAI,KAAK;AACT,aAAK,MAAM;AACX,eAAO,IAAI,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE;AACxC;AAAA,IACF;AACA,WAAO,eAAe,MAAM,UAAS,SAAS;AAC9C,WAAO;AAAA,EACR;AACD;AACA,SAAS,MAAM,SAAS;AACxB,SAAS,MAAM,WAAW;AACzB,SAAO,IAAI,SAAS,EAAE,QAAQ;AAC/B;AACA,SAAS,QAAQ,SAAS,YAAY;AACrC,SAAO,SAAS,MAAM,UAAU;AACjC;AACA,SAAS,WAAW,WAAW;AAC9B,SAAO,SAAS,SAAS;AAC1B;AACA,SAAS,SAAS,MAAM;AACvB,QAAM,UAAU,IAAI,SAAS,KAAK,QAAQ,CAAC;AAC3C,MAAI,OAAO,MAAM,QAAQ,QAAQ,CAAC,EAAG,OAAM,IAAI,UAAU,8CAA8C,IAAI,EAAE;AAE7G,aAAW,OAAO;AAClB,QAAM,QAAQ,QAAQ;AACvB;AACA,SAAS,YAAY;AACpB,aAAW,OAAO;AACnB;;;AHxDA,IAAM,cAAc;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AACA,SAAS,iBAAiBC,SAAQ;AACjC,SAAO,SAAS,KAAKC,KAAI,UAAU,CAAC,GAAG;AACtC,UAAM,QAAQ,eAAe;AAC7B,UAAM,WAAW,MAAM,OAAO,QAAQ,QAAQ,CAAC;AAC/C,UAAM,EAAE,WAAW,SAAS,YAAY,IAAI,UAAU,SAAS,WAAW,KAAK,QAAQ,IAAI;AAE3F,UAAM,YAAYD,QAAO,MAAM,OAAO,EAAE,YAAY,EAAE,MAAM,KAAK,CAAC;AAClE,IAAAC,MAAKA,IAAG,KAAK,SAAS;AACtB,UAAMC,QAAc,YAAK,KAAK,WAAW,aAAa;AACtD,QAAI,CAACA,MAAM,OAAM,IAAI,MAAM,4CAA4C;AACvE,UAAM,QAAQ,IAAI,MAAM,WAAW,EAAE,IAAI,QAAQ,KAAK,UAAU;AAC/D,YAAM,oBAAoB,QAAQ,IAAI,QAAQ,KAAK,QAAQ;AAC3D,UAAI,OAAO,sBAAsB,WAAY,QAAO,6BAAoC,mBAAY,QAAQ;AAC5G,UAAI,QAAQ,SAAU,QAAO;AAC7B,UAAI,OAAO,QAAQ,YAAY,YAAY,SAAS,GAAG,EAAG,OAAM,IAAI,YAAY,uDAAuD,GAAG,+DAA+D;AACzM,aAAO,YAAY,MAAM;AACxB,cAAM,oBAAoB,IAAI,MAAM,mBAAmB;AACvD,cAAM,UAAU,MAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtD,cAAI;AACJ,cAAI;AACJ,cAAI;AACJ,gBAAM,EAAE,YAAY,aAAa,IAAIC,eAAc;AACnD,gBAAM,QAAQ,YAAY;AACzB,gBAAI;AACH,cAAO,YAAK,KAAK,WAAW,SAAS,GAAG;AACxC,oBAAM,MAAM,MAAMF,IAAG;AACrB,cAAO,YAAK,KAAK,WAAW,UAAU,GAAG;AACzC,sBAAQ,MAAM,kBAAkB,KAAK,WAAW,GAAG,IAAI,CAAC;AACxD,2BAAa,UAAU;AACvB,2BAAa,SAAS;AAAA,YACvB,SAAS,KAAK;AACb,0BAAY;AACZ,kBAAI,CAAQ,YAAK,KAAK,WAAW,oBAAoB,EAAG,cAAa,WAAW,OAAO,QAAQ;AAAA,YAChG;AAAA,UACD;AACA,sBAAY,WAAW,MAAM;AAC5B,yBAAa,UAAU;AACvB,YAAO,YAAK,KAAK,WAAW,sBAAsB,IAAI;AACtD,kBAAM,kBAAkB,CAAC,UAAU;AAClC,qBAAO,iBAAiB,IAAI,MAAM,oCAAoC,EAAE,MAAM,CAAC,GAAG,iBAAiB,CAAC;AAAA,YACrG;AACA,kBAAM,EAAE,KAAK,MAAM,gBAAgB,SAAS,CAAC,EAAE,MAAM,CAAC,MAAM,gBAAgB,CAAC,CAAC;AAAA,UAC/E,GAAG,OAAO;AACV,gBAAM;AAAA,QACP,CAAC;AACD,YAAI,UAAU;AACd,QAAAC,MAAK,eAAe,CAAC;AACrB,QAAAA,MAAK,WAAW,KAAK,MAAM;AAC1B,cAAI,CAAC,SAAS;AACb,kBAAM,UAAiB,YAAK,KAAK,WAAW,QAAQ,IAAI,SAAS;AACjE,kBAAM,OAAc,YAAK,KAAK,WAAW,eAAe,IAAI,qBAAqB;AACjF,kBAAM,kBAAkB,UAAU,IAAI,IAAI,OAAO,GAAG,OAAO,GAAG,CAAC;AAC/D,kBAAM,QAAQ,IAAI,MAAM,GAAG,eAAe;AAAA;AAAA,QAA+I,eAAe;AAAA,CAAI;AAC5M,kBAAM,iBAAiB,OAAO,iBAAiB;AAAA,UAChD;AAAA,QACD,CAAC;AACD,YAAI;AAGJ,eAAO;AAAA,UACN,KAAK,aAAa,YAAY;AAC7B,sBAAU;AACV,oBAAQ,kBAAkB,QAAQ,GAAG,KAAK,aAAa,UAAU;AAAA,UAClE;AAAA,UACA,MAAM,YAAY;AACjB,oBAAQ,kBAAkB,QAAQ,GAAG,MAAM,UAAU;AAAA,UACtD;AAAA,UACA,QAAQ,WAAW;AAClB,oBAAQ,kBAAkB,QAAQ,GAAG,QAAQ,SAAS;AAAA,UACvD;AAAA,UACA,CAAC,OAAO,WAAW,GAAG;AAAA,QACvB;AAAA,MACD;AAAA,IACD,EAAE,CAAC;AACH,WAAO;AAAA,EACR;AACD;AACA,SAAS,iBAAiB,QAAQ,QAAQ;AACzC,MAAI,OAAO,UAAU,OAAQ,QAAO,QAAQ,OAAO,MAAM,QAAQ,OAAO,SAAS,OAAO,OAAO;AAC/F,SAAO;AACR;AAEA,SAAS,gBAAgB,MAAM;AAC9B,QAAM,IAAI,MAAM,oCAAoC,OAAO,2JAA2J;AACvN;AAEA,IAAI,eAAe,EAAC,SAAS,CAAC,EAAC;AAE/B,IAAI,aAAa,aAAa;AAE9B,IAAI;AAEJ,SAAS,oBAAqB;AAC7B,MAAI,sBAAuB,QAAO,aAAa;AAC/C,0BAAwB;AACxB,GAAC,SAAU,QAAQ,SAAS;AAC3B,KAAC,WAAW;AACX,OAAC,SAASE,aAAY;AACrB,YAAI,OAAO,oBAAoB,cAAc,QAAyB,MAAuB;AAC5F,iBAAO,OAAO,UAAUA;AAAA,QACzB,OAAO;AACN,iBAAO,KAAK,IAAIA,WAAU;AAAA,QAC3B;AAAA,MACD,GAAG,SAASC,OAAM,OAAO;AACxB,YAAIC,aAAYD,MAAK;AACrB,YAAI,qBAAqBC,WAAU;AAEnC,QAAAA,WAAU,UAAU,iBAAiB,SAAU,UAAU;AACxD,cAAI,SAAS,MAAM,KAAK,MAAM,QAAQ;AACtC,cAAI,WAAWD,MAAK,OAAO;AAE3B,6BAAmB,OAAO;AAAA,YAAK;AAAA,YAC9B,QAAQ,UAAU,MAAM;AAAA,YACxB;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACD;AAAA,QACD,CAAC;AAED,QAAAA,MAAK,OAAO,gBAAgB,SAAS,KAAK,KAAK,KAAK;AACnD,cAAIA,MAAK,UAAU,KAAK,GAAG,EAAE,GAAG,GAAG,cAAc,GAAG;AAAA,QACrD;AAEA,iBAAS,QAAQ,UAAU,QAAQ;AAClC,cAAI,aAAa,QAAQ;AACxB,mBAAO;AAAA,UACR;AACA,cAAI,OAAO,WAAY,OAAO,UAAW;AACxC,mBAAO;AAAA,UACR;AACA,cAAI,OAAO,aAAc,YAAY,aAAa,MAAM;AACvD,mBAAO,aAAa;AAAA,UACrB;AACA,cAAI,CAAC,CAAC,YAAY,CAAC,QAAQ;AAC1B,mBAAO;AAAA,UACR;AAEA,cAAI,MAAM,QAAQ,QAAQ,GAAG;AAC5B,gBAAI,OAAO,OAAO,WAAY,UAAU;AACvC,qBAAO;AAAA,YACR;AACA,gBAAI,KAAK,MAAM,UAAU,MAAM,KAAK,MAAM;AAC1C,mBAAO,SAAS,MAAM,SAAU,KAAK;AACpC,qBAAO,GAAG,KAAK,SAAU,KAAK;AAC7B,uBAAO,QAAQ,KAAK,GAAG;AAAA,cACxB,CAAC;AAAA,YACF,CAAC;AAAA,UACF;AAEA,cAAI,oBAAoB,MAAM;AAC7B,gBAAI,kBAAkB,MAAM;AAC3B,qBAAO,SAAS,QAAQ,MAAM,OAAO,QAAQ;AAAA,YAC9C,OAAO;AACN,qBAAO;AAAA,YACR;AAAA,UACD;AAEA,iBAAO,OAAO,KAAK,QAAQ,EAAE,MAAM,SAAU,KAAK;AACjD,gBAAI,KAAK,SAAS,GAAG;AACrB,gBAAI,KAAK,OAAO,GAAG;AACnB,gBAAI,OAAO,OAAQ,YAAY,OAAO,QAAQ,OAAO,MAAM;AAC1D,qBAAO,QAAQ,IAAI,EAAE;AAAA,YACtB;AACA,gBAAI,OAAO,OAAQ,YAAY;AAC9B,qBAAO,GAAG,EAAE;AAAA,YACb;AACA,mBAAO,OAAO;AAAA,UACf,CAAC;AAAA,QACF;AAAA,MACD,CAAC;AAAA,IAEF,GAAG,KAAK,UAAU;AAAA,EACnB,GAAG,YAAY;AACf,SAAO,aAAa;AACrB;AAEA,IAAI,oBAAoB,kBAAkB;AAC1C,IAAI,SAAsB,wCAAwB,iBAAiB;AAEnE,SAAS,uBAAuBE,OAAM,WAAW,SAAS;AACzD,QAAM,MAAMA,MAAK,KAAK,WAAW,QAAQ,IAAI,SAAS;AACtD,QAAM,OAAO,GAAGA,MAAK,KAAK,WAAW,OAAO,CAAC,IAAI,UAAW;AAC5D,QAAM,cAAcA,MAAK,KAAK,WAAW,SAAS;AAClD,QAAM,UAAU,cAAc,IAAI,WAAW,KAAK;AAClD,SAAO,iBAAiB,OAAO,IAAI,GAAG,GAAG,IAAI;AAC9C;AACA,SAAS,kBAAkB,OAAO,SAAS,WAAW,OAAO;AAC5D,QAAML,QAAO;AAEb,MAAIA,SAAQ,mBAAmB,SAAS;AAEvC,cAAU,QAAQ,QAAQ,MAAM;AAC/B,UAAI,CAACA,MAAK,SAAU;AACpB,YAAM,QAAQA,MAAK,SAAS,QAAQ,OAAO;AAC3C,UAAI,UAAU,GAAI,CAAAA,MAAK,SAAS,OAAO,OAAO,CAAC;AAAA,IAChD,CAAC;AAED,QAAI,CAACA,MAAK,SAAU,CAAAA,MAAK,WAAW,CAAC;AACrC,IAAAA,MAAK,SAAS,KAAK,OAAO;AAC1B,QAAI,WAAW;AACf,IAAAA,MAAK,eAAe,CAAC;AACrB,IAAAA,MAAK,WAAW,KAAK,MAAM;AAC1B,UAAI,CAAC,UAAU;AACd,cAAM,YAAY,WAAW,mBAAmB,uBAAuB,CAAC,MAAM,KAAK;AACnF,cAAM,QAAQ,UAAU,MAAM,KAAK;AACnC,gBAAQ,KAAK;AAAA,UACZ,yBAAyB,SAAS;AAAA,UAClC;AAAA,UACA;AAAA,UACA;AAAA,QACD,EAAE,KAAK,EAAE,CAAC;AAAA,MACX;AAAA,IACD,CAAC;AACD,WAAO;AAAA,MACN,KAAK,aAAa,YAAY;AAC7B,mBAAW;AACX,eAAO,QAAQ,KAAK,aAAa,UAAU;AAAA,MAC5C;AAAA,MACA,MAAM,YAAY;AACjB,eAAO,QAAQ,MAAM,UAAU;AAAA,MAChC;AAAA,MACA,QAAQ,WAAW;AAClB,eAAO,QAAQ,QAAQ,SAAS;AAAA,MACjC;AAAA,MACA,CAAC,OAAO,WAAW,GAAG;AAAA,IACvB;AAAA,EACD;AACA,SAAO;AACR;AAEA,IAAI;AACJ,SAAS,oBAAoB;AAC5B,MAAI,CAAC,QAAS,WAAU,IAAI,eAAe,EAAE,SAAS,CAAC,UAAU,aAAa;AAC7E,WAAO,OAAO,UAAU,UAAU,CAAC,kBAAkB,cAAc,CAAC;AAAA,EACrE,EAAE,CAAC;AACH,SAAO;AACR;AACA,SAAS,SAAS,UAAU,SAAS;AACpC,MAAI,OAAO,aAAa,YAAY;AACnC,QAAI,CAAC,QAAS,OAAM,IAAI,MAAM,yCAAyC,OAAO,QAAQ,EAAE;AAExF,WAAO;AAAA,EACR;AACA,MAAI;AACH,aAAS;AAAA,EACV,SAAS,GAAG;AACX,WAAO;AAAA,EACR;AACA,QAAM,IAAI,MAAM,gCAAgC;AACjD;AACA,SAAS,aAAaA,OAAM;AAC3B,SAAO;AAAA,IACN,UAAUA,MAAK,KAAK;AAAA,IACpB,MAAM,SAASA,KAAI,EAAE,MAAM,CAAC,EAAE,KAAK,KAAK;AAAA,IACxC,QAAQA,MAAK;AAAA,EACd;AACD;AACA,IAAM,iBAAiB,CAACG,OAAM,UAAU;AACvC,WAAS,QAAQ,eAAe,KAAK;AACpC,UAAMH,QAAO,MAAM,KAAK,KAAK,aAAa;AAC1C,QAAI,CAACA,MAAM,OAAM,IAAI,MAAM,IAAI,aAAa,uCAAuC;AACnF,WAAOA;AAAA,EACR;AACA,aAAW,OAAO,CAAC,iBAAiB,iBAAiB,EAAG,OAAM,UAAUG,MAAK,UAAU,WAAW,KAAK,SAAS,YAAY,SAAS;AACpI,UAAM,KAAK,MAAM,SAAS,GAAG;AAC7B,UAAM,QAAQ,MAAM,KAAK,MAAM,QAAQ;AACvC,QAAI,MAAO,OAAM,IAAI,MAAM,GAAG,GAAG,4BAA4B;AAC7D,UAAM,WAAW,MAAM,KAAK,MAAM,QAAQ;AAC1C,UAAMH,QAAO,QAAQ,KAAK,IAAI;AAC9B,QAAI,OAAO,eAAe,YAAY,OAAO,YAAY,aAAa;AACrE,gBAAU;AACV,mBAAa;AAAA,IACd;AACA,UAAM,eAAe,MAAM,KAAK,MAAM,SAAS;AAC/C,sBAAkB,EAAE,OAAO;AAAA,MAC1B,UAAU;AAAA,MACV;AAAA,MACA,UAAU;AAAA,MACV;AAAA,MACA;AAAA,MACA,GAAG,aAAaA,KAAI;AAAA,IACrB,CAAC;AAAA,EACF,CAAC;AACD,QAAM,UAAUG,MAAK,UAAU,WAAW,uBAAuB,SAAS,MAAM,SAAS;AACxF,UAAM,KAAK,MAAM,SAAS,qBAAqB;AAC/C,UAAM,QAAQ,MAAM,KAAK,MAAM,QAAQ;AACvC,QAAI,MAAO,OAAM,IAAI,MAAM,+CAAiD;AAC5E,UAAM,QAAQ,IAAI,MAAM,UAAU;AAClC,UAAM,WAAW,MAAM,KAAK,MAAM,QAAQ;AAC1C,UAAMH,QAAO,QAAQ,uBAAuB,IAAI;AAChD,UAAM,eAAe,MAAM,KAAK,MAAM,SAAS;AAC/C,UAAM,UAAU,kBAAkB,EAAE,UAAU;AAAA,MAC7C,UAAU;AAAA,MACV;AAAA,MACA,UAAU;AAAA,MACV,aAAa,EAAE,KAAK;AAAA,MACpB;AAAA,MACA,GAAG,aAAaA,KAAI;AAAA,IACrB,CAAC;AACD,WAAO,kBAAkBA,OAAM,SAAS,uBAAuB,OAAO,IAAI,GAAG,KAAK;AAAA,EACnF,CAAC;AACD,QAAM,UAAUG,MAAK,UAAU,WAAW,yBAAyB,SAAS,oBAAoB,YAAY,gBAAgB,SAAS;AACpI,UAAM,KAAK,MAAM,SAAS,uBAAuB;AACjD,UAAM,QAAQ,MAAM,KAAK,MAAM,QAAQ;AACvC,QAAI,MAAO,OAAM,IAAI,MAAM,iDAAmD;AAC9E,UAAMH,QAAO,QAAQ,yBAAyB,IAAI;AAClD,UAAM,eAAeA,MAAK,QAAQA,MAAK,OAAO;AAC9C,QAAI,aAAc,OAAM,IAAI,MAAM,oEAAoE;AACtG,UAAM,WAAW,MAAM,KAAK,MAAM,QAAQ;AAC1C,UAAM,QAAQ,MAAM,KAAK,MAAM,OAAO;AACtC,QAAI,OAAO,eAAe,UAAU;AACnC,gBAAU;AACV,uBAAiB;AACjB,mBAAa;AAAA,IACd;AACA,QAAI,eAAgB,kBAAiB,yBAAyB,cAAc;AAC5E,UAAM,eAAe,MAAM,KAAK,MAAM,SAAS;AAC/C,sBAAkB,EAAE,OAAO;AAAA,MAC1B,UAAU;AAAA,MACV;AAAA,MACA,UAAU;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,GAAG,aAAaA,KAAI;AAAA,IACrB,CAAC;AAAA,EACF,CAAC;AACD,QAAM,UAAUG,MAAK,UAAU,WAAW,gCAAgC,SAAS,SAAS;AAC3F,UAAM,KAAK,MAAM,SAAS,8BAA8B;AACxD,UAAM,QAAQ,MAAM,KAAK,MAAM,QAAQ;AACvC,QAAI,MAAO,OAAM,IAAI,MAAM,wDAA0D;AACrF,UAAM,WAAW,MAAM,KAAK,MAAM,QAAQ;AAC1C,UAAMH,QAAO,QAAQ,gCAAgC,IAAI;AACzD,UAAM,UAAU,MAAM,KAAK,MAAM,SAAS;AAC1C,UAAM,eAAe,MAAM,KAAK,MAAM,SAAS;AAC/C,sBAAkB,EAAE,OAAO;AAAA,MAC1B,UAAU,SAAS,UAAU,OAAO;AAAA,MACpC;AAAA,MACA;AAAA,MACA,GAAG,aAAaA,KAAI;AAAA,IACrB,CAAC;AAAA,EACF,CAAC;AACD,QAAM,UAAUG,MAAK,UAAU,WAAW,sCAAsC,SAAS,oBAAoB,gBAAgB,SAAS;AACrI,UAAM,QAAQ,MAAM,KAAK,MAAM,QAAQ;AACvC,QAAI,MAAO,OAAM,IAAI,MAAM,8DAAgE;AAC3F,UAAMH,QAAO,QAAQ,sCAAsC,IAAI;AAC/D,UAAM,eAAeA,MAAK,QAAQA,MAAK,OAAO;AAC9C,QAAI,aAAc,OAAM,IAAI,MAAM,oEAAoE;AACtG,UAAM,WAAW,MAAM,KAAK,MAAM,QAAQ;AAC1C,UAAM,QAAQ,MAAM,KAAK,MAAM,OAAO;AACtC,UAAM,UAAU,MAAM,KAAK,MAAM,SAAS;AAC1C,UAAM,eAAe,MAAM,KAAK,MAAM,SAAS;AAC/C,QAAI,eAAgB,kBAAiB,yBAAyB,cAAc;AAC5E,sBAAkB,EAAE,OAAO;AAAA,MAC1B,UAAU,SAAS,UAAU,OAAO;AAAA,MACpC;AAAA,MACA;AAAA,MACA,UAAU;AAAA,MACV;AAAA,MACA;AAAA,MACA,GAAG,aAAaA,KAAI;AAAA,IACrB,CAAC;AAAA,EACF,CAAC;AACD,QAAM,UAAUG,MAAK,QAAQ,yBAAyB,aAAa;AACpE;AAEO,WAAI,UAAU;AACd,WAAI,cAAc;AAClB,WAAI,MAAM;AACV,WAAI,cAAc;AAClB,WAAI,sBAAsB;AAEjC,SAAS,aAAaH,OAAM;AAC3B,QAAMF,UAAS,CAAC,OAAO,YAAY;AAClC,UAAM,EAAE,eAAe,IAAI,SAASA,OAAM;AAC1C,aAAS,EAAE,gBAAgB,iBAAiB,EAAE,GAAGA,OAAM;AACvD,UAAMQ,UAAgB,cAAO,OAAO,OAAO;AAC3C,UAAM,QAAQN,SAAQ,eAAe;AACrC,QAAI;AAEJ,aAAOM,QAAO,SAAS,KAAK;AAAA,QACvB,QAAOA;AAAA,EACb;AACA,SAAO,OAAOR,SAAe,aAAM;AACnC,SAAO,OAAOA,SAAQ,WAAW,0BAA0B,CAAC;AAC5D,EAAAA,QAAO,WAAW,MAAM,SAASA,OAAM;AACvC,EAAAA,QAAO,WAAW,CAAC,UAAU,SAAS,OAAOA,OAAM;AAEnD,QAAM,cAAc,SAAS,WAAW,aAAa,CAAC,KAAK,CAAC;AAC5D,WAAS;AAAA,IACR,GAAG;AAAA,IACH,gBAAgB;AAAA,IAChB,uBAAuB;AAAA,IACvB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,kCAAkC;AAAA,IAClC,aAAa,sBAAsB;AAAA,IACnC,IAAI,WAAW;AACd,aAAO,eAAe,EAAE;AAAA,IACzB;AAAA,IACA,iBAAiBE,QAAO,YAAYA,KAAI,IAAI,YAAY;AAAA,EACzD,GAAGF,OAAM;AAET,EAAAA,QAAO,SAAS,CAAC,aAAoB,cAAO,OAAOA,SAAQ,QAAQ;AACnE,EAAAA,QAAO,qBAAqB,CAAC,kBAAkB,yBAAyB,aAAa;AACrF,EAAAA,QAAO,OAAO,IAAI,SAAS;AAE1B,WAAOA,QAAO,GAAG,IAAI,EAAE,YAAY,EAAE,MAAM,KAAK,CAAC;AAAA,EAClD;AACA,EAAAA,QAAO,OAAO,iBAAiBA,OAAM;AACrC,EAAAA,QAAO,cAAc,CAAC,YAAY;AACjC,IAAO,cAAO,KAAK,WAAW,UAAU,KAAK,OAAO,OAAO,GAAG,mBAAmB;AAAA,EAClF;AACA,WAAS,WAAW,UAAU;AAC7B,UAAM,WAAW,MAAM,IAAI,MAAM,uCAAuC,QAAQ,aAAaA,QAAO,SAAS,EAAE,cAAc,EAAE;AAC/H,QAAI,MAAM,kBAAmB,OAAM,kBAAkB,SAAS,GAAG,UAAU;AAC3E,IAAAA,QAAO,SAAS;AAAA,MACf,0BAA0B;AAAA,MAC1B,kCAAkC;AAAA,IACnC,CAAC;AAAA,EACF;AACA,WAAS,gBAAgB;AACxB,UAAM,QAAQ,IAAI,MAAM,gDAAgD;AACxE,QAAI,MAAM,kBAAmB,OAAM,kBAAkB,OAAO,aAAa;AACzE,IAAAA,QAAO,SAAS;AAAA,MACf,uBAAuB;AAAA,MACvB,4BAA4B;AAAA,IAC7B,CAAC;AAAA,EACF;AACA,EAAO,YAAK,UAAUA,SAAQ,cAAc,UAAU;AACtD,EAAO,YAAK,UAAUA,SAAQ,iBAAiB,aAAa;AAC5D,EAAAA,QAAO,OAAO,cAAc;AAC5B,SAAOA;AACR;AACA,IAAM,eAAe,aAAa;AAClC,OAAO,eAAe,YAAY,eAAe;AAAA,EAChD,OAAO;AAAA,EACP,UAAU;AAAA,EACV,cAAc;AACf,CAAC;AAWD,IAAI,gBAAgB,CAAC;AAErB,IAAIS;AACJ,IAAI;AAEJ,SAAS,gBAAiB;AACzB,MAAI,kBAAmB,QAAOA;AAC9B,sBAAoB;AAMpB,MAAI;AAGJ,MAAI,OAAO,mBAAmB,aAAa;AAEvC,mBAAe;AAAA,EACnB,WAAW,OAAO,WAAW,aAAa;AAEtC,mBAAe;AAAA,EACnB,OAAO;AAEH,mBAAe;AAAA,EACnB;AAEA,EAAAA,UAAS;AACT,SAAOA;AACR;AAEA,IAAI;AACJ,IAAI;AAEJ,SAAS,uBAAwB;AAChC,MAAI,yBAA0B,QAAO;AACrC,6BAA2B;AAU3B,MAAI;AACJ,MAAI;AACA,UAAMC,UAAS,CAAC;AAEhB,IAAAA,QAAO;AACP,oBAAgB;AAAA,EACpB,SAAS,GAAG;AAIR,oBAAgB;AAAA,EACpB;AAEA,oBAAkB;AAClB,SAAO;AACR;AAEA,IAAI;AACJ,IAAI;AAEJ,SAAS,8BAA+B;AACvC,MAAI,gCAAiC,QAAO;AAC5C,oCAAkC;AAElC,MAAI,OAAO,SAAS;AACpB,MAAI,gBAAgB,qBAAqB;AAEzC,MAAI,uBAAuB;AAAA;AAAA,IAEvB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAKA,MAAI,eAAe;AACf,yBAAqB,KAAK,WAAW;AAAA,EACzC;AAEA,yBAAuB,SAASC,sBAAqB,WAAW;AAE5D,WAAO,OAAO,oBAAoB,SAAS,EAAE;AAAA,MAAO,SAChD,QACA,MACF;AACE,YAAI,qBAAqB,SAAS,IAAI,GAAG;AACrC,iBAAO;AAAA,QACX;AAEA,YAAI,OAAO,UAAU,IAAI,MAAM,YAAY;AACvC,iBAAO;AAAA,QACX;AAEA,eAAO,IAAI,IAAI,KAAK,KAAK,UAAU,IAAI,CAAC;AAExC,eAAO;AAAA,MACX;AAAA,MACA,uBAAO,OAAO,IAAI;AAAA,IAAC;AAAA,EACvB;AACA,SAAO;AACR;AAEA,IAAI;AACJ,IAAI;AAEJ,SAAS,eAAgB;AACxB,MAAI,iBAAkB,QAAO;AAC7B,qBAAmB;AAEnB,MAAI,gBAAgB,4BAA4B;AAEhD,UAAQ,cAAc,MAAM,SAAS;AACrC,SAAO;AACR;AAEA,IAAI;AACJ,IAAI;AAEJ,SAAS,uBAAwB;AAChC,MAAI,yBAA0B,QAAO;AACrC,6BAA2B;AAE3B,MAAIC,SAAQ,aAAa,EAAE;AAK3B,WAAS,aAAa,SAAS,KAAK;AAChC,QAAI,QAAQ,IAAI,EAAE,MAAM,QAAW;AAC/B,cAAQ,IAAI,EAAE,IAAI;AAAA,IACtB;AAEA,WAAO,QAAQ,IAAI,EAAE,IAAI,IAAI;AAAA,EACjC;AAKA,WAAS,mBAAmB,SAAS,KAAK,OAAO,OAAO;AACpD,QAAI,mBAAmB;AAEvB,QAAI,UAAU,MAAM,SAAS,GAAG;AAC5B,yBAAmB,IAAI,aAAa,MAAM,QAAQ,CAAC,CAAC;AAAA,IACxD;AAEA,QAAI,aAAa,SAAS,GAAG,KAAK,kBAAkB;AAChD,cAAQ,IAAI,EAAE,KAAK;AACnB,aAAO;AAAA,IACX;AAEA,WAAO;AAAA,EACX;AAeA,WAAS,cAAc,OAAO;AAC1B,QAAI,UAAU,CAAC;AAEf,QAAI,SAAS,UAAU,SAAS,IAAI,YAAY;AAEhD,WAAOA,OAAM,QAAQ,mBAAmB,KAAK,MAAM,OAAO,CAAC;AAAA,EAC/D;AAEA,oBAAkB;AAClB,SAAO;AACR;AAEA,IAAI;AACJ,IAAI;AAEJ,SAAS,mBAAoB;AAC5B,MAAI,qBAAsB,QAAO;AACjC,yBAAuB;AAOvB,WAAS,UAAU,OAAO;AACtB,UAAM,OAAO,MAAM,eAAe,MAAM,YAAY;AACpD,WAAO,QAAQ;AAAA,EACnB;AAEA,gBAAc;AACd,SAAO;AACR;AAEA,IAAI,aAAa,CAAC;AAIlB,IAAI;AAEJ,SAAS,oBAAqB;AAC7B,MAAI,sBAAuB,QAAO;AAClC,0BAAwB;AACxB,GAAC,SAAU,SAAS;AASnB,YAAQ,OAAO,SAAU,MAAM,KAAK;AAChC,UAAI,UAAU,WAAY;AACtB,gBAAQ,aAAa,GAAG;AACxB,eAAO,KAAK,MAAM,MAAM,SAAS;AAAA,MACrC;AACA,UAAI,KAAK,WAAW;AAChB,gBAAQ,YAAY,KAAK;AAAA,MAC7B;AACA,aAAO;AAAA,IACX;AASA,YAAQ,aAAa,SAAU,aAAa,UAAU;AAClD,aAAO,GAAG,WAAW,IAAI,QAAQ,iFAAiF,WAAW;AAAA,IACjI;AAOA,YAAQ,eAAe,SAAU,KAAK;AAElC,UAAI,OAAO,YAAY,YAAY,QAAQ,aAAa;AAEpD,gBAAQ,YAAY,GAAG;AAAA,MAC3B,WAAW,QAAQ,MAAM;AACrB,gBAAQ,KAAK,GAAG;AAAA,MACpB,OAAO;AACH,gBAAQ,IAAI,GAAG;AAAA,MACnB;AAAA,IACJ;AAAA,EACD,GAAG,UAAU;AACb,SAAO;AACR;AAEA,IAAI;AACJ,IAAI;AAEJ,SAAS,eAAgB;AACxB,MAAI,iBAAkB,QAAO;AAC7B,qBAAmB;AASnB,UAAQ,SAASA,OAAM,KAAKC,KAAI;AAC5B,QAAI,OAAO;AAEX,QAAI;AAEA,UAAI,QAAQ,WAAY;AACpB,YAAI,CAACA,IAAG,MAAM,MAAM,SAAS,GAAG;AAE5B,gBAAM,IAAI,MAAM;AAAA,QACpB;AAAA,MACJ,CAAC;AAAA,IACL,SAAS,GAAG;AACR,aAAO;AAAA,IACX;AAEA,WAAO;AAAA,EACX;AACA,SAAO;AACR;AAEA,IAAI;AACJ,IAAI;AAEJ,SAAS,sBAAuB;AAC/B,MAAI,wBAAyB,QAAO;AACpC,4BAA0B;AAO1B,iBAAe,SAASC,cAAa,MAAM;AACvC,QAAI,CAAC,MAAM;AACP,aAAO;AAAA,IACX;AAEA,QAAI;AACA,aACI,KAAK,eACL,KAAK;AAAA;AAAA;AAAA;AAAA,OAKJ,OAAO,IAAI,EAAE,MAAM,oBAAoB,KAAK,CAAC,GAAG,CAAC;AAAA,IAE1D,SAAS,GAAG;AAGR,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACR;AAEA,IAAI;AACJ,IAAI;AAEJ,SAAS,0BAA2B;AACnC,MAAI,4BAA6B,QAAO;AACxC,gCAA8B;AAE9B,MAAI,OAAO,aAAa,EAAE;AAC1B,MAAI,QAAQ,aAAa,EAAE;AAK3B,WAAS,WAAW,GAAG,GAAG;AAEtB,QAAI,QAAQ,EAAE,QAAQ,CAAC;AACvB,QAAI,QAAQ,EAAE,QAAQ,CAAC;AACvB,QAAI,MAAO,SAAS,MAAM,UAAW;AACrC,QAAI,MAAO,SAAS,MAAM,UAAW;AAErC,WAAO,MAAM,MAAM,KAAK;AAAA,EAC5B;AAaA,WAAS,iBAAiB,OAAO;AAC7B,WAAO,KAAK,MAAM,KAAK,GAAG,UAAU;AAAA,EACxC;AAEA,uBAAqB;AACrB,SAAO;AACR;AAEA,IAAI;AACJ,IAAI;AAEJ,SAAS,mBAAoB;AAC5B,MAAI,qBAAsB,QAAO;AACjC,yBAAuB;AAEvB,MAAI,gBAAgB,4BAA4B;AAEhD,cAAY,cAAc,SAAS,SAAS;AAC5C,SAAO;AACR;AAEA,IAAI;AACJ,IAAI;AAEJ,SAAS,aAAc;AACtB,MAAI,eAAgB,QAAO;AAC3B,mBAAiB;AAEjB,MAAI,gBAAgB,4BAA4B;AAEhD,QAAM,cAAc,IAAI,SAAS;AACjC,SAAO;AACR;AAEA,IAAI;AACJ,IAAI;AAEJ,SAAS,gBAAiB;AACzB,MAAI,kBAAmB,QAAO;AAC9B,sBAAoB;AAEpB,MAAI,gBAAgB,4BAA4B;AAEhD,WAAS,cAAc,OAAO,SAAS;AACvC,SAAO;AACR;AAEA,IAAI;AACJ,IAAI;AAEJ,SAAS,aAAc;AACtB,MAAI,eAAgB,QAAO;AAC3B,mBAAiB;AAEjB,MAAI,gBAAgB,4BAA4B;AAEhD,QAAM,cAAc,IAAI,SAAS;AACjC,SAAO;AACR;AAEA,IAAI;AACJ,IAAI;AAEJ,SAAS,gBAAiB;AACzB,MAAI,kBAAmB,QAAO;AAC9B,sBAAoB;AAEpB,MAAI,gBAAgB,4BAA4B;AAEhD,WAAS,cAAc,OAAO,SAAS;AACvC,SAAO;AACR;AAEA,IAAI;AACJ,IAAI;AAEJ,SAAS,oBAAqB;AAC7B,MAAI,sBAAuB,QAAO;AAClC,0BAAwB;AAExB,eAAa;AAAA,IACT,OAAO,aAAa;AAAA,IACpB,UAAU,iBAAiB;AAAA,IAC3B,KAAK,WAAW;AAAA,IAChB,QAAQ,cAAc;AAAA,IACtB,KAAK,WAAW;AAAA,IAChB,QAAQ,cAAc;AAAA,EAC1B;AACA,SAAO;AACR;AAEA,IAAI,eAAe,EAAC,SAAS,CAAC,EAAC;AAE/B,IAAI,aAAa,aAAa;AAE9B,IAAI;AAEJ,SAAS,oBAAqB;AAC7B,MAAI,sBAAuB,QAAO,aAAa;AAC/C,0BAAwB;AACxB,GAAC,SAAU,QAAQ,SAAS;AAC3B,KAAC,SAAUL,SAAQ,SAAS;AAC3B,aAAO,UAAU,QAAQ;AAAA,IAC1B,GAAE,aAAa,WAAY;AAM3B,UAAI,gBAAgB,OAAO,YAAY;AAGvC,UAAI,eAAe,OAAO,SAAS,WAAW,OAAO;AAErD,UAAI,eAAe,OAAO,WAAW;AACrC,UAAI,YAAY,OAAO,QAAQ;AAC/B,UAAI,YAAY,OAAO,QAAQ;AAC/B,UAAI,gBAAgB,OAAO,YAAY;AACvC,UAAI,gBAAgB,OAAO,YAAY;AACvC,UAAI,iBAAiB,OAAO,aAAa;AACzC,UAAI,uBAAuB,gBAAgB,OAAO,OAAO,aAAa;AACtE,UAAI,0BAA0B,gBAAgB,OAAO,OAAO,gBAAgB;AAC5E,UAAI,mBAAmB,aAAa,OAAO,IAAI,UAAU,YAAY;AACrE,UAAI,mBAAmB,aAAa,OAAO,IAAI,UAAU,YAAY;AACrE,UAAI,uBAAuB,oBAAoB,OAAO,gBAAe,oBAAI,IAAI,GAAE,QAAQ,CAAC;AACxF,UAAI,uBAAuB,oBAAoB,OAAO,gBAAe,oBAAI,IAAI,GAAE,QAAQ,CAAC;AACxF,UAAI,sBAAsB,wBAAwB,OAAO,MAAM,UAAU,OAAO,QAAQ,MAAM;AAC9F,UAAI,yBAAyB,uBAAuB,OAAO,eAAe,CAAC,EAAE,OAAO,QAAQ,EAAE,CAAC;AAC/F,UAAI,uBAAuB,wBAAwB,OAAO,OAAO,UAAU,OAAO,QAAQ,MAAM;AAChG,UAAI,0BAA0B,wBAAwB,OAAO,eAAe,GAAG,OAAO,QAAQ,EAAE,CAAC;AACjG,UAAI,0BAA0B;AAC9B,UAAI,2BAA2B;AAW/B,eAASM,YAAW,KAAK;AAevB,YAAI,YAAY,OAAO;AACvB,YAAI,cAAc,UAAU;AAC1B,iBAAO;AAAA,QACT;AAQA,YAAI,QAAQ,MAAM;AAChB,iBAAO;AAAA,QACT;AAkBA,YAAI,QAAQ,cAAc;AACxB,iBAAO;AAAA,QACT;AAQA,YACE,MAAM,QAAQ,GAAG,MAChB,4BAA4B,SAAS,EAAE,OAAO,eAAe,OAC9D;AACA,iBAAO;AAAA,QACT;AAIA,YAAI,OAAO,WAAW,YAAY,WAAW,MAAM;AAQjD,cAAI,OAAO,OAAO,aAAa,YAAY,QAAQ,OAAO,UAAU;AAClE,mBAAO;AAAA,UACT;AAqBA,cAAI,OAAO,OAAO,aAAa,YAAY,QAAQ,OAAO,UAAU;AAClE,mBAAO;AAAA,UACT;AAEA,cAAI,OAAO,OAAO,cAAc,UAAU;AAOxC,gBAAI,OAAO,OAAO,UAAU,cAAc,YACtC,QAAQ,OAAO,UAAU,WAAW;AACtC,qBAAO;AAAA,YACT;AAQA,gBAAI,OAAO,OAAO,UAAU,YAAY,YACpC,QAAQ,OAAO,UAAU,SAAS;AACpC,qBAAO;AAAA,YACT;AAAA,UACF;AAEA,eAAK,OAAO,OAAO,gBAAgB,cAC/B,OAAO,OAAO,gBAAgB,aAC9B,eAAe,OAAO,aAAa;AAOrC,gBAAI,IAAI,YAAY,cAAc;AAChC,qBAAO;AAAA,YACT;AAcA,gBAAI,IAAI,YAAY,MAAM;AACxB,qBAAO;AAAA,YACT;AAcA,gBAAI,IAAI,YAAY,MAAM;AACxB,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAwBA,YAAI,YAAa,2BAA2B,IAAI,OAAO,WAAW;AAClE,YAAI,OAAO,cAAc,UAAU;AACjC,iBAAO;AAAA,QACT;AAEA,YAAI,eAAe,OAAO,eAAe,GAAG;AAS5C,YAAI,iBAAiB,OAAO,WAAW;AACrC,iBAAO;AAAA,QACT;AAQA,YAAI,iBAAiB,KAAK,WAAW;AACnC,iBAAO;AAAA,QACT;AAWA,YAAI,iBAAiB,iBAAiB,QAAQ,WAAW;AACvD,iBAAO;AAAA,QACT;AAQA,YAAI,aAAa,iBAAiB,IAAI,WAAW;AAC/C,iBAAO;AAAA,QACT;AAQA,YAAI,aAAa,iBAAiB,IAAI,WAAW;AAC/C,iBAAO;AAAA,QACT;AAQA,YAAI,iBAAiB,iBAAiB,QAAQ,WAAW;AACvD,iBAAO;AAAA,QACT;AAQA,YAAI,iBAAiB,iBAAiB,QAAQ,WAAW;AACvD,iBAAO;AAAA,QACT;AAQA,YAAI,kBAAkB,iBAAiB,SAAS,WAAW;AACzD,iBAAO;AAAA,QACT;AAQA,YAAI,aAAa,iBAAiB,sBAAsB;AACtD,iBAAO;AAAA,QACT;AAQA,YAAI,aAAa,iBAAiB,sBAAsB;AACtD,iBAAO;AAAA,QACT;AAQA,YAAI,uBAAuB,iBAAiB,wBAAwB;AAClE,iBAAO;AAAA,QACT;AAQA,YAAI,wBAAwB,iBAAiB,yBAAyB;AACpE,iBAAO;AAAA,QACT;AAQA,YAAI,iBAAiB,MAAM;AACzB,iBAAO;AAAA,QACT;AAEA,eAAO,OACJ,UACA,SACA,KAAK,GAAG,EACR,MAAM,yBAAyB,wBAAwB;AAAA,MAC5D;AAEA,aAAOA;AAAA,IAEP,EAAE;AAAA,EACH,GAAG,YAAY;AACf,SAAO,aAAa;AACrB;AAEA,IAAI;AACJ,IAAI;AAEJ,SAAS,gBAAiB;AACzB,MAAI,kBAAmB,QAAO;AAC9B,sBAAoB;AAEpB,MAAI,OAAO,kBAAkB;AAO7B,WAAS,SAASC,QAAO,OAAO;AAC5B,WAAO,KAAK,KAAK,EAAE,YAAY;AAAA,EACnC;AACA,SAAO;AACR;AAEA,IAAI;AACJ,IAAI;AAEJ,SAAS,uBAAwB;AAChC,MAAI,yBAA0B,QAAO;AACrC,6BAA2B;AAO3B,WAAS,cAAc,OAAO;AAC1B,QAAI,SAAS,MAAM,UAAU;AAEzB,aAAO,MAAM,SAAS;AAAA,IAC1B;AACA,WAAO,OAAO,KAAK;AAAA,EACvB;AAEA,oBAAkB;AAClB,SAAO;AACR;AAEA,IAAI;AACJ,IAAI;AAEJ,SAAS,aAAc;AACtB,MAAI,eAAgB,QAAO;AAC3B,mBAAiB;AAEjB,QAAM;AAAA,IACF,QAAQ,cAAc;AAAA,IACtB,eAAe,qBAAqB;AAAA,IACpC,WAAW,iBAAiB;AAAA,IAC5B,YAAY,kBAAkB;AAAA,IAC9B,OAAO,aAAa;AAAA,IACpB,cAAc,oBAAoB;AAAA,IAClC,kBAAkB,wBAAwB;AAAA,IAC1C,YAAY,kBAAkB;AAAA,IAC9B,QAAQ,cAAc;AAAA,IACtB,eAAe,qBAAqB;AAAA,EACxC;AACA,SAAO;AACR;AAEA,IAAI;AAEJ,SAAS,uBAAwB;AAChC,MAAI,yBAA0B,QAAO;AACrC,6BAA2B;AAE3B,QAAM,eAAe,WAAW,EAAE;AAClC,MAAI,cAAc;AAClB,MAAI,OAAO,wBAAwB,aAAa;AAC5C,QAAI;AACA,qBAAe,oBAAoB;AAAA,IACvC,SAAS,GAAG;AAAA,IAEZ;AACA,QAAI;AACA,6BAAuB,oBAAoB;AAAA,IAC/C,SAAS,GAAG;AAAA,IAEZ;AAAA,EACJ;AAoIA,WAAS,WAAW,SAAS;AACzB,UAAM,aAAa,KAAK,IAAI,GAAG,EAAE,IAAI;AACrC,UAAM,iBAAiB;AACvB,UAAM,OAAO,WAAY;AACrB,aAAO;AAAA,IACX;AACA,UAAM,aAAa,WAAY;AAC3B,aAAO,CAAC;AAAA,IACZ;AACA,UAAM,YAAY,CAAC;AACnB,QAAI,eACA,wBAAwB;AAE5B,QAAI,QAAQ,YAAY;AACpB,gBAAU,aAAa;AACvB,sBAAgB,QAAQ,WAAW,MAAM,CAAC;AAC1C,8BAAwB,OAAO,kBAAkB;AAAA,IACrD;AACA,cAAU,eAAe,QAAQ,QAAQ,YAAY;AACrD,cAAU,cAAc,QAAQ,QAAQ,WAAW;AACnD,cAAU,gBAAgB,QAAQ,QAAQ,aAAa;AACvD,cAAU,SACN,QAAQ,WAAW,OAAO,QAAQ,QAAQ,WAAW;AACzD,cAAU,eACN,UAAU,UAAU,OAAO,QAAQ,QAAQ,OAAO,WAAW;AACjE,cAAU,WACN,QAAQ,WAAW,OAAO,QAAQ,QAAQ,aAAa;AAC3D,UAAM,gBAAgB,QAAQ,WAAW,QAAQ,uBAAuB,QAAQ,oBAAoB,KAAK;AACzG,cAAU,cACN,QAAQ,eAAe,OAAO,QAAQ,YAAY,QAAQ;AAC9D,UAAM,0BACF,QAAQ,gBACP,OAAO,QAAQ,aAAa,MAAM,qBAAqB;AAC5D,UAAM,qCACF,QAAQ,eACR,QAAQ,YAAY,eACpB,QAAQ,YAAY,YAAY;AACpC,cAAU,iBAAiB,QAAQ,eAAe,gBAAgB;AAClE,cAAU,wBACN,QAAQ,yBACR,OAAO,QAAQ,0BAA0B;AAC7C,cAAU,uBACN,QAAQ,wBACR,OAAO,QAAQ,yBAAyB;AAC5C,cAAU,sBACN,QAAQ,uBACR,OAAO,QAAQ,wBAAwB;AAC3C,cAAU,4BACN,QAAQ,sBACR,OAAO,QAAQ,uBAAuB;AAC1C,cAAU,eACN,QAAQ,gBAAgB,OAAO,QAAQ,iBAAiB;AAC5D,cAAU,iBACN,QAAQ,kBAAkB,OAAO,QAAQ,mBAAmB;AAChE,cAAU,OAAO,QAAQ,QAAQ,OAAO,QAAQ,SAAS;AAEzD,QAAI,QAAQ,cAAc;AACtB,cAAQ,aAAa,aAAa;AAAA,IACtC;AAEA,UAAM,aAAa,QAAQ;AAC3B,UAAM,aAAa,UAAU,OACvB,OAAO;AAAA,MACH,uBAAO,OAAO,IAAI;AAAA,MAClB,OAAO,0BAA0B,QAAQ,IAAI;AAAA,IACjD,IACA;AACN,QAAI,gBAAgB;AAEpB,QAAI,eAAe,QAAW;AAC1B,YAAM,IAAI;AAAA,QACN;AAAA,MAEJ;AAAA,IACJ;AACA,cAAU,OAAO;AAAA,IAQjB,MAAM,qBAAqB;AAAA,MACvB,YAAY,MAAM,WAAW,WAAW,UAAU;AAC9C,aAAK,OAAO;AACZ,aAAK,YAAY;AACjB,aAAK,YAAY;AACjB,aAAK,WAAW;AAAA,MACpB;AAAA,MAEA,SAAS;AACL,eAAO,KAAK,UAAU,EAAE,GAAG,KAAK,CAAC;AAAA,MACrC;AAAA,IACJ;AAMA,aAAS,eAAe,KAAK;AACzB,UAAI,OAAO,UAAU;AACjB,eAAO,OAAO,SAAS,GAAG;AAAA,MAC9B;AAEA,aAAO,SAAS,GAAG;AAAA,IACvB;AAEA,QAAI,sBAAsB;AAM1B,aAAS,yBAAyB,OAAO,GAAG;AACxC,UAAI,MAAM,aAAa,MAAM,MAAM,YAAY,GAAG;AAC9C,8BAAsB;AAAA,MAC1B;AAAA,IACJ;AAKA,aAAS,2BAA2B;AAChC,4BAAsB;AAAA,IAC1B;AASA,aAAS,UAAU,KAAK;AACpB,UAAI,CAAC,KAAK;AACN,eAAO;AAAA,MACX;AAEA,YAAM,UAAU,IAAI,MAAM,GAAG;AAC7B,YAAM,IAAI,QAAQ;AAClB,UAAI,IAAI;AACR,UAAI,KAAK;AACT,UAAI;AAEJ,UAAI,IAAI,KAAK,CAAC,sBAAsB,KAAK,GAAG,GAAG;AAC3C,cAAM,IAAI;AAAA,UACN;AAAA,QACJ;AAAA,MACJ;AAEA,aAAO,KAAK;AACR,iBAAS,SAAS,QAAQ,CAAC,GAAG,EAAE;AAEhC,YAAI,UAAU,IAAI;AACd,gBAAM,IAAI,MAAM,gBAAgB,GAAG,EAAE;AAAA,QACzC;AAEA,cAAM,SAAS,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC;AAAA,MACzC;AAEA,aAAO,KAAK;AAAA,IAChB;AASA,aAAS,cAAc,SAAS;AAC5B,YAAM,SAAS;AACf,YAAM,YAAa,UAAU,MAAO;AACpC,YAAM,oBACF,YAAY,IAAI,YAAY,SAAS;AAEzC,aAAO,KAAK,MAAM,iBAAiB;AAAA,IACvC;AAOA,aAAS,SAAS,OAAO;AACrB,UAAI,CAAC,OAAO;AACR,eAAO;AAAA,MACX;AACA,UAAI,OAAO,MAAM,YAAY,YAAY;AACrC,eAAO,MAAM,QAAQ;AAAA,MACzB;AACA,UAAI,OAAO,UAAU,UAAU;AAC3B,eAAO;AAAA,MACX;AACA,YAAM,IAAI,UAAU,6CAA6C;AAAA,IACrE;AAQA,aAAS,QAAQ,MAAM,IAAI,OAAO;AAC9B,aAAO,SAAS,MAAM,UAAU,QAAQ,MAAM,UAAU;AAAA,IAC5D;AAMA,aAAS,qBAAqB,OAAO,KAAK;AACtC,YAAM,oBAAoB,IAAI;AAAA,QAC1B,0BAA0B,MAAM,SAAS;AAAA,MAC7C;AAEA,UAAI,CAAC,IAAI,OAAO;AACZ,eAAO;AAAA,MACX;AAGA,YAAM,wBAAwB;AAC9B,UAAI,qBAAqB,IAAI;AAAA,QACzB,OAAO,OAAO,KAAK,KAAK,EAAE,KAAK,GAAG,CAAC;AAAA,MACvC;AAEA,UAAI,uBAAuB;AAEvB,6BAAqB,IAAI;AAAA,UACrB,yBAAyB,OAAO,KAAK,KAAK,EAAE,KAAK,GAAG,CAAC;AAAA,QACzD;AAAA,MACJ;AAEA,UAAI,mBAAmB;AACvB,UAAI,MAAM,MAAM,MAAM,IAAI,EAAE,KAAK,SAAU,MAAM,GAAG;AAGhD,cAAM,wBAAwB,KAAK,MAAM,qBAAqB;AAE9D,YAAI,uBAAuB;AACvB,6BAAmB;AACnB,iBAAO;AAAA,QACX;AAIA,cAAM,qBAAqB,KAAK,MAAM,kBAAkB;AACxD,YAAI,oBAAoB;AACpB,6BAAmB;AACnB,iBAAO;AAAA,QACX;AAKA,eAAO,oBAAoB;AAAA,MAC/B,CAAC;AAED,YAAM,QAAQ,GAAG,iBAAiB;AAAA,EAAK,IAAI,QAAQ,WAAW,MAC1D,IAAI,KAAK,QAAQ,WACrB;AAAA,EAAK,IAAI,MAAM,MACV,MAAM,IAAI,EACV,MAAM,mBAAmB,CAAC,EAC1B,KAAK,IAAI,CAAC;AAEf,UAAI;AACA,eAAO,eAAe,mBAAmB,SAAS;AAAA,UAC9C,OAAO;AAAA,QACX,CAAC;AAAA,MACL,SAAS,GAAG;AAAA,MAEZ;AAEA,aAAO;AAAA,IACX;AAGA,aAAS,aAAa;AAAA,MAClB,MAAM,kBAAkB,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAY/B,YAAY,MAAM,OAAO,MAAM,MAAM,QAAQ,QAAQ,IAAI;AAGrD,cAAI,UAAU,WAAW,GAAG;AACxB,kBAAM,UAAU,MAAM,GAAG;AAAA,UAC7B,OAAO;AACH,kBAAM,GAAG,SAAS;AAAA,UACtB;AAIA,iBAAO,eAAe,MAAM,eAAe;AAAA,YACvC,OAAO;AAAA,YACP,YAAY;AAAA,UAChB,CAAC;AAAA,QACL;AAAA,QAEA,QAAQ,OAAO,WAAW,EAAE,UAAU;AAClC,iBAAO,oBAAoB;AAAA,QAC/B;AAAA,MACJ;AAEA,gBAAU,SAAS;AAEnB,UAAI,WAAW,KAAK;AAChB,kBAAU,MAAM,SAASC,OAAM;AAC3B,iBAAO,UAAU,MAAM;AAAA,QAC3B;AAAA,MACJ;AAEA,UAAI,WAAW,UAAU;AACrB,kBAAU,WAAW,SAAS,WAAW;AACrC,iBAAO,WAAW,SAAS;AAAA,QAC/B;AAAA,MACJ;AAEA,gBAAU,WAAW,SAAS,WAAW;AACrC,eAAO,WAAW,SAAS;AAAA,MAC/B;AAQA,YAAM,iBAAiB,IAAI,MAAM,WAAW;AAAA;AAAA,QAExC,QAAQ;AAGJ,cAAI,gBAAgB,WAAW;AAC3B,kBAAM,IAAI;AAAA,cACN;AAAA,YACJ;AAAA,UACJ;AAEA,iBAAO,IAAI,WAAW,UAAU,MAAM,GAAG,EAAE,SAAS;AAAA,QACxD;AAAA,MACJ,CAAC;AAED,aAAO;AAAA,IACX;AAUA,aAAS,aAAa;AAClB,YAAM,YAAY,CAAC;AAKnB,aAAO,oBAAoB,UAAU,EAAE;AAAA,QACnC,CAAC,aAAc,UAAU,QAAQ,IAAI,WAAW,QAAQ;AAAA,MAC5D;AAEA,gBAAU,iBAAiB,YAAa,MAAM;AAC1C,cAAM,gBAAgB,IAAI,WAAW,eAAe,GAAG,IAAI;AAC3D,cAAM,YAAY,CAAC;AAEnB,SAAC,eAAe,sBAAsB,iBAAiB,EAAE;AAAA,UACrD,CAAC,WAAW;AACR,sBAAU,MAAM,IACZ,cAAc,MAAM,EAAE,KAAK,aAAa;AAAA,UAChD;AAAA,QACJ;AAEA,SAAC,UAAU,eAAe,EAAE,QAAQ,CAAC,WAAW;AAC5C,oBAAU,MAAM,IAAI,SAAU,MAAM;AAChC,mBAAO,cAAc,MAAM,EAAE,QAAQ,UAAU,MAAM,GAAG;AAAA,UAC5D;AAAA,QACJ,CAAC;AAED,eAAO;AAAA,MACX;AAEA,gBAAU,eAAe,YAAY,OAAO;AAAA,QACxC,WAAW,eAAe;AAAA,MAC9B;AAEA,gBAAU,eAAe,qBACrB,WAAW,eAAe;AAE9B,aAAO;AAAA,IACX;AAGA,aAAS,WAAW,OAAO,KAAK;AAE5B,UAAI,CAAC,MAAM,MAAM;AACb,cAAM,OAAO,CAAC;AAAA,MAClB;AACA,YAAM,KAAK,KAAK,GAAG;AAAA,IACvB;AAGA,aAAS,QAAQ,OAAO;AAEpB,UAAI,CAAC,MAAM,MAAM;AACb;AAAA,MACJ;AACA,eAAS,IAAI,GAAG,IAAI,MAAM,KAAK,QAAQ,KAAK;AACxC,cAAM,MAAM,MAAM,KAAK,CAAC;AACxB,YAAI,KAAK,MAAM,MAAM,IAAI,IAAI;AAE7B,iCAAyB,OAAO,CAAC;AACjC,YAAI,MAAM,aAAa,IAAI,MAAM,WAAW;AACxC,gBAAM,qBAAqB,OAAO,GAAG;AAAA,QACzC;AAAA,MACJ;AACA,+BAAyB;AACzB,YAAM,OAAO,CAAC;AAAA,IAClB;AAOA,aAAS,SAAS,OAAO,OAAO;AAC5B,UAAI,MAAM,SAAS,QAAW;AAC1B,cAAM,IAAI,MAAM,0CAA0C;AAAA,MAC9D;AAEA,UAAI,uBAAuB;AAEvB,YAAI,OAAO,MAAM,SAAS,YAAY;AAClC,gBAAM,IAAI;AAAA,YACN,iEACI,MAAM,IACV,YAAY,OAAO,MAAM,IAAI;AAAA,UACjC;AAAA,QACJ;AAAA,MACJ;AAEA,UAAI,qBAAqB;AACrB,cAAM,QAAQ,IAAI,MAAM;AAAA,MAC5B;AAEA,YAAM,OAAO,MAAM,YAAY,cAAc;AAE7C,UAAI,MAAM,eAAe,OAAO,GAAG;AAC/B,YAAI,OAAO,MAAM,UAAU,UAAU;AACjC,gBAAM,QAAQ,SAAS,MAAM,OAAO,EAAE;AAAA,QAC1C;AAEA,YAAI,CAAC,eAAe,MAAM,KAAK,GAAG;AAC9B,gBAAM,QAAQ;AAAA,QAClB;AACA,cAAM,QAAQ,MAAM,QAAQ,aAAa,IAAI,MAAM;AACnD,cAAM,QAAQ,KAAK,IAAI,GAAG,MAAM,KAAK;AAAA,MACzC;AAEA,UAAI,MAAM,eAAe,UAAU,GAAG;AAClC,cAAM,OAAO;AACb,cAAM,WAAW,MAAM,WAAW,aAAa,IAAI,MAAM;AAAA,MAC7D;AAEA,UAAI,MAAM,eAAe,WAAW,GAAG;AACnC,cAAM,OAAO;AACb,cAAM,YAAY;AAAA,MACtB;AAEA,UAAI,MAAM,eAAe,cAAc,GAAG;AACtC,cAAM,OAAO;AACb,cAAM,eAAe;AAAA,MACzB;AAEA,UAAI,CAAC,MAAM,QAAQ;AACf,cAAM,SAAS,CAAC;AAAA,MACpB;AAEA,YAAM,KAAK;AACX,YAAM,YAAY,MAAM;AACxB,YAAM,SACF,MAAM,OAAO,SAAS,MAAM,KAAK,MAAM,MAAM,aAAa,IAAI;AAElE,YAAM,OAAO,MAAM,EAAE,IAAI;AAEzB,UAAI,uBAAuB;AACvB,cAAM,MAAM;AAAA,UACR,OAAO;AAAA,UACP,KAAK,WAAY;AACb,iBAAK,QAAQ;AACb,mBAAO;AAAA,UACX;AAAA,UACA,OAAO,WAAY;AACf,iBAAK,QAAQ;AACb,mBAAO;AAAA,UACX;AAAA,UACA,QAAQ,WAAY;AAChB,mBAAO,KAAK;AAAA,UAChB;AAAA,UACA,SAAS,WAAY;AACjB,kBAAM,SACF,MAAM,OACL,SAAS,MAAM,KAAK,MAAM,MAAM,aAAa,IAAI;AAGtD,kBAAM,OAAO,MAAM,EAAE,IAAI;AAEzB,mBAAO;AAAA,UACX;AAAA,UACA,CAAC,OAAO,WAAW,GAAG,WAAY;AAC9B,mBAAO,MAAM;AAAA,UACjB;AAAA,QACJ;AACA,eAAO;AAAA,MACX;AAEA,aAAO,MAAM;AAAA,IACjB;AASA,aAAS,cAAc,GAAG,GAAG;AAEzB,UAAI,EAAE,SAAS,EAAE,QAAQ;AACrB,eAAO;AAAA,MACX;AACA,UAAI,EAAE,SAAS,EAAE,QAAQ;AACrB,eAAO;AAAA,MACX;AAGA,UAAI,EAAE,aAAa,CAAC,EAAE,WAAW;AAC7B,eAAO;AAAA,MACX;AACA,UAAI,CAAC,EAAE,aAAa,EAAE,WAAW;AAC7B,eAAO;AAAA,MACX;AAGA,UAAI,EAAE,YAAY,EAAE,WAAW;AAC3B,eAAO;AAAA,MACX;AACA,UAAI,EAAE,YAAY,EAAE,WAAW;AAC3B,eAAO;AAAA,MACX;AAGA,UAAI,EAAE,KAAK,EAAE,IAAI;AACb,eAAO;AAAA,MACX;AACA,UAAI,EAAE,KAAK,EAAE,IAAI;AACb,eAAO;AAAA,MACX;AAAA,IAGJ;AAQA,aAAS,kBAAkB,OAAO,MAAM,IAAI;AACxC,YAAMC,UAAS,MAAM;AACrB,UAAI,QAAQ;AACZ,UAAI,IAAI;AAER,WAAK,MAAMA,SAAQ;AACf,YAAIA,QAAO,eAAe,EAAE,GAAG;AAC3B,sBAAY,QAAQ,MAAM,IAAIA,QAAO,EAAE,CAAC;AAExC,cACI,cACC,CAAC,SAAS,cAAc,OAAOA,QAAO,EAAE,CAAC,MAAM,IAClD;AACE,oBAAQA,QAAO,EAAE;AAAA,UACrB;AAAA,QACJ;AAAA,MACJ;AAEA,aAAO;AAAA,IACX;AAMA,aAAS,WAAW,OAAO;AACvB,YAAMA,UAAS,MAAM;AACrB,UAAI,QAAQ;AACZ,UAAI;AAEJ,WAAK,MAAMA,SAAQ;AACf,YAAIA,QAAO,eAAe,EAAE,GAAG;AAC3B,cAAI,CAAC,SAAS,cAAc,OAAOA,QAAO,EAAE,CAAC,MAAM,GAAG;AAClD,oBAAQA,QAAO,EAAE;AAAA,UACrB;AAAA,QACJ;AAAA,MACJ;AAEA,aAAO;AAAA,IACX;AAMA,aAAS,UAAU,OAAO;AACtB,YAAMA,UAAS,MAAM;AACrB,UAAI,QAAQ;AACZ,UAAI;AAEJ,WAAK,MAAMA,SAAQ;AACf,YAAIA,QAAO,eAAe,EAAE,GAAG;AAC3B,cAAI,CAAC,SAAS,cAAc,OAAOA,QAAO,EAAE,CAAC,MAAM,IAAI;AACnD,oBAAQA,QAAO,EAAE;AAAA,UACrB;AAAA,QACJ;AAAA,MACJ;AAEA,aAAO;AAAA,IACX;AAMA,aAAS,UAAU,OAAO,OAAO;AAC7B,UAAI,OAAO,MAAM,aAAa,UAAU;AACpC,cAAM,OAAO,MAAM,EAAE,EAAE,UAAU,MAAM;AAAA,MAC3C,OAAO;AACH,eAAO,MAAM,OAAO,MAAM,EAAE;AAAA,MAChC;AAEA,UAAI,OAAO,MAAM,SAAS,YAAY;AAClC,cAAM,KAAK,MAAM,MAAM,MAAM,IAAI;AAAA,MACrC,OAAO;AAEH,cAAM,QAAQ;AACd,SAAC,WAAY;AACT,gBAAM,MAAM,IAAI;AAAA,QACpB,GAAG;AAAA,MACP;AAAA,IACJ;AAMA,aAAS,gBAAgB,OAAO;AAC5B,UAAI,UAAU,kBAAkB,UAAU,kBAAkB;AACxD,eAAO,SAAS,KAAK;AAAA,MACzB;AACA,aAAO,QAAQ,KAAK;AAAA,IACxB;AAMA,aAAS,mBAAmB,OAAO;AAC/B,UAAI,UAAU,kBAAkB,UAAU,kBAAkB;AACxD,eAAO,UAAU,KAAK;AAAA,MAC1B;AACA,aAAO,MAAM,KAAK;AAAA,IACtB;AAKA,aAAS,iBAAiB;AACtB,UAAI,QAAQ;AACZ,aAAO,SAAU,KAAK;AAElB,SAAC,WAAW,QAAQ,KAAK,GAAG;AAAA,MAChC;AAAA,IACJ;AACA,UAAM,WAAW,eAAe;AAOhC,aAAS,WAAW,OAAO,SAAS,OAAO;AACvC,UAAI,CAAC,SAAS;AAGV;AAAA,MACJ;AAEA,UAAI,CAAC,MAAM,QAAQ;AACf,cAAM,SAAS,CAAC;AAAA,MACpB;AAIA,YAAM,KAAK,OAAO,OAAO;AAEzB,UAAI,OAAO,MAAM,EAAE,KAAK,KAAK,gBAAgB;AACzC,cAAM,cAAc,gBAAgB,KAAK;AAEzC,YAAI,MAAM,4BAA4B,MAAM;AACxC,gBAAM,gBAAgB,MAAM,IAAI,WAAW,EAAE;AAC7C,iBAAO,OAAO,kBAAkB,aAC1B,cAAc,OAAO,IACrB;AAAA,QACV;AACA;AAAA,UACI,eAAe,WAAW;AAAA;AAAA,QAE9B;AAAA,MACJ;AAEA,UAAI,MAAM,OAAO,eAAe,EAAE,GAAG;AAEjC,cAAM,QAAQ,MAAM,OAAO,EAAE;AAC7B,YACI,MAAM,SAAS,SACd,MAAM,SAAS,aAAa,UAAU,cACtC,MAAM,SAAS,cAAc,UAAU,WAC1C;AACE,iBAAO,MAAM,OAAO,EAAE;AAAA,QAC1B,OAAO;AACH,gBAAM,QAAQ,gBAAgB,KAAK;AACnC,gBAAM,WAAW,mBAAmB,MAAM,IAAI;AAC9C,gBAAM,IAAI;AAAA,YACN,0CAA0C,QAAQ,uBAAuB,KAAK;AAAA,UAClF;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAOA,aAAS,UAAU,OAAO,QAAQ;AAC9B,UAAI,QAAQ,GAAG;AACf,YAAM,kBAAkB;AACxB,YAAM,oBAAoB;AAE1B,WAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,QAAQ,IAAI,GAAG,KAAK;AAC9C,iBAAS,MAAM,QAAQ,CAAC;AACxB,YAAI,WAAW,YAAY,QAAQ,SAAS;AACxC,kBAAQ,QAAQ,SAAS,MAAM,eAAe;AAAA,QAClD,WAAW,WAAW,cAAc,QAAQ,SAAS;AACjD,kBAAQ,QAAQ,WAAW,MAAM,iBAAiB;AAAA,QACtD,WAAW,WAAW,eAAe;AACjC,gBAAM,yBAAyB,OAAO;AAAA,YAClC;AAAA,YACA,IAAI,MAAM;AAAA,UACd;AACA,cACI,0BACA,uBAAuB,OACvB,CAAC,uBAAuB,KAC1B;AACE,mBAAO;AAAA,cACH;AAAA,cACA;AAAA,cACA;AAAA,YACJ;AAAA,UACJ,WAAW,uBAAuB,cAAc;AAC5C,oBAAQ,MAAM,IAAI,MAAM,IAAI,MAAM,EAAE;AAAA,UACxC;AAAA,QACJ,OAAO;AACH,cAAI,QAAQ,MAAM,KAAK,QAAQ,MAAM,EAAE,gBAAgB;AACnD,oBAAQ,MAAM,IAAI,MAAM,IAAI,MAAM,EAAE;AAAA,UACxC,OAAO;AACH,gBAAI;AACA,qBAAO,QAAQ,MAAM;AAAA,YACzB,SAAS,QAAQ;AAAA,YAEjB;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,MAAM,wBAAwB,QAAW;AACzC,mBAAS,IAAI,GAAG,IAAI,MAAM,oBAAoB,QAAQ,KAAK;AACvD,kBAAM,QAAQ,MAAM,oBAAoB,CAAC;AACzC,yBAAa,MAAM,UAAU,IAAI,MAAM;AAAA,UAC3C;AAAA,QACJ;AACA,YAAI,MAAM,gCAAgC,QAAW;AACjD,mBACQ,IAAI,GACR,IAAI,MAAM,4BAA4B,QACtC,KACF;AACE,kBAAM,QAAQ,MAAM,4BAA4B,CAAC;AACjD,iCAAqB,MAAM,UAAU,IAAI,MAAM;AAAA,UACnD;AAAA,QACJ;AAAA,MACJ;AAEA,UAAI,OAAO,sBAAsB,MAAM;AACnC,gBAAQ,cAAc,MAAM,gBAAgB;AAAA,MAChD;AAGA,YAAM,UAAU,CAAC;AAEjB,iBAAW,CAAC,UAAU,MAAM,KAAK,MAAM,iBAAiB,QAAQ,GAAG;AAC/D,eAAO,oBAAoB,SAAS,QAAQ;AAC5C,cAAM,iBAAiB,OAAO,QAAQ;AAAA,MAC1C;AAGA,UAAI,CAAC,MAAM,QAAQ;AACf,eAAO,CAAC;AAAA,MACZ;AACA,aAAO,OAAO,KAAK,MAAM,MAAM,EAAE,IAAI,SAAS,OAAO,KAAK;AACtD,eAAO,MAAM,OAAO,GAAG;AAAA,MAC3B,CAAC;AAAA,IACL;AAOA,aAAS,aAAa,QAAQ,QAAQ,OAAO;AACzC,YAAM,MAAM,EAAE,iBAAiB,OAAO,UAAU,eAAe;AAAA,QAC3D;AAAA,QACA;AAAA,MACJ;AACA,YAAM,IAAI,MAAM,EAAE,IAAI,OAAO,MAAM;AAEnC,UAAI,WAAW,QAAQ;AACnB,eAAO,MAAM,IAAI,MAAM,MAAM;AAAA,MACjC,WAAW,WAAW,QAAQ;AAC1B,eAAO,MAAM,IAAI,MAAM,MAAM;AAAA,MACjC,WAAW,WAAW,eAAe;AACjC,cAAM,yBAAyB,OAAO;AAAA,UAClC;AAAA,UACA;AAAA,QACJ;AAEA,YACI,0BACA,uBAAuB,OACvB,CAAC,uBAAuB,KAC1B;AACE,iBAAO;AAAA,YACH;AAAA,YACA,IAAI,MAAM;AAAA,YACV;AAAA,UACJ;AAEA,gBAAM,iBAAiB,OAAO;AAAA,YAC1B;AAAA,YACA;AAAA,UACJ;AACA,iBAAO,eAAe,QAAQ,QAAQ,cAAc;AAAA,QACxD,OAAO;AACH,iBAAO,MAAM,IAAI,MAAM,MAAM;AAAA,QACjC;AAAA,MACJ,OAAO;AACH,eAAO,MAAM,IAAI,WAAY;AACzB,iBAAO,MAAM,MAAM,EAAE,MAAM,OAAO,SAAS;AAAA,QAC/C;AAEA,eAAO;AAAA,UACH,OAAO,MAAM;AAAA,UACb,OAAO,0BAA0B,MAAM,MAAM,CAAC;AAAA,QAClD;AAAA,MACJ;AAEA,aAAO,MAAM,EAAE,QAAQ;AAAA,IAC3B;AAMA,aAAS,eAAe,OAAO,kBAAkB;AAC7C,YAAM,KAAK,gBAAgB;AAAA,IAC/B;AAuBA,UAAM,SAAS;AAAA,MACX,YAAY,QAAQ;AAAA,MACpB,cAAc,QAAQ;AAAA,MACtB,aAAa,QAAQ;AAAA,MACrB,eAAe,QAAQ;AAAA,MACvB,MAAM,QAAQ;AAAA,IAClB;AAEA,QAAI,UAAU,cAAc;AACxB,aAAO,eAAe,QAAQ;AAAA,IAClC;AAEA,QAAI,UAAU,gBAAgB;AAC1B,aAAO,iBAAiB,QAAQ;AAAA,IACpC;AAEA,QAAI,UAAU,QAAQ;AAClB,aAAO,SAAS,QAAQ,QAAQ;AAAA,IACpC;AAEA,QAAI,UAAU,UAAU;AACpB,aAAO,WAAW,QAAQ,QAAQ;AAAA,IACtC;AAEA,QAAI,UAAU,aAAa;AACvB,aAAO,cAAc,QAAQ;AAAA,IACjC;AAEA,QAAI,UAAU,uBAAuB;AACjC,aAAO,wBAAwB,QAAQ;AAAA,IAC3C;AAEA,QAAI,UAAU,gBAAgB;AAC1B,aAAO,iBAAiB,QAAQ;AAAA,IACpC;AAEA,QAAI,UAAU,sBAAsB;AAChC,aAAO,uBAAuB,QAAQ;AAAA,IAC1C;AAEA,QAAI,UAAU,qBAAqB;AAC/B,aAAO,sBAAsB,QAAQ;AAAA,IACzC;AAEA,QAAI,UAAU,oBAAoB;AAC9B,aAAO,qBAAqB,QAAQ;AAAA,IACxC;AAEA,QAAI,UAAU,MAAM;AAChB,aAAO,OAAO;AAAA,IAClB;AAEA,UAAM,qBAAqB,QAAQ,gBAAgB,QAAQ;AAO3D,aAAS,YAAY,OAAO,WAAW;AAEnC,cAAQ,KAAK,MAAM,SAAS,KAAK,CAAC;AAElC,kBAAY,aAAa;AACzB,UAAI,QAAQ;AACZ,YAAM,qBAAqB,CAAC,GAAG,CAAC;AAEhC,YAAM,QAAQ;AAAA,QACV,KAAK;AAAA,QACL,MAAM,WAAW;AAAA,QACjB;AAAA,MACJ;AAEA,YAAM,KAAK,QAAQ;AAGnB,eAAS,qBAAqB;AAC1B,eAAO,MAAO,MAAM,MAAM,SAAS;AAAA,MACvC;AAGA,eAAS,OAAO,MAAM;AAClB,cAAM,mBAAmB,MAAM,MAAM,mBAAmB,CAAC,IAAI;AAC7D,cAAM,iBAAiB,KAAK,MAAM,mBAAmB,GAAI;AACzD,cAAM,oBACD,mBAAmB,iBAAiB,OAAO,MAC5C,QACA,mBAAmB,CAAC;AAExB,YAAI,MAAM,QAAQ,IAAI,GAAG;AACrB,cAAI,KAAK,CAAC,IAAI,KAAK;AACf,kBAAM,IAAI;AAAA,cACN;AAAA,YACJ;AAAA,UACJ;AAEA,gBAAM,UAAU,KAAK,CAAC;AACtB,cAAI,WAAW,mBAAmB,KAAK,CAAC;AACxC,cAAI,UAAU,iBAAiB;AAE/B,cAAI,WAAW,GAAG;AACd,wBAAY;AACZ,uBAAW;AAAA,UACf;AAEA,iBAAO,CAAC,SAAS,QAAQ;AAAA,QAC7B;AACA,eAAO,CAAC,gBAAgB,gBAAgB;AAAA,MAC5C;AAWA,eAAS,qBAAqB;AAC1B,cAAM,MAAM,OAAO;AACnB,cAAM,SAAS,IAAI,CAAC,IAAI,MAAO,IAAI,CAAC,IAAI;AACxC,eAAO;AAAA,MACX;AAEA,UAAI,UAAU,cAAc;AACxB,eAAO,SAAS,WAAY;AACxB,gBAAM,QAAQ,OAAO;AACrB,iBAAO,OAAO,MAAM,CAAC,CAAC,IAAI,OAAO,GAAG,IAAI,OAAO,MAAM,CAAC,CAAC;AAAA,QAC3D;AAAA,MACJ;AAEA,UAAI,UAAU,MAAM;AAChB,cAAM,OAAO,WAAW;AACxB,cAAM,KAAK,QAAQ;AAAA,MACvB;AAEA,YAAM,sBAAsB,SAAS,oBACjC,MACA,SACF;AACE,YAAI,uBAAuB;AAE3B,YAAI,MAAM,YAAY,IAAI,GAAG;AACzB,iCAAuB;AAAA,QAC3B;AAEA,cAAM,SAAS,SAAS,OAAO;AAAA,UAC3B;AAAA,UACA,MAAM,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC;AAAA,UAC7C,OACI,OAAO,YAAY,cACb,uBACA,KAAK,IAAI,SAAS,oBAAoB;AAAA,UAChD,cAAc;AAAA,QAClB,CAAC;AAED,eAAO,OAAO,MAAM;AAAA,MACxB;AAEA,YAAM,qBAAqB,SAAS,mBAAmB,SAAS;AAC5D,eAAO,WAAW,OAAO,SAAS,cAAc;AAAA,MACpD;AAEA,YAAM,aAAa,SAAS,WAAW,MAAM,SAAS;AAClD,eAAO,SAAS,OAAO;AAAA,UACnB;AAAA,UACA,MAAM,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC;AAAA,UAC7C,OAAO;AAAA,QACX,CAAC;AAAA,MACL;AACA,UAAI,OAAO,QAAQ,YAAY,eAAe,eAAe;AACzD,cAAM,WAAW,cAAc,MAAM,IACjC,SAAS,sBAAsB,SAAS,KAAK;AACzC,iBAAO,IAAI,QAAQ,QAAQ,SAAS,mBAChC,SACF;AACE,qBAAS,OAAO;AAAA,cACZ,MAAM;AAAA,cACN,MAAM,CAAC,GAAG;AAAA,cACV,OAAO;AAAA,YACX,CAAC;AAAA,UACL,CAAC;AAAA,QACL;AAAA,MACR;AAEA,YAAM,eAAe,SAAS,aAAa,SAAS;AAChD,eAAO,WAAW,OAAO,SAAS,SAAS;AAAA,MAC/C;AAEA,YAAM,WAAW,SAAS,SAAS,MAAM;AACrC,eAAO,WAAW,OAAO;AAAA,UACrB;AAAA,UACA,MAAM,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC;AAAA,UAC7C,OAAO,sBAAsB,IAAI,MAAM,IAAI;AAAA,QAC/C,CAAC;AAAA,MACL;AAEA,YAAM,iBAAiB,SAAS,eAAe,MAAM;AACjD,eAAO,MAAM,SAAS,IAAI;AAAA,MAC9B;AAEA,YAAM,cAAc,SAAS,YAAY,MAAM,SAAS;AAEpD,kBAAU,SAAS,SAAS,EAAE;AAC9B,eAAO,SAAS,OAAO;AAAA,UACnB;AAAA,UACA,MAAM,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC;AAAA,UAC7C,OAAO;AAAA,UACP,UAAU;AAAA,QACd,CAAC;AAAA,MACL;AAEA,YAAM,gBAAgB,SAAS,cAAc,SAAS;AAClD,eAAO,WAAW,OAAO,SAAS,UAAU;AAAA,MAChD;AAEA,UAAI,UAAU,cAAc;AACxB,cAAM,eAAe,SAAS,aAAa,MAAM;AAC7C,iBAAO,SAAS,OAAO;AAAA,YACnB;AAAA,YACA,MAAM,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC;AAAA,YAC7C,WAAW;AAAA,UACf,CAAC;AAAA,QACL;AAEA,YAAI,OAAO,QAAQ,YAAY,eAAe,eAAe;AACzD,gBAAM,aAAa,cAAc,MAAM,IACnC,SAAS,wBAAwB,KAAK;AAClC,mBAAO,IAAI,QAAQ;AAAA,cACf,SAAS,qBAAqB,SAAS;AACnC,yBAAS,OAAO;AAAA,kBACZ,MAAM;AAAA,kBACN,MAAM,CAAC,GAAG;AAAA,kBACV,WAAW;AAAA,gBACf,CAAC;AAAA,cACL;AAAA,YACJ;AAAA,UACJ;AAAA,QACR;AAEA,cAAM,iBAAiB,SAAS,eAAe,SAAS;AACpD,iBAAO,WAAW,OAAO,SAAS,WAAW;AAAA,QACjD;AAAA,MACJ;AAEA,YAAM,cAAc,SAAS,cAAc;AACvC,eACI,OAAO,KAAK,MAAM,UAAU,CAAC,CAAC,EAAE,UAC/B,MAAM,QAAQ,CAAC,GAAG;AAAA,MAE3B;AAEA,YAAM,wBAAwB,SAAS,sBAAsB,MAAM;AAC/D,cAAM,SAAS,SAAS,OAAO;AAAA,UAC3B;AAAA,UACA,OAAO,mBAAmB;AAAA,UAC1B,IAAI,OAAO;AACP,mBAAO,CAAC,mBAAmB,CAAC;AAAA,UAChC;AAAA,UACA,WAAW;AAAA,QACf,CAAC;AAED,eAAO,OAAO,MAAM;AAAA,MACxB;AAEA,YAAM,uBAAuB,SAAS,qBAAqB,SAAS;AAChE,eAAO,WAAW,OAAO,SAAS,gBAAgB;AAAA,MACtD;AAEA,YAAM,gBAAgB,SAAS,gBAAgB;AAC3C,gBAAQ,KAAK;AAAA,MACjB;AASA,eAAS,OAAO,WAAW,SAAS,SAAS,QAAQ;AACjD,cAAM,UACF,OAAO,cAAc,WACf,YACA,UAAU,SAAS;AAC7B,cAAM,KAAK,KAAK,MAAM,OAAO;AAC7B,cAAM,YAAY,cAAc,OAAO;AACvC,YAAI,aAAa,QAAQ;AACzB,YAAI,SAAS,MAAM,MAAM;AAEzB,YAAI,UAAU,GAAG;AACb,gBAAM,IAAI,UAAU,kCAAkC;AAAA,QAC1D;AAGA,YAAI,cAAc,KAAK;AACnB,oBAAU;AACV,wBAAc;AAAA,QAClB;AAEA,gBAAQ;AACR,YAAI,WAAW,MAAM;AACrB,YAAI,WAAW,MAAM;AAGrB,YAAI,OACA,gBACA,QACA,iBACA,mBACA;AAGJ,cAAM,aAAa;AAGnB,iBAAS,MAAM;AACf,gBAAQ,KAAK;AACb,YAAI,WAAW,MAAM,KAAK;AAEtB,sBAAY,MAAM,MAAM;AACxB,oBAAU,MAAM,MAAM;AAAA,QAC1B;AAGA,iBAAS,cAAc;AAEnB,kBAAQ,kBAAkB,OAAO,UAAU,MAAM;AAEjD,iBAAO,SAAS,YAAY,QAAQ;AAChC,gBAAI,MAAM,OAAO,MAAM,EAAE,GAAG;AACxB,yBAAW,MAAM;AACjB,oBAAM,MAAM,MAAM;AAClB,uBAAS,MAAM;AACf,kBAAI;AACA,wBAAQ,KAAK;AACb,0BAAU,OAAO,KAAK;AAAA,cAC1B,SAAS,GAAG;AACR,iCAAiB,kBAAkB;AAAA,cACvC;AAEA,kBAAI,SAAS;AAIT,mCAAmB,eAAe;AAClC;AAAA,cACJ;AAEA,gCAAkB;AAAA,YACtB;AAEA,0BAAc;AAAA,UAClB;AAGA,mBAAS,MAAM;AACf,kBAAQ,KAAK;AACb,cAAI,WAAW,MAAM,KAAK;AAEtB,wBAAY,MAAM,MAAM;AACxB,sBAAU,MAAM,MAAM;AAAA,UAC1B;AACA,gBAAM,aAAa;AAGnB,kBAAQ,kBAAkB,OAAO,UAAU,MAAM;AACjD,cAAI,OAAO;AACP,gBAAI;AACA,oBAAM,KAAK,SAAS,MAAM,GAAG;AAAA,YACjC,SAAS,GAAG;AACR,+BAAiB,kBAAkB;AAAA,YACvC;AAAA,UACJ,OAAO;AAEH,kBAAM,MAAM;AAGZ,oBAAQ;AAAA,UACZ;AACA,cAAI,gBAAgB;AAChB,kBAAM;AAAA,UACV;AAEA,cAAI,SAAS;AACT,oBAAQ,MAAM,GAAG;AAAA,UACrB,OAAO;AACH,mBAAO,MAAM;AAAA,UACjB;AAAA,QACJ;AAEA,0BACI,WACA,WAAY;AACR,cAAI;AACA,8BAAkB;AAClB,0BAAc;AACd,wBAAY;AAAA,UAChB,SAAS,GAAG;AACR,mBAAO,CAAC;AAAA,UACZ;AAAA,QACJ;AAEJ,4BAAoB,WAAY;AAE5B,cAAI,WAAW,MAAM,KAAK;AACtB,wBAAY,MAAM,MAAM;AACxB,sBAAU,MAAM,MAAM;AACtB,wBAAY,MAAM,MAAM;AAAA,UAC5B;AAAA,QACJ;AAEA,wBAAgB,WAAY;AACxB,kBAAQ,kBAAkB,OAAO,UAAU,MAAM;AACjD,qBAAW;AAAA,QACf;AAEA,eAAO,YAAY;AAAA,MACvB;AAMA,YAAM,OAAO,SAAS,KAAK,WAAW;AAClC,eAAO,OAAO,WAAW,KAAK;AAAA,MAClC;AAEA,UAAI,OAAO,QAAQ,YAAY,aAAa;AAKxC,cAAM,YAAY,SAAS,UAAU,WAAW;AAC5C,iBAAO,IAAI,QAAQ,QAAQ,SAAU,SAAS,QAAQ;AAClD,+BAAmB,WAAY;AAC3B,kBAAI;AACA,uBAAO,WAAW,MAAM,SAAS,MAAM;AAAA,cAC3C,SAAS,GAAG;AACR,uBAAO,CAAC;AAAA,cACZ;AAAA,YACJ,CAAC;AAAA,UACL,CAAC;AAAA,QACL;AAAA,MACJ;AAEA,YAAM,OAAO,SAAS,OAAO;AACzB,gBAAQ,KAAK;AACb,cAAM,QAAQ,WAAW,KAAK;AAC9B,YAAI,CAAC,OAAO;AACR,iBAAO,MAAM;AAAA,QACjB;AAEA,cAAM,aAAa;AACnB,YAAI;AACA,gBAAM,MAAM,MAAM;AAClB,oBAAU,OAAO,KAAK;AACtB,kBAAQ,KAAK;AACb,iBAAO,MAAM;AAAA,QACjB,UAAE;AACE,gBAAM,aAAa;AAAA,QACvB;AAAA,MACJ;AAEA,UAAI,OAAO,QAAQ,YAAY,aAAa;AACxC,cAAM,YAAY,SAAS,YAAY;AACnC,iBAAO,IAAI,QAAQ,QAAQ,SAAU,SAAS,QAAQ;AAClD,+BAAmB,WAAY;AAC3B,kBAAI;AACA,sBAAM,QAAQ,WAAW,KAAK;AAC9B,oBAAI,CAAC,OAAO;AACR,0BAAQ,MAAM,GAAG;AACjB;AAAA,gBACJ;AAEA,oBAAI;AACJ,sBAAM,aAAa;AACnB,sBAAM,MAAM,MAAM;AAClB,oBAAI;AACA,4BAAU,OAAO,KAAK;AAAA,gBAC1B,SAAS,GAAG;AACR,wBAAM;AAAA,gBACV;AACA,sBAAM,aAAa;AAEnB,mCAAmB,WAAY;AAC3B,sBAAI,KAAK;AACL,2BAAO,GAAG;AAAA,kBACd,OAAO;AACH,4BAAQ,MAAM,GAAG;AAAA,kBACrB;AAAA,gBACJ,CAAC;AAAA,cACL,SAAS,GAAG;AACR,uBAAO,CAAC;AAAA,cACZ;AAAA,YACJ,CAAC;AAAA,UACL,CAAC;AAAA,QACL;AAAA,MACJ;AAEA,YAAM,SAAS,SAAS,SAAS;AAC7B,YAAI,WAAW;AACf,gBAAQ,KAAK;AACb,aAAK,IAAI,GAAG,IAAI,MAAM,WAAW,KAAK;AAClC,cAAI,CAAC,MAAM,QAAQ;AACf,qCAAyB;AACzB,mBAAO,MAAM;AAAA,UACjB;AAEA,sBAAY,OAAO,KAAK,MAAM,MAAM,EAAE;AACtC,cAAI,cAAc,GAAG;AACjB,qCAAyB;AACzB,mBAAO,MAAM;AAAA,UACjB;AAEA,gBAAM,KAAK;AACX,mCAAyB,OAAO,CAAC;AAAA,QACrC;AAEA,cAAM,YAAY,WAAW,KAAK;AAClC,cAAM,qBAAqB,OAAO,SAAS;AAAA,MAC/C;AAEA,YAAM,aAAa,SAAS,aAAa;AACrC,eAAO,MAAM,KAAK,mBAAmB,CAAC;AAAA,MAC1C;AAEA,UAAI,OAAO,QAAQ,YAAY,aAAa;AACxC,cAAM,cAAc,SAAS,cAAc;AACvC,iBAAO,IAAI,QAAQ,QAAQ,SAAU,SAAS,QAAQ;AAClD,gBAAI,IAAI;AAIR,qBAAS,QAAQ;AACb,iCAAmB,WAAY;AAC3B,oBAAI;AACA,0BAAQ,KAAK;AAEb,sBAAI;AACJ,sBAAI,IAAI,MAAM,WAAW;AACrB,wBAAI,CAAC,MAAM,QAAQ;AACf,+CAAyB;AACzB,8BAAQ,MAAM,GAAG;AACjB;AAAA,oBACJ;AAEA,gCAAY,OAAO;AAAA,sBACf,MAAM;AAAA,oBACV,EAAE;AACF,wBAAI,cAAc,GAAG;AACjB,+CAAyB;AACzB,8BAAQ,MAAM,GAAG;AACjB;AAAA,oBACJ;AAEA,0BAAM,KAAK;AAEX;AAEA,0BAAM;AACN,6CAAyB,OAAO,CAAC;AACjC;AAAA,kBACJ;AAEA,wBAAM,YAAY,WAAW,KAAK;AAClC,yBAAO,qBAAqB,OAAO,SAAS,CAAC;AAAA,gBACjD,SAAS,GAAG;AACR,yBAAO,CAAC;AAAA,gBACZ;AAAA,cACJ,CAAC;AAAA,YACL;AACA,kBAAM;AAAA,UACV,CAAC;AAAA,QACL;AAAA,MACJ;AAEA,YAAM,YAAY,SAAS,YAAY;AACnC,cAAM,QAAQ,UAAU,KAAK;AAC7B,YAAI,CAAC,OAAO;AACR,kBAAQ,KAAK;AACb,iBAAO,MAAM;AAAA,QACjB;AAEA,eAAO,MAAM,KAAK,MAAM,SAAS,MAAM,GAAG;AAAA,MAC9C;AAEA,UAAI,OAAO,QAAQ,YAAY,aAAa;AACxC,cAAM,iBAAiB,SAAS,iBAAiB;AAC7C,iBAAO,IAAI,QAAQ,QAAQ,SAAU,SAAS,QAAQ;AAClD,+BAAmB,WAAY;AAC3B,kBAAI;AACA,sBAAM,QAAQ,UAAU,KAAK;AAC7B,oBAAI,CAAC,OAAO;AACR,0BAAQ,KAAK;AACb,0BAAQ,MAAM,GAAG;AAAA,gBACrB;AAEA,wBAAQ,MAAM,UAAU,MAAM,SAAS,MAAM,GAAG,CAAC;AAAA,cACrD,SAAS,GAAG;AACR,uBAAO,CAAC;AAAA,cACZ;AAAA,YACJ,CAAC;AAAA,UACL,CAAC;AAAA,QACL;AAAA,MACJ;AAEA,YAAM,QAAQ,SAAS,QAAQ;AAC3B,gBAAQ;AACR,cAAM,SAAS,CAAC;AAChB,cAAM,OAAO,CAAC;AACd,cAAM,MAAM;AAAA,MAChB;AAEA,YAAM,gBAAgB,SAAS,cAAc,YAAY;AAErD,cAAM,SAAS,SAAS,UAAU;AAClC,cAAM,aAAa,SAAS,MAAM;AAClC,YAAI,IAAI;AAER,2BAAmB,CAAC,IAAI,mBAAmB,CAAC,IAAI;AAChD,2BAAmB,CAAC,IAAI,mBAAmB,CAAC,IAAI;AAEhD,cAAM,MAAM;AACZ,gBAAQ;AAGR,aAAK,MAAM,MAAM,QAAQ;AACrB,cAAI,MAAM,OAAO,eAAe,EAAE,GAAG;AACjC,oBAAQ,MAAM,OAAO,EAAE;AACvB,kBAAM,aAAa;AACnB,kBAAM,UAAU;AAAA,UACpB;AAAA,QACJ;AAAA,MACJ;AAMA,YAAM,OAAO,SAAS,KAAK,WAAW;AAClC,cAAM,UACF,OAAO,cAAc,WACf,YACA,UAAU,SAAS;AAC7B,cAAM,KAAK,KAAK,MAAM,OAAO;AAE7B,mBAAW,SAAS,OAAO,OAAO,MAAM,MAAM,GAAG;AAC7C,cAAI,MAAM,MAAM,KAAK,MAAM,QAAQ;AAC/B,kBAAM,SAAS,MAAM,MAAM;AAAA,UAC/B;AAAA,QACJ;AACA,cAAM,KAAK,EAAE;AAAA,MACjB;AAEA,UAAI,UAAU,aAAa;AACvB,cAAM,cAAc,uBAAO,OAAO,IAAI;AACtC,cAAM,YAAY,MAAM;AAAA,MAC5B;AAEA,UAAI,UAAU,QAAQ;AAClB,cAAM,SAAS;AAAA,MACnB;AAEA,aAAO;AAAA,IACX;AAQA,aAAS,QAAQ,QAAQ;AACrB,UACI,UAAU,SAAS,KACnB,kBAAkB,QAClB,MAAM,QAAQ,MAAM,KACpB,OAAO,WAAW,UACpB;AACE,cAAM,IAAI;AAAA,UACN,kCAAkC;AAAA,YAC9B;AAAA,UACJ,CAAC;AAAA,QACL;AAAA,MACJ;AAEA,UAAI,QAAQ,KAAK,WAAW,MAAM;AAG9B,cAAM,IAAI;AAAA,UACN;AAAA,QACJ;AAAA,MACJ;AAGA,eAAS,OAAO,WAAW,cAAc,SAAS,CAAC;AACnD,aAAO,oBAAoB,OAAO,qBAAqB;AACvD,aAAO,mBAAmB,OAAO,oBAAoB;AACrD,aAAO,0BACH,OAAO,2BAA2B;AAEtC,UAAI,OAAO,QAAQ;AACf,cAAM,IAAI;AAAA,UACN;AAAA,QACJ;AAAA,MACJ;AAMA,eAAS,mBAAmB,OAAO;AAC/B,YAAI,OAAO,qBAAqB;AAC5B;AAAA,QACJ;AAEA,cAAM,IAAI;AAAA,UACN,wDAAwD,KAAK;AAAA,QACjE;AAAA,MACJ;AAEA,UAAI,GAAG;AACP,YAAM,QAAQ,YAAY,OAAO,KAAK,OAAO,SAAS;AACtD,YAAM,0BAA0B,OAAO;AAEvC,YAAM,YAAY,WAAY;AAC1B,eAAO,UAAU,OAAO,MAAM;AAAA,MAClC;AAEA,YAAM,mBAAmB,oBAAI,IAAI;AAEjC,YAAM,UAAU,OAAO,UAAU,CAAC;AAElC,UAAI,MAAM,QAAQ,WAAW,GAAG;AAC5B,cAAM,UAAU,OAAO,KAAK,MAAM;AAAA,MACtC;AAEA,UAAI,OAAO,sBAAsB,MAAM;AACnC,cAAM,eAAe,eAAe;AAAA,UAChC;AAAA,UACA;AAAA,UACA,OAAO;AAAA,QACX;AACA,cAAM,aAAa,QAAQ;AAAA,UACvB;AAAA,UACA,OAAO;AAAA,QACX;AACA,cAAM,mBAAmB;AAAA,MAC7B;AAEA,UAAI,MAAM,QAAQ,SAAS,aAAa,GAAG;AACvC,cAAM,SAAS,MAAM;AACjB,cAAI,oCAAoC;AACpC,mBAAO,QAAQ,YAAY,YAAY;AAAA,UAC3C;AACA,cAAI,yBAAyB;AACzB,mBAAO,QAAQ,YAAY;AAAA,UAC/B;AAAA,QACJ,GAAG;AACH,YAAI,OAAO;AACP,iBAAO,oBAAoB,KAAK,EAAE,QAAQ,SAAU,MAAM;AACtD,gBAAI,SAAS,OAAO;AAChB,oBAAM,YAAY,IAAI,IAClB,KAAK,QAAQ,YAAY,MAAM,IACzB,aACA;AAAA,YACd;AAAA,UACJ,CAAC;AAED,gBAAM,YAAY,OAAO,CAAC,SACtB,IAAI,qBAAqB,MAAM,QAAQ,GAAG,CAAC;AAC/C,gBAAM,YAAY,UAAU,CAAC,SACzB,IAAI,qBAAqB,MAAM,WAAW,GAAG,GAAG;AAGpD,gBAAM,YAAY,aAAa,SAAS,OAAO,GAAG;AAAA,QACtD,YAAY,OAAO,UAAU,CAAC,GAAG,SAAS,aAAa,GAAG;AACtD,iBAAO,mBAAmB,aAAa;AAAA,QAC3C;AAAA,MACJ;AACA,UAAI,YAAY,gBAAgB,cAAc;AAC1C,cAAM,sBAAsB,CAAC;AAAA,MACjC;AACA,UAAI,YAAY,gBAAgB,sBAAsB;AAClD,cAAM,8BAA8B,CAAC;AAAA,MACzC;AACA,WAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,QAAQ,IAAI,GAAG,KAAK;AAC9C,cAAM,wBAAwB,MAAM,QAAQ,CAAC;AAE7C,YAAI,CAAC,UAAU,qBAAqB,GAAG;AACnC,6BAAmB,qBAAqB;AAExC;AAAA,QACJ;AAEA,YAAI,0BAA0B,UAAU;AACpC,cACI,QAAQ,WACR,OAAO,QAAQ,QAAQ,WAAW,YACpC;AACE,yBAAa,QAAQ,SAAS,uBAAuB,KAAK;AAAA,UAC9D;AAAA,QACJ,WAAW,0BAA0B,YAAY;AAC7C,cACI,QAAQ,WACR,OAAO,QAAQ,QAAQ,aAAa,YACtC;AACE,yBAAa,QAAQ,SAAS,uBAAuB,KAAK;AAAA,UAC9D;AAAA,QACJ,OAAO;AACH,uBAAa,SAAS,uBAAuB,KAAK;AAAA,QACtD;AACA,YACI,MAAM,wBAAwB,UAC9B,aAAa,qBAAqB,GACpC;AACE,gBAAM,WAAW,aAAa,qBAAqB;AACnD,gBAAM,oBAAoB,KAAK;AAAA,YAC3B,YAAY;AAAA,YACZ;AAAA,UACJ,CAAC;AACD,uBAAa,qBAAqB,IAC9B,QAAQ,qBAAqB;AAAA,QACrC;AACA,YAAI,MAAM,gCAAgC,QAAW;AACjD,cAAI,0BAA0B,cAAc;AACxC,kBAAM,4BAA4B,KAAK;AAAA,cACnC,YAAY;AAAA,cACZ,UAAU,qBAAqB;AAAA,YACnC,CAAC;AAED,iCAAqB,aAAa,CAC9B,OACA,OACA,UAAU,CAAC,MAEX,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC7B,oBAAM,QAAQ,MAAM;AAChB,wBAAQ,OAAO;AAAA,kBACX;AAAA,kBACA;AAAA,gBACJ;AACA,sBAAM,iBAAiB,OAAO,KAAK;AAKnC,sBAAM,aAAa,MAAM;AACzB,uBAAO,QAAQ,OAAO,MAAM;AAAA,cAChC;AAEA,oBAAM,SAAS,MAAM,WAAW,MAAM;AAClC,oBAAI,QAAQ,QAAQ;AAChB,0BAAQ,OAAO;AAAA,oBACX;AAAA,oBACA;AAAA,kBACJ;AACA,wBAAM,iBAAiB,OAAO,KAAK;AAAA,gBACvC;AAEA,wBAAQ,KAAK;AAAA,cACjB,GAAG,KAAK;AAER,kBAAI,QAAQ,QAAQ;AAChB,oBAAI,QAAQ,OAAO,SAAS;AACxB,wBAAM;AAAA,gBACV,OAAO;AACH,0BAAQ,OAAO;AAAA,oBACX;AAAA,oBACA;AAAA,kBACJ;AACA,wBAAM,iBAAiB;AAAA,oBACnB;AAAA,oBACA,QAAQ;AAAA,kBACZ;AAAA,gBACJ;AAAA,cACJ;AAAA,YACJ,CAAC;AAAA,UACT,WAAW,0BAA0B,gBAAgB;AACjD,kBAAM,4BAA4B,KAAK;AAAA,cACnC,YAAY;AAAA,cACZ,UAAU,qBAAqB;AAAA,YACnC,CAAC;AAED,iCAAqB,eAAe,CAAC,OAAO,UAAU,CAAC,MACnD,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC7B,oBAAM,QAAQ,MAAM;AAChB,wBAAQ,OAAO;AAAA,kBACX;AAAA,kBACA;AAAA,gBACJ;AACA,sBAAM,iBAAiB,OAAO,KAAK;AAKnC,sBAAM,eAAe,MAAM;AAC3B,uBAAO,QAAQ,OAAO,MAAM;AAAA,cAChC;AAEA,oBAAM,SAAS,MAAM,aAAa,MAAM;AACpC,oBAAI,QAAQ,QAAQ;AAChB,0BAAQ,OAAO;AAAA,oBACX;AAAA,oBACA;AAAA,kBACJ;AACA,wBAAM,iBAAiB,OAAO,KAAK;AAAA,gBACvC;AAEA,wBAAQ,KAAK;AAAA,cACjB,CAAC;AAED,kBAAI,QAAQ,QAAQ;AAChB,oBAAI,QAAQ,OAAO,SAAS;AACxB,wBAAM;AAAA,gBACV,OAAO;AACH,0BAAQ,OAAO;AAAA,oBACX;AAAA,oBACA;AAAA,kBACJ;AACA,wBAAM,iBAAiB;AAAA,oBACnB;AAAA,oBACA,QAAQ;AAAA,kBACZ;AAAA,gBACJ;AAAA,cACJ;AAAA,YACJ,CAAC;AAAA,UACT,WAAW,0BAA0B,eAAe;AAChD,kBAAM,4BAA4B,KAAK;AAAA,cACnC,YAAY;AAAA,cACZ,UAAU,qBAAqB;AAAA,YACnC,CAAC;AAED,iCAAqB,cAAc,CAC/B,OACA,OACA,UAAU,CAAC,OACT;AAAA,cACF,CAAC,OAAO,aAAa,GAAG,MAAM;AAC1B,sBAAM,mBAAmB,MAAM;AAC3B,sBAAI,SAAS;AACb,wBAAM,UAAU,IAAI,QAAQ,CAAC,KAAK,QAAQ;AACtC,8BAAU;AACV,6BAAS;AAAA,kBACb,CAAC;AACD,0BAAQ,UAAU;AAClB,0BAAQ,SAAS;AACjB,yBAAO;AAAA,gBACX;AAEA,oBAAI,OAAO;AACX,oBAAI,YAAY;AAChB,oBAAI;AACJ,oBAAI,gBAAgB;AACpB,sBAAM,YAAY,CAAC;AAEnB,sBAAM,SAAS,MAAM,YAAY,MAAM;AACnC,sBAAI,UAAU,SAAS,GAAG;AACtB,8BAAU,MAAM,EAAE,QAAQ;AAAA,kBAC9B,OAAO;AACH;AAAA,kBACJ;AAAA,gBACJ,GAAG,KAAK;AAER,sBAAM,QAAQ,MAAM;AAChB,0BAAQ,OAAO;AAAA,oBACX;AAAA,oBACA;AAAA,kBACJ;AACA,wBAAM,iBAAiB,OAAO,KAAK;AAEnC,wBAAM,cAAc,MAAM;AAC1B,yBAAO;AACP,6BAAW,cAAc,WAAW;AAChC,+BAAW,QAAQ;AAAA,kBACvB;AAAA,gBACJ;AAEA,oBAAI,QAAQ,QAAQ;AAChB,sBAAI,QAAQ,OAAO,SAAS;AACxB,2BAAO;AAAA,kBACX,OAAO;AACH,4BAAQ,OAAO;AAAA,sBACX;AAAA,sBACA;AAAA,oBACJ;AACA,0BAAM,iBAAiB;AAAA,sBACnB;AAAA,sBACA,QAAQ;AAAA,oBACZ;AAAA,kBACJ;AAAA,gBACJ;AAEA,uBAAO;AAAA,kBACH,MAAM,YAAY;AACd,wBAAI,QAAQ,QAAQ,WAAW,CAAC,WAAW;AACvC,kCAAY;AACZ,4BAAM,QAAQ,OAAO;AAAA,oBACzB;AAEA,wBAAI,MAAM;AACN,6BAAO,EAAE,MAAM,MAAM,OAAO,OAAU;AAAA,oBAC1C;AAEA,wBAAI,gBAAgB,GAAG;AACnB;AACA,6BAAO,EAAE,MAAM,OAAO,MAAa;AAAA,oBACvC;AAEA,0BAAM,aAAa,iBAAiB;AACpC,8BAAU,KAAK,UAAU;AAEzB,0BAAM;AAEN,wBAAI,cAAc,UAAU,WAAW,GAAG;AACtC,iCAAW,QAAQ;AAAA,oBACvB;AAEA,wBAAI,QAAQ,QAAQ,WAAW,CAAC,WAAW;AACvC,kCAAY;AACZ,4BAAM,QAAQ,OAAO;AAAA,oBACzB;AAEA,wBAAI,MAAM;AACN,6BAAO,EAAE,MAAM,MAAM,OAAO,OAAU;AAAA,oBAC1C;AAEA,2BAAO,EAAE,MAAM,OAAO,MAAa;AAAA,kBACvC;AAAA,kBACA,QAAQ,YAAY;AAChB,wBAAI,MAAM;AACN,6BAAO,EAAE,MAAM,MAAM,OAAO,OAAU;AAAA,oBAC1C;AAEA,wBAAI,UAAU,SAAS,GAAG;AACtB,mCAAa,iBAAiB;AAC9B,4BAAM;AAAA,oBACV;AAEA,0BAAM,cAAc,MAAM;AAC1B,2BAAO;AAEP,wBAAI,QAAQ,QAAQ;AAChB,8BAAQ,OAAO;AAAA,wBACX;AAAA,wBACA;AAAA,sBACJ;AACA,4BAAM,iBAAiB,OAAO,KAAK;AAAA,oBACvC;AAEA,2BAAO,EAAE,MAAM,MAAM,OAAO,OAAU;AAAA,kBAC1C;AAAA,gBACJ;AAAA,cACJ;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAEA,aAAO;AAAA,IACX;AAIA,WAAO;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAaA,QAAM,wBAAwB,WAAW,YAAY;AAErD,gBAAc,SAAS,sBAAsB;AAC7C,gBAAc,cAAc,sBAAsB;AAClD,gBAAc,UAAU,sBAAsB;AAC9C,gBAAc,aAAa;AAC3B,SAAO;AACR;AAEA,IAAI,uBAAuB,qBAAqB;AAEhD,IAAM,aAAN,MAAiB;AAAA,EAChB;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,OAAO,SAAS;AAAA,EAChB,YAAY,EAAE,QAAAT,SAAQ,OAAO,GAAG;AAC/B,SAAK,cAAc;AACnB,SAAK,cAAc;AACnB,SAAK,cAAc;AACnB,SAAK,cAAc,qBAAqB,WAAWA,OAAM;AACzD,SAAK,UAAUA;AAAA,EAChB;AAAA,EACA,iBAAiB;AAChB,QAAI,KAAK,YAAa,MAAK,OAAO,MAAM;AAAA,EACzC;AAAA,EACA,UAAU;AACT,SAAK,cAAc;AAAA,EACpB;AAAA,EACA,eAAe;AACd,QAAI,KAAK,iBAAiB,EAAG,MAAK,OAAO,OAAO;AAAA,EACjD;AAAA,EACA,MAAM,oBAAoB;AACzB,QAAI,KAAK,iBAAiB,EAAG,OAAM,KAAK,OAAO,YAAY;AAAA,EAC5D;AAAA,EACA,uBAAuB;AACtB,QAAI,KAAK,iBAAiB,EAAG,MAAK,OAAO,UAAU;AAAA,EACpD;AAAA,EACA,MAAM,4BAA4B;AACjC,QAAI,KAAK,iBAAiB,EAAG,OAAM,KAAK,OAAO,eAAe;AAAA,EAC/D;AAAA,EACA,yBAAyB,QAAQ,GAAG;AACnC,QAAI,KAAK,iBAAiB,EAAG,UAAS,IAAI,OAAO,IAAI,GAAG,KAAK;AAC5D,WAAK,OAAO,KAAK;AAEjB,WAAK,OAAO,KAAK,CAAC;AAClB,UAAI,KAAK,OAAO,YAAY,MAAM,EAAG;AAAA,IACtC;AAAA,EACD;AAAA,EACA,MAAM,8BAA8B,QAAQ,GAAG;AAC9C,QAAI,KAAK,iBAAiB,EAAG,UAAS,IAAI,OAAO,IAAI,GAAG,KAAK;AAC5D,YAAM,KAAK,OAAO,UAAU;AAE5B,WAAK,OAAO,KAAK,CAAC;AAClB,UAAI,KAAK,OAAO,YAAY,MAAM,EAAG;AAAA,IACtC;AAAA,EACD;AAAA,EACA,oBAAoB,SAAS;AAC5B,QAAI,KAAK,iBAAiB,EAAG,MAAK,OAAO,KAAK,OAAO;AAAA,EACtD;AAAA,EACA,MAAM,yBAAyB,SAAS;AACvC,QAAI,KAAK,iBAAiB,EAAG,OAAM,KAAK,OAAO,UAAU,OAAO;AAAA,EACjE;AAAA,EACA,2BAA2B;AAC1B,QAAI,KAAK,iBAAiB,EAAG,MAAK,OAAO,WAAW;AAAA,EACrD;AAAA,EACA,cAAc;AACb,QAAI,KAAK,iBAAiB;AAE1B,WAAK,OAAO,cAAc;AAAA,EAC3B;AAAA,EACA,gBAAgB;AACf,QAAI,KAAK,aAAa;AACrB,gBAAU;AACV,WAAK,cAAc;AAAA,IACpB;AACA,QAAI,KAAK,aAAa;AACrB,WAAK,OAAO,UAAU;AACtB,WAAK,cAAc;AAAA,IACpB;AAAA,EACD;AAAA,EACA,gBAAgB;AACf,QAAI,KAAK,YAAa,OAAM,IAAI,MAAM,uIAAyI;AAC/K,QAAI,CAAC,KAAK,aAAa;AACtB,YAAM,SAAS,OAAO,KAAK,KAAK,YAAY,MAAM,EAAE,OAAO,CAAC,UAAU,UAAU,cAAc,UAAU,gBAAgB;AACxH,UAAI,KAAK,aAAa,QAAQ,SAAS,UAAU,KAAK,eAAe,EAAG,OAAM,IAAI,MAAM,wDAAwD;AAChJ,WAAK,SAAS,KAAK,YAAY,QAAQ;AAAA,QACtC,KAAK,KAAK,IAAI;AAAA,QACd,GAAG,KAAK;AAAA,QACR,QAAQ,KAAK,aAAa,UAAU;AAAA,QACpC,qBAAqB;AAAA,MACtB,CAAC;AACD,WAAK,cAAc;AAAA,IACpB;AAAA,EACD;AAAA,EACA,QAAQ;AACP,QAAI,KAAK,iBAAiB,GAAG;AAC5B,YAAM,EAAE,KAAAQ,KAAI,IAAI,KAAK;AACrB,WAAK,OAAO,MAAM;AAClB,WAAK,OAAO,cAAcA,IAAG;AAAA,IAC9B;AAAA,EACD;AAAA,EACA,cAAcA,MAAK;AAClB,UAAM,OAAO,OAAOA,SAAQ,eAAeA,gBAAe,OAAOA,OAAM,IAAI,KAAKA,IAAG;AACnF,QAAI,KAAK,YAAa,MAAK,OAAO,cAAc,IAAI;AAAA,SAC/C;AACJ,WAAK,cAAc,QAAQ,IAAI,KAAK,KAAK,kBAAkB,CAAC;AAC5D,eAAS,KAAK,WAAW;AAAA,IAC1B;AAAA,EACD;AAAA,EACA,sBAAsB;AACrB,WAAO,KAAK,cAAc,IAAI,KAAK,KAAK,OAAO,GAAG,IAAI,KAAK;AAAA,EAC5D;AAAA,EACA,oBAAoB;AACnB,WAAO,KAAK,KAAK;AAAA,EAClB;AAAA,EACA,gBAAgB;AACf,QAAI,KAAK,iBAAiB,EAAG,QAAO,KAAK,OAAO,YAAY;AAC5D,WAAO;AAAA,EACR;AAAA,EACA,UAAU,QAAQ;AACjB,SAAK,cAAc;AAAA,EACpB;AAAA,EACA,eAAe;AACd,WAAO,KAAK;AAAA,EACb;AAAA,EACA,mBAAmB;AAClB,QAAI,CAAC,KAAK,YAAa,OAAM,IAAI,MAAM,gEAAkE;AACzG,WAAO,KAAK;AAAA,EACb;AACD;AAEA,SAAS,eAAe,QAAQ,QAAQ;AACvC,MAAI,OAAO,UAAU,OAAQ,QAAO,QAAQ,OAAO,MAAM,QAAQ,OAAO,SAAS,OAAO,OAAO;AAC/F,SAAO;AACR;AACA,SAAS,QAAQ,UAAU,UAAU,CAAC,GAAG;AACxC,QAAM,EAAE,YAAY,aAAa,cAAc,cAAc,IAAIE,eAAc;AAC/E,QAAM,EAAE,WAAW,IAAI,UAAU,IAAI,IAAI,OAAO,YAAY,WAAW,EAAE,SAAS,QAAQ,IAAI;AAC9F,QAAM,oBAAoB,IAAI,MAAM,mBAAmB;AACvD,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvC,QAAI;AACJ,QAAI,gBAAgB;AACpB,QAAI;AACJ,QAAI;AACJ,UAAM,YAAY,CAAC,WAAW;AAC7B,UAAI,UAAW,cAAa,SAAS;AACrC,UAAI,WAAY,eAAc,UAAU;AACxC,cAAQ,MAAM;AAAA,IACf;AACA,UAAM,gBAAgB,MAAM;AAC3B,UAAI,WAAY,eAAc,UAAU;AACxC,UAAI,QAAQ;AACZ,UAAI,CAAC,MAAO,SAAQ,eAAe,IAAI,MAAM,uBAAuB,GAAG,iBAAiB;AACxF,aAAO,KAAK;AAAA,IACb;AACA,UAAM,gBAAgB,MAAM;AAC3B,UAAI,GAAG,aAAa,EAAG,IAAG,oBAAoB,QAAQ;AACtD,UAAI,kBAAkB,UAAW;AACjC,UAAI;AACH,cAAM,SAAS,SAAS;AACxB,YAAI,WAAW,QAAQ,OAAO,WAAW,YAAY,OAAO,OAAO,SAAS,YAAY;AACvF,gBAAM,WAAW;AACjB,0BAAgB;AAChB,mBAAS,KAAK,CAAC,kBAAkB;AAChC,4BAAgB;AAChB,sBAAU,aAAa;AAAA,UACxB,GAAG,CAAC,kBAAkB;AACrB,4BAAgB;AAChB,wBAAY;AAAA,UACb,CAAC;AAAA,QACF,OAAO;AACN,oBAAU,MAAM;AAChB,iBAAO;AAAA,QACR;AAAA,MACD,SAAS,OAAO;AACf,oBAAY;AAAA,MACb;AAAA,IACD;AACA,QAAI,cAAc,MAAM,KAAM;AAC9B,gBAAY,WAAW,eAAe,OAAO;AAC7C,iBAAa,YAAY,eAAe,QAAQ;AAAA,EACjD,CAAC;AACF;AACA,SAAS,UAAU,UAAU,UAAU,CAAC,GAAG;AAC1C,QAAM,EAAE,YAAY,aAAa,cAAc,cAAc,IAAIA,eAAc;AAC/E,QAAM,EAAE,WAAW,IAAI,UAAU,IAAI,IAAI,OAAO,YAAY,WAAW,EAAE,SAAS,QAAQ,IAAI;AAC9F,QAAM,oBAAoB,IAAI,MAAM,mBAAmB;AACvD,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvC,QAAI,gBAAgB;AACpB,QAAI;AACJ,QAAI;AACJ,UAAM,WAAW,CAAC,UAAU;AAC3B,UAAI,WAAY,eAAc,UAAU;AACxC,UAAI,CAAC,MAAO,SAAQ,eAAe,IAAI,MAAM,yBAAyB,GAAG,iBAAiB;AAC1F,aAAO,KAAK;AAAA,IACb;AACA,UAAM,YAAY,CAAC,WAAW;AAC7B,UAAI,CAAC,OAAQ;AACb,UAAI,UAAW,cAAa,SAAS;AACrC,UAAI,WAAY,eAAc,UAAU;AACxC,cAAQ,MAAM;AACd,aAAO;AAAA,IACR;AACA,UAAM,gBAAgB,MAAM;AAC3B,UAAI,GAAG,aAAa,EAAG,IAAG,oBAAoB,QAAQ;AACtD,UAAI,kBAAkB,UAAW;AACjC,UAAI;AACH,cAAM,SAAS,SAAS;AACxB,YAAI,WAAW,QAAQ,OAAO,WAAW,YAAY,OAAO,OAAO,SAAS,YAAY;AACvF,gBAAM,WAAW;AACjB,0BAAgB;AAChB,mBAAS,KAAK,CAAC,kBAAkB;AAChC,4BAAgB;AAChB,sBAAU,aAAa;AAAA,UACxB,GAAG,CAAC,kBAAkB;AACrB,4BAAgB;AAChB,qBAAS,aAAa;AAAA,UACvB,CAAC;AAAA,QACF,MAAO,QAAO,UAAU,MAAM;AAAA,MAC/B,SAAS,OAAO;AACf,iBAAS,KAAK;AAAA,MACf;AAAA,IACD;AACA,QAAI,cAAc,MAAM,KAAM;AAC9B,gBAAY,WAAW,UAAU,OAAO;AACxC,iBAAa,YAAY,eAAe,QAAQ;AAAA,EACjD,CAAC;AACF;AAEA,SAAS,eAAe;AACvB,MAAI,UAAU;AACd,QAAM,cAAc,eAAe;AACnC,MAAI;AACJ,QAAM,SAAS,MAAM,YAAY,IAAI,WAAW;AAAA,IAC/C,QAAQ;AAAA,IACR,QAAQ,YAAY,OAAO;AAAA,EAC5B,CAAC;AACD,QAAM,eAA+B,oBAAI,IAAI;AAC7C,QAAM,YAA4B,oBAAI,IAAI;AAC1C,QAAM,eAAe;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACA,QAAM,QAAQ;AAAA,IACb,cAAc,QAAQ;AACrB,UAAI,eAAe,GAAG;AACrB,YAAI,QAAQ,QAAQ,SAAS,UAAU,KAAK,YAAY,QAAQ,YAAY,QAAQ,SAAS,UAAU,EAAG,OAAM,IAAI,MAAM,wIAA0I;AAAA,MACrQ;AACA,UAAI,OAAQ,QAAO,EAAE,UAAU;AAAA,QAC9B,GAAG,YAAY,OAAO;AAAA,QACtB,GAAG;AAAA,MACJ,CAAC;AAAA,UACI,QAAO,EAAE,UAAU,YAAY,OAAO,UAAU;AACrD,aAAO,EAAE,cAAc;AACvB,aAAO;AAAA,IACR;AAAA,IACA,eAAe;AACd,aAAO,OAAO,EAAE,aAAa;AAAA,IAC9B;AAAA,IACA,gBAAgB;AACf,aAAO,EAAE,cAAc;AACvB,aAAO;AAAA,IACR;AAAA,IACA,uBAAuB;AACtB,aAAO,EAAE,qBAAqB;AAC9B,aAAO;AAAA,IACR;AAAA,IACA,MAAM,4BAA4B;AACjC,YAAM,OAAO,EAAE,0BAA0B;AACzC,aAAO;AAAA,IACR;AAAA,IACA,eAAe;AACd,aAAO,EAAE,aAAa;AACtB,aAAO;AAAA,IACR;AAAA,IACA,MAAM,oBAAoB;AACzB,YAAM,OAAO,EAAE,kBAAkB;AACjC,aAAO;AAAA,IACR;AAAA,IACA,cAAc;AACb,aAAO,EAAE,YAAY;AACrB,aAAO;AAAA,IACR;AAAA,IACA,oBAAoB,IAAI;AACvB,aAAO,EAAE,oBAAoB,EAAE;AAC/B,aAAO;AAAA,IACR;AAAA,IACA,MAAM,yBAAyB,IAAI;AAClC,YAAM,OAAO,EAAE,yBAAyB,EAAE;AAC1C,aAAO;AAAA,IACR;AAAA,IACA,2BAA2B;AAC1B,aAAO,EAAE,yBAAyB;AAClC,aAAO;AAAA,IACR;AAAA,IACA,MAAM,gCAAgC;AACrC,YAAM,OAAO,EAAE,8BAA8B;AAC7C,aAAO;AAAA,IACR;AAAA,IACA,2BAA2B;AAC1B,aAAO,EAAE,yBAAyB;AAClC,aAAO;AAAA,IACR;AAAA,IACA,gBAAgB;AACf,aAAO,OAAO,EAAE,cAAc;AAAA,IAC/B;AAAA,IACA,cAAc,MAAM;AACnB,aAAO,EAAE,cAAc,IAAI;AAC3B,aAAO;AAAA,IACR;AAAA,IACA,sBAAsB;AACrB,aAAO,OAAO,EAAE,oBAAoB;AAAA,IACrC;AAAA,IACA,oBAAoB;AACnB,aAAO,OAAO,EAAE,kBAAkB;AAAA,IACnC;AAAA,IACA,iBAAiB;AAChB,aAAO,EAAE,eAAe;AACxB,aAAO;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ,SAAS;AAChB,kBAAY,SAAS,wBAA0B,CAAC,UAAU,CAAC;AAC3D,aAAO,QAAQ;AAAA,IAChB;AAAA,IACA,KAAK,MAAM,SAAS;AACnB,UAAI,OAAO,SAAS,SAAU,OAAM,IAAI,UAAU,mDAAmD,OAAO,IAAI,EAAE;AAClH,YAAM,WAAW,YAAY,MAAM;AACnC,cAAQ,EAAE,UAAU,MAAM,UAAU,OAAO,YAAY,aAAa,MAAM,QAAQ,MAAM,QAAQ,EAAE,aAAa,MAAM,UAAU,QAAQ,EAAE,eAAe,EAAE,SAAS,CAAC,IAAI,OAAO;AAAA,IAChL;AAAA,IACA,OAAO,MAAM;AACZ,UAAI,OAAO,SAAS,SAAU,OAAM,IAAI,UAAU,qDAAqD,OAAO,IAAI,EAAE;AACpH,cAAQ,EAAE,YAAY,MAAM,YAAY,QAAQ,CAAC;AAAA,IAClD;AAAA,IACA,OAAO,MAAM,SAAS;AACrB,UAAI,OAAO,SAAS,SAAU,OAAM,IAAI,UAAU,qDAAqD,OAAO,IAAI,EAAE;AACpH,YAAM,WAAW,YAAY,QAAQ;AACrC,cAAQ,EAAE,UAAU,MAAM,UAAU,OAAO,YAAY,aAAa,MAAM,QAAQ,MAAM,QAAQ,EAAE,aAAa,MAAM,UAAU,QAAQ,EAAE,eAAe,EAAE,SAAS,CAAC,IAAI,OAAO;AAAA,IAChL;AAAA,IACA,SAAS,MAAM;AACd,UAAI,OAAO,SAAS,SAAU,OAAM,IAAI,UAAU,uDAAuD,OAAO,IAAI,EAAE;AACtH,cAAQ,EAAE,YAAY,MAAM,YAAY,UAAU,CAAC;AAAA,IACpD;AAAA,IACA,MAAM,aAAa,MAAM;AACxB,aAAO,QAAQ,EAAE,aAAa,MAAM,YAAY,cAAc,GAAG,QAAQ,EAAE,eAAe,EAAE,SAAS;AAAA,IACtG;AAAA,IACA,MAAM,WAAW,MAAM;AACtB,aAAO,QAAQ,EAAE,WAAW,MAAM,YAAY,YAAY,CAAC;AAAA,IAC5D;AAAA,IACA,WAAW,OAAO;AACjB,aAAO,QAAQ,EAAE,WAAW,EAAE,MAAM,CAAC,EAAE;AAAA,IACxC;AAAA,IACA,OAAO,MAAM,WAAW,CAAC,GAAG;AAC3B,aAAO;AAAA,IACR;AAAA,IACA,eAAeN,KAAI;AAClB,aAAO,eAAeA,GAAE;AAAA,IACzB;AAAA,IACA,gBAAgB;AACf,OAAC,GAAG,KAAK,EAAE,QAAQ,EAAE,QAAQ,CAAC,QAAQ,IAAI,UAAU,CAAC;AACrD,aAAO;AAAA,IACR;AAAA,IACA,gBAAgB;AACf,OAAC,GAAG,KAAK,EAAE,QAAQ,EAAE,QAAQ,CAAC,QAAQ,IAAI,UAAU,CAAC;AACrD,aAAO;AAAA,IACR;AAAA,IACA,kBAAkB;AACjB,OAAC,GAAG,KAAK,EAAE,QAAQ,EAAE,QAAQ,CAAC,QAAQ,IAAI,YAAY,CAAC;AACvD,aAAO;AAAA,IACR;AAAA,IACA,WAAW,MAAM,OAAO;AACvB,UAAI,CAAC,aAAa,IAAI,IAAI,EAAG,cAAa,IAAI,MAAM,OAAO,yBAAyB,YAAY,IAAI,CAAC;AACrG,aAAO,eAAe,YAAY,MAAM;AAAA,QACvC;AAAA,QACA,UAAU;AAAA,QACV,cAAc;AAAA,QACd,YAAY;AAAA,MACb,CAAC;AACD,aAAO;AAAA,IACR;AAAA,IACA,QAAQ,MAAM,OAAO;AACpB,UAAI,CAAC,UAAU,IAAI,IAAI,EAAG,WAAU,IAAI,MAAM,QAAQ,IAAI,IAAI,CAAC;AAC/D,UAAI,aAAa,SAAS,IAAI,EAAG,SAAQ,IAAI,IAAI,IAAI,QAAQ,MAAM;AAAA,eAC1D,UAAU,OAAQ,QAAO,QAAQ,IAAI,IAAI;AAAA,UAC7C,SAAQ,IAAI,IAAI,IAAI,OAAO,KAAK;AACrC,aAAO;AAAA,IACR;AAAA,IACA,mBAAmB;AAClB,mBAAa,QAAQ,CAAC,UAAU,SAAS;AACxC,YAAI,CAAC,SAAU,SAAQ,eAAe,YAAY,IAAI;AAAA,YACjD,QAAO,eAAe,YAAY,MAAM,QAAQ;AAAA,MACtD,CAAC;AACD,mBAAa,MAAM;AACnB,aAAO;AAAA,IACR;AAAA,IACA,gBAAgB;AACf,gBAAU,QAAQ,CAAC,UAAU,SAAS;AACrC,YAAI,aAAa,OAAQ,QAAO,QAAQ,IAAI,IAAI;AAAA,YAC3C,SAAQ,IAAI,IAAI,IAAI;AAAA,MAC1B,CAAC;AACD,gBAAU,MAAM;AAChB,aAAO;AAAA,IACR;AAAA,IACA,eAAe;AACd,mBAAa,YAAY,WAAW;AACpC,aAAO;AAAA,IACR;AAAA,IACA,MAAM,uBAAuB;AAC5B,aAAO,wBAAwB;AAAA,IAChC;AAAA,IACA,UAAU,QAAQ;AACjB,UAAI,CAAC,QAAS,WAAU,EAAE,GAAG,YAAY,OAAO;AAChD,aAAO,OAAO,YAAY,QAAQ,MAAM;AAAA,IACzC;AAAA,IACA,cAAc;AACb,UAAI,QAAS,QAAO,OAAO,YAAY,QAAQ,OAAO;AAAA,IACvD;AAAA,EACD;AACA,SAAO;AACR;AACA,IAAM,SAAS,aAAa;AAC5B,IAAM,KAAK;AACX,SAAS,UAAU;AAElB,SAAO,OAAO,sBAAsB,cAAc,oBAAoB,IAAI,MAAM,CAAC,GAAG,EAAE,IAAI,GAAG,MAAM;AAClG,UAAM,IAAI,MAAM,6DAA6D,OAAO,IAAI,CAAC,kBAAkB;AAAA,EAC5G,EAAE,CAAC;AACJ;AACA,SAAS,YAAY,MAAM;AAC1B,QAAM,aAAa,uBAAuB,EAAE,iBAAiB,EAAE,CAAC;AAChE,QAAM,aAAa,WAAW,MAAM,IAAI;AAExC,QAAM,qBAAqB,WAAW,UAAU,CAACO,WAAU;AAC1D,WAAOA,OAAM,SAAS,cAAc,IAAI,EAAE,KAAKA,OAAM,SAAS,GAAG,IAAI,GAAG;AAAA,EACzE,CAAC;AACD,QAAM,QAAQ,iBAAiB,WAAW,qBAAqB,CAAC,CAAC;AACjE,SAAO,OAAO,QAAQ;AACvB;;;AIz6HA,SAAS,oBAAoB;AAC7B,SAAS,UAAU,WAAW,WAAW,YAAY,UAAU,IAAI,cAAc,gBAAgB,OAAO,YAAY;AACpH,YAAYC,WAAU;AAEtB,SAAS,UAAAC,SAAQ,cAAc;AAC/B,OAAO;AACP,OAAO;AAEP,OAAO;AAEP,OAAO;AACP,OAAO;AACP,OAAO;AACP,OAAO;;;ACXP,IAAM,eAAe,OAAO;AAC5B,IAAM,YAAY,IAAI,aAAa,YAAY;AAC/C,OAAO,OAAO,GAAG,GAAG,MAAM,SAAS;AACnC,OAAO,KAAK,MAAM,GAAG,GAAG,MAAM,UAAU,QAAQ,CAAC;", "names": ["getSafeTimers", "expect", "fn", "test", "getSafeTimers", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chai", "Assertion", "util", "assert", "global", "object", "copyPrototypeMethods", "every", "fn", "functionName", "typeDetect", "typeOf", "now", "timers", "getSafeTimers", "stack", "chai", "assert"]}