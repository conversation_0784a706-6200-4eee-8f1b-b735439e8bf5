{"version": 3, "sources": ["../../../../src/runtimes/adapters/attachment/SimpleTextAttachmentAdapter.ts"], "sourcesContent": ["import {\n  CompleteAttachment,\n  PendingAttachment,\n} from \"../../../types/AttachmentTypes\";\nimport { AttachmentAdapter } from \"./AttachmentAdapter\";\n\nexport class SimpleTextAttachmentAdapter implements AttachmentAdapter {\n  public accept =\n    \"text/plain,text/html,text/markdown,text/csv,text/xml,text/json,text/css\";\n\n  public async add(state: { file: File }): Promise<PendingAttachment> {\n    return {\n      id: state.file.name,\n      type: \"document\",\n      name: state.file.name,\n      contentType: state.file.type,\n      file: state.file,\n      status: { type: \"requires-action\", reason: \"composer-send\" },\n    };\n  }\n\n  public async send(\n    attachment: PendingAttachment,\n  ): Promise<CompleteAttachment> {\n    return {\n      ...attachment,\n      status: { type: \"complete\" },\n      content: [\n        {\n          type: \"text\",\n          text: `<attachment name=${attachment.name}>\\n${await getFileText(attachment.file)}\\n</attachment>`,\n        },\n      ],\n    };\n  }\n\n  public async remove() {\n    // noop\n  }\n}\n\nconst getFileText = (file: File) =>\n  new Promise<string>((resolve, reject) => {\n    const reader = new FileReader();\n\n    reader.onload = () => resolve(reader.result as string);\n    reader.onerror = (error) => reject(error);\n\n    reader.readAsText(file);\n  });\n"], "mappings": ";AAMO,IAAM,8BAAN,MAA+D;AAAA,EAC7D,SACL;AAAA,EAEF,MAAa,IAAI,OAAmD;AAClE,WAAO;AAAA,MACL,IAAI,MAAM,KAAK;AAAA,MACf,MAAM;AAAA,MACN,MAAM,MAAM,KAAK;AAAA,MACjB,aAAa,MAAM,KAAK;AAAA,MACxB,MAAM,MAAM;AAAA,MACZ,QAAQ,EAAE,MAAM,mBAAmB,QAAQ,gBAAgB;AAAA,IAC7D;AAAA,EACF;AAAA,EAEA,MAAa,KACX,YAC6B;AAC7B,WAAO;AAAA,MACL,GAAG;AAAA,MACH,QAAQ,EAAE,MAAM,WAAW;AAAA,MAC3B,SAAS;AAAA,QACP;AAAA,UACE,MAAM;AAAA,UACN,MAAM,oBAAoB,WAAW,IAAI;AAAA,EAAM,MAAM,YAAY,WAAW,IAAI,CAAC;AAAA;AAAA,QACnF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAa,SAAS;AAAA,EAEtB;AACF;AAEA,IAAM,cAAc,CAAC,SACnB,IAAI,QAAgB,CAAC,SAAS,WAAW;AACvC,QAAM,SAAS,IAAI,WAAW;AAE9B,SAAO,SAAS,MAAM,QAAQ,OAAO,MAAgB;AACrD,SAAO,UAAU,CAAC,UAAU,OAAO,KAAK;AAExC,SAAO,WAAW,IAAI;AACxB,CAAC;", "names": []}