export type {
  Attachment,
  PendingAttachment,
  CompleteAttachment,
  AttachmentStatus,
} from "./AttachmentTypes";

export type {
  AppendMessage,
  TextMessagePart,
  ReasoningMessagePart,
  SourceMessagePart,
  ImageMessagePart,
  FileMessagePart,
  Unstable_AudioMessagePart,
  ToolCallMessagePart,
  MessageStatus,
  MessagePartStatus,
  ToolCallMessagePartStatus,

  // thread message types
  ThreadUserMessagePart,
  ThreadAssistantMessagePart,
  ThreadSystemMessage,
  ThreadAssistantMessage,
  ThreadUserMessage,
  ThreadMessage,
} from "./AssistantTypes";

// TODO remove in v0.11
export type {
  TextMessagePart as TextContentPart,
  ReasoningMessagePart as ReasoningContentPart,
  SourceMessagePart as SourceContentPart,
  ImageMessagePart as ImageContentPart,
  FileMessagePart as FileContentPart,
  Unstable_AudioMessagePart as AudioContentPart,
  ToolCallMessagePart as ToolCallContentPart,
  MessagePartStatus as ContentPartStatus,
  ToolCallMessagePartStatus as ToolCallContentPartStatus,

  // thread message types
  ThreadUserMessagePart as ThreadUserContentPart,
  ThreadAssistantMessagePart as ThreadAssistantContentPart,
  ThreadSystemMessage as ThreadSystemContentPart,
  ThreadAssistantMessage as ThreadAssistantContent,
  ThreadUserMessage as ThreadUserContent,
  ThreadMessage as ThreadContent,
} from "./AssistantTypes";

export type {
  EmptyMessagePartComponent,
  EmptyMessagePartProps,
  TextMessagePartComponent,
  TextMessagePartProps,
  ReasoningMessagePartComponent,
  ReasoningMessagePartProps,
  SourceMessagePartComponent,
  SourceMessagePartProps,
  ImageMessagePartComponent,
  ImageMessagePartProps,
  FileMessagePartComponent,
  FileMessagePartProps,
  Unstable_AudioMessagePartComponent,
  Unstable_AudioMessagePartProps,
  ToolCallMessagePartComponent,
  ToolCallMessagePartProps,
} from "./MessagePartComponentTypes";

// TODO remove in v0.11
export type {
  EmptyMessagePartComponent as EmptyContentPartComponent,
  EmptyMessagePartProps as EmptyContentPartProps,
  TextMessagePartComponent as TextContentPartComponent,
  TextMessagePartProps as TextContentPartProps,
  ReasoningMessagePartComponent as ReasoningContentPartComponent,
  ReasoningMessagePartProps as ReasoningContentPartProps,
  SourceMessagePartComponent as SourceContentPartComponent,
  SourceMessagePartProps as SourceContentPartProps,
  ImageMessagePartComponent as ImageContentPartComponent,
  ImageMessagePartProps as ImageContentPartProps,
  FileMessagePartComponent as FileContentPartComponent,
  FileMessagePartProps as FileContentPartProps,
  Unstable_AudioMessagePartComponent as AudioContentPartComponent,
  Unstable_AudioMessagePartProps as AudioContentPartProps,
  ToolCallMessagePartComponent as ToolCallContentPartComponent,
  ToolCallMessagePartProps as ToolCallContentPartProps,
} from "./MessagePartComponentTypes";

// Thread list item types
export type { ThreadListItemStatus } from "../api/ThreadListItemRuntime";

export type { Unsubscribe } from "./Unsubscribe";
