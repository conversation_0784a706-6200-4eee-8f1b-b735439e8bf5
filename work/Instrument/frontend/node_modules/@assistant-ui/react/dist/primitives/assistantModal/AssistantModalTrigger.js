// src/primitives/assistantModal/AssistantModalTrigger.tsx
import { forwardRef } from "react";
import * as PopoverPrimitive from "@radix-ui/react-popover";
import { usePopoverScope } from "./scope.js";
import { jsx } from "react/jsx-runtime";
var AssistantModalPrimitiveTrigger = forwardRef(
  ({
    __scopeAssistantModal,
    ...rest
  }, ref) => {
    const scope = usePopoverScope(__scopeAssistantModal);
    return /* @__PURE__ */ jsx(PopoverPrimitive.Trigger, { ...scope, ...rest, ref });
  }
);
AssistantModalPrimitiveTrigger.displayName = "AssistantModalPrimitive.Trigger";
export {
  AssistantModalPrimitiveTrigger
};
//# sourceMappingURL=AssistantModalTrigger.js.map