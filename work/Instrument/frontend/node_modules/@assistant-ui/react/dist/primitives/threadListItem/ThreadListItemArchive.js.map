{"version": 3, "sources": ["../../../src/primitives/threadListItem/ThreadListItemArchive.ts"], "sourcesContent": ["\"use client\";\n\nimport {\n  ActionButtonElement,\n  ActionButtonProps,\n  createActionButton,\n} from \"../../utils/createActionButton\";\nimport { useThreadListItemRuntime } from \"../../context/react/ThreadListItemContext\";\nimport { useCallback } from \"react\";\n\nconst useThreadListItemArchive = () => {\n  const runtime = useThreadListItemRuntime();\n  return useCallback(() => {\n    runtime.archive();\n  }, [runtime]);\n};\n\nexport namespace ThreadListItemPrimitiveArchive {\n  export type Element = ActionButtonElement;\n  export type Props = ActionButtonProps<typeof useThreadListItemArchive>;\n}\n\nexport const ThreadListItemPrimitiveArchive = createActionButton(\n  \"ThreadListItemPrimitive.Archive\",\n  useThreadListItemArchive,\n);\n"], "mappings": ";;;AAEA;AAAA,EAGE;AAAA,OACK;AACP,SAAS,gCAAgC;AACzC,SAAS,mBAAmB;AAE5B,IAAM,2BAA2B,MAAM;AACrC,QAAM,UAAU,yBAAyB;AACzC,SAAO,YAAY,MAAM;AACvB,YAAQ,QAAQ;AAAA,EAClB,GAAG,CAAC,OAAO,CAAC;AACd;AAOO,IAAM,iCAAiC;AAAA,EAC5C;AAAA,EACA;AACF;", "names": []}