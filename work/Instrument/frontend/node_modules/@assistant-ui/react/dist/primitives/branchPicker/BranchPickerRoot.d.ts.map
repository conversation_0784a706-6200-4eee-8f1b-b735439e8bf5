{"version": 3, "file": "BranchPickerRoot.d.ts", "sourceRoot": "", "sources": ["../../../src/primitives/branchPicker/BranchPickerRoot.tsx"], "names": [], "mappings": "AAEA,OAAO,EAAE,SAAS,EAAE,MAAM,2BAA2B,CAAC;AACtD,OAAO,EAAE,KAAK,YAAY,EAAc,wBAAwB,EAAE,MAAM,OAAO,CAAC;AAGhF,yBAAiB,yBAAyB,CAAC;IACzC,KAAY,OAAO,GAAG,YAAY,CAAC,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC;IACzD,KAAY,KAAK,GAAG,wBAAwB,CAAC,OAAO,SAAS,CAAC,GAAG,CAAC,GAAG;QACnE;;;;WAIG;QACH,oBAAoB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;KAC5C,CAAC;CACH;AAED;;;;;;;;;;;;;;;;GAgBG;AACH,eAAO,MAAM,yBAAyB;;;IA1BlC;;;;OAIG;2BACoB,OAAO,GAAG,SAAS;kDA8B5C,CAAC"}