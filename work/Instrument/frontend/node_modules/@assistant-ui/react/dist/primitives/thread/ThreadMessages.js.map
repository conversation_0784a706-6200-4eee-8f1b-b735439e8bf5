{"version": 3, "sources": ["../../../src/primitives/thread/ThreadMessages.tsx"], "sourcesContent": ["\"use client\";\n\nimport { type ComponentType, type FC, memo, useMemo } from \"react\";\nimport { useThread, useThreadRuntime } from \"../../context/react/ThreadContext\";\nimport { MessageRuntimeProvider } from \"../../context/providers/MessageRuntimeProvider\";\nimport { useEditComposer, useMessage } from \"../../context\";\nimport { ThreadMessage as ThreadMessageType } from \"../../types\";\n\nexport namespace ThreadPrimitiveMessages {\n  export type Props = {\n    /**\n     * Component configuration for rendering different types of messages and composers.\n     *\n     * You can provide either:\n     * 1. A single `Message` component that handles all message types\n     * 2. Specific components for `UserMessage` and `AssistantMessage` (with optional `SystemMessage`)\n     *\n     * Optional edit composer components can be provided to customize the editing experience\n     * for different message types when users edit their messages.\n     */\n    components:\n      | {\n          /** Component used to render all message types */\n          Message: ComponentType;\n          /** Component used when editing any message type */\n          EditComposer?: ComponentType | undefined;\n          /** Component used when editing user messages specifically */\n          UserEditComposer?: ComponentType | undefined;\n          /** Component used when editing assistant messages specifically */\n          AssistantEditComposer?: ComponentType | undefined;\n          /** Component used when editing system messages specifically */\n          SystemEditComposer?: ComponentType | undefined;\n          /** Component used to render user messages specifically */\n          UserMessage?: ComponentType | undefined;\n          /** Component used to render assistant messages specifically */\n          AssistantMessage?: ComponentType | undefined;\n          /** Component used to render system messages specifically */\n          SystemMessage?: ComponentType | undefined;\n        }\n      | {\n          /** Component used to render all message types (fallback) */\n          Message?: ComponentType | undefined;\n          /** Component used when editing any message type */\n          EditComposer?: ComponentType | undefined;\n          /** Component used when editing user messages specifically */\n          UserEditComposer?: ComponentType | undefined;\n          /** Component used when editing assistant messages specifically */\n          AssistantEditComposer?: ComponentType | undefined;\n          /** Component used when editing system messages specifically */\n          SystemEditComposer?: ComponentType | undefined;\n          /** Component used to render user messages */\n          UserMessage: ComponentType;\n          /** Component used to render assistant messages */\n          AssistantMessage: ComponentType;\n          /** Component used to render system messages */\n          SystemMessage?: ComponentType | undefined;\n        };\n  };\n}\n\nconst isComponentsSame = (\n  prev: ThreadPrimitiveMessages.Props[\"components\"],\n  next: ThreadPrimitiveMessages.Props[\"components\"],\n) => {\n  return (\n    prev.Message === next.Message &&\n    prev.EditComposer === next.EditComposer &&\n    prev.UserEditComposer === next.UserEditComposer &&\n    prev.AssistantEditComposer === next.AssistantEditComposer &&\n    prev.SystemEditComposer === next.SystemEditComposer &&\n    prev.UserMessage === next.UserMessage &&\n    prev.AssistantMessage === next.AssistantMessage &&\n    prev.SystemMessage === next.SystemMessage\n  );\n};\n\nconst DEFAULT_SYSTEM_MESSAGE = () => null;\n\nconst getComponent = (\n  components: ThreadPrimitiveMessages.Props[\"components\"],\n  role: ThreadMessageType[\"role\"],\n  isEditing: boolean,\n) => {\n  switch (role) {\n    case \"user\":\n      if (isEditing) {\n        return (\n          components.UserEditComposer ??\n          components.EditComposer ??\n          components.UserMessage ??\n          (components.Message as ComponentType)\n        );\n      } else {\n        return components.UserMessage ?? (components.Message as ComponentType);\n      }\n    case \"assistant\":\n      if (isEditing) {\n        return (\n          components.AssistantEditComposer ??\n          components.EditComposer ??\n          components.AssistantMessage ??\n          (components.Message as ComponentType)\n        );\n      } else {\n        return (\n          components.AssistantMessage ?? (components.Message as ComponentType)\n        );\n      }\n    case \"system\":\n      if (isEditing) {\n        return (\n          components.SystemEditComposer ??\n          components.EditComposer ??\n          components.SystemMessage ??\n          (components.Message as ComponentType)\n        );\n      } else {\n        return components.SystemMessage ?? DEFAULT_SYSTEM_MESSAGE;\n      }\n    default:\n      const _exhaustiveCheck: never = role;\n      throw new Error(`Unknown message role: ${_exhaustiveCheck}`);\n  }\n};\n\ntype ThreadMessageComponentProps = {\n  components: ThreadPrimitiveMessages.Props[\"components\"];\n};\n\nconst ThreadMessageComponent: FC<ThreadMessageComponentProps> = ({\n  components,\n}) => {\n  const role = useMessage((m) => m.role);\n  const isEditing = useEditComposer((c) => c.isEditing);\n  const Component = getComponent(components, role, isEditing);\n\n  return <Component />;\n};\nexport namespace ThreadPrimitiveMessageByIndex {\n  export type Props = {\n    index: number;\n    components: ThreadPrimitiveMessages.Props[\"components\"];\n  };\n}\n\n/**\n * Renders a single message at the specified index in the current thread.\n *\n * This component provides message context for a specific message in the thread\n * and renders it using the provided component configuration.\n *\n * @example\n * ```tsx\n * <ThreadPrimitive.MessageByIndex\n *   index={0}\n *   components={{\n *     UserMessage: MyUserMessage,\n *     AssistantMessage: MyAssistantMessage\n *   }}\n * />\n * ```\n */\nexport const ThreadPrimitiveMessageByIndex: FC<ThreadPrimitiveMessageByIndex.Props> =\n  memo(\n    ({ index, components }) => {\n      const threadRuntime = useThreadRuntime();\n      const runtime = useMemo(\n        () => threadRuntime.getMesssageByIndex(index),\n        [threadRuntime, index],\n      );\n\n      return (\n        <MessageRuntimeProvider runtime={runtime}>\n          <ThreadMessageComponent components={components} />\n        </MessageRuntimeProvider>\n      );\n    },\n    (prev, next) =>\n      prev.index === next.index &&\n      isComponentsSame(prev.components, next.components),\n  );\n\nThreadPrimitiveMessageByIndex.displayName = \"ThreadPrimitive.MessageByIndex\";\n\n/**\n * Renders all messages in the current thread using the provided component configuration.\n *\n * This component automatically renders all messages in the thread, providing the appropriate\n * message context for each message. It handles different message types (user, assistant, system)\n * and supports editing mode through the provided edit composer components.\n *\n * @example\n * ```tsx\n * <ThreadPrimitive.Messages\n *   components={{\n *     UserMessage: MyUserMessage,\n *     AssistantMessage: MyAssistantMessage,\n *     EditComposer: MyEditComposer\n *   }}\n * />\n * ```\n */\nexport const ThreadPrimitiveMessagesImpl: FC<ThreadPrimitiveMessages.Props> = ({\n  components,\n}) => {\n  const messagesLength = useThread((t) => t.messages.length);\n\n  const messageElements = useMemo(() => {\n    if (messagesLength === 0) return null;\n    return Array.from({ length: messagesLength }, (_, index) => (\n      <ThreadPrimitiveMessageByIndex\n        key={index}\n        index={index}\n        components={components}\n      />\n    ));\n  }, [messagesLength, components]);\n\n  return messageElements;\n};\n\nThreadPrimitiveMessagesImpl.displayName = \"ThreadPrimitive.Messages\";\n\nexport const ThreadPrimitiveMessages = memo(\n  ThreadPrimitiveMessagesImpl,\n  (prev, next) => isComponentsSame(prev.components, next.components),\n);\n"], "mappings": ";;;AAEA,SAAsC,MAAM,eAAe;AAC3D,SAAS,WAAW,wBAAwB;AAC5C,SAAS,8BAA8B;AACvC,SAAS,iBAAiB,kBAAkB;AAmInC;AA5ET,IAAM,mBAAmB,CACvB,MACA,SACG;AACH,SACE,KAAK,YAAY,KAAK,WACtB,KAAK,iBAAiB,KAAK,gBAC3B,KAAK,qBAAqB,KAAK,oBAC/B,KAAK,0BAA0B,KAAK,yBACpC,KAAK,uBAAuB,KAAK,sBACjC,KAAK,gBAAgB,KAAK,eAC1B,KAAK,qBAAqB,KAAK,oBAC/B,KAAK,kBAAkB,KAAK;AAEhC;AAEA,IAAM,yBAAyB,MAAM;AAErC,IAAM,eAAe,CACnB,YACA,MACA,cACG;AACH,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,UAAI,WAAW;AACb,eACE,WAAW,oBACX,WAAW,gBACX,WAAW,eACV,WAAW;AAAA,MAEhB,OAAO;AACL,eAAO,WAAW,eAAgB,WAAW;AAAA,MAC/C;AAAA,IACF,KAAK;AACH,UAAI,WAAW;AACb,eACE,WAAW,yBACX,WAAW,gBACX,WAAW,oBACV,WAAW;AAAA,MAEhB,OAAO;AACL,eACE,WAAW,oBAAqB,WAAW;AAAA,MAE/C;AAAA,IACF,KAAK;AACH,UAAI,WAAW;AACb,eACE,WAAW,sBACX,WAAW,gBACX,WAAW,iBACV,WAAW;AAAA,MAEhB,OAAO;AACL,eAAO,WAAW,iBAAiB;AAAA,MACrC;AAAA,IACF;AACE,YAAM,mBAA0B;AAChC,YAAM,IAAI,MAAM,yBAAyB,gBAAgB,EAAE;AAAA,EAC/D;AACF;AAMA,IAAM,yBAA0D,CAAC;AAAA,EAC/D;AACF,MAAM;AACJ,QAAM,OAAO,WAAW,CAAC,MAAM,EAAE,IAAI;AACrC,QAAM,YAAY,gBAAgB,CAAC,MAAM,EAAE,SAAS;AACpD,QAAM,YAAY,aAAa,YAAY,MAAM,SAAS;AAE1D,SAAO,oBAAC,aAAU;AACpB;AAyBO,IAAM,gCACX;AAAA,EACE,CAAC,EAAE,OAAO,WAAW,MAAM;AACzB,UAAM,gBAAgB,iBAAiB;AACvC,UAAM,UAAU;AAAA,MACd,MAAM,cAAc,mBAAmB,KAAK;AAAA,MAC5C,CAAC,eAAe,KAAK;AAAA,IACvB;AAEA,WACE,oBAAC,0BAAuB,SACtB,8BAAC,0BAAuB,YAAwB,GAClD;AAAA,EAEJ;AAAA,EACA,CAAC,MAAM,SACL,KAAK,UAAU,KAAK,SACpB,iBAAiB,KAAK,YAAY,KAAK,UAAU;AACrD;AAEF,8BAA8B,cAAc;AAoBrC,IAAM,8BAAiE,CAAC;AAAA,EAC7E;AACF,MAAM;AACJ,QAAM,iBAAiB,UAAU,CAAC,MAAM,EAAE,SAAS,MAAM;AAEzD,QAAM,kBAAkB,QAAQ,MAAM;AACpC,QAAI,mBAAmB,EAAG,QAAO;AACjC,WAAO,MAAM,KAAK,EAAE,QAAQ,eAAe,GAAG,CAAC,GAAG,UAChD;AAAA,MAAC;AAAA;AAAA,QAEC;AAAA,QACA;AAAA;AAAA,MAFK;AAAA,IAGP,CACD;AAAA,EACH,GAAG,CAAC,gBAAgB,UAAU,CAAC;AAE/B,SAAO;AACT;AAEA,4BAA4B,cAAc;AAEnC,IAAM,0BAA0B;AAAA,EACrC;AAAA,EACA,CAAC,MAAM,SAAS,iBAAiB,KAAK,YAAY,KAAK,UAAU;AACnE;", "names": []}