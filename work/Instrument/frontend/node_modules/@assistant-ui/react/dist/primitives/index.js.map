{"version": 3, "sources": ["../../src/primitives/index.ts"], "sourcesContent": ["export * as ActionBarPrimitive from \"./actionBar\";\nexport * as AssistantModalPrimitive from \"./assistantModal\";\nexport * as AttachmentPrimitive from \"./attachment\";\nexport * as BranchPickerPrimitive from \"./branchPicker\";\nexport * as ComposerPrimitive from \"./composer\";\nexport * as MessagePartPrimitive from \"./messagePart\";\nexport * as ErrorPrimitive from \"./error\";\nexport * as MessagePrimitive from \"./message\";\nexport * as ThreadPrimitive from \"./thread\";\nexport * as ThreadListPrimitive from \"./threadList\";\nexport * as ThreadListItemPrimitive from \"./threadListItem\";\n\nexport { useMessagePartText } from \"./messagePart/useMessagePartText\";\nexport { useMessagePartReasoning } from \"./messagePart/useMessagePartReasoning\";\nexport { useMessagePartSource } from \"./messagePart/useMessagePartSource\";\nexport { useMessagePartFile } from \"./messagePart/useMessagePartFile\";\nexport { useMessagePartImage } from \"./messagePart/useMessagePartImage\";\nexport { useThreadViewportAutoScroll } from \"./thread/useThreadViewportAutoScroll\";\n\n// TODO remove in v0.11\nexport * as ContentPartPrimitive from \"./messagePart\";\nexport { useMessagePartText as useContentPartText } from \"./messagePart/useMessagePartText\";\nexport { useMessagePartReasoning as useContentPartReasoning } from \"./messagePart/useMessagePartReasoning\";\nexport { useMessagePartSource as useContentPartSource } from \"./messagePart/useMessagePartSource\";\nexport { useMessagePartFile as useContentPartFile } from \"./messagePart/useMessagePartFile\";\nexport { useMessagePartImage as useContentPartImage } from \"./messagePart/useMessagePartImage\";\n"], "mappings": ";AAAA,YAAY,wBAAwB;AACpC,YAAY,6BAA6B;AACzC,YAAY,yBAAyB;AACrC,YAAY,2BAA2B;AACvC,YAAY,uBAAuB;AACnC,YAAY,0BAA0B;AACtC,YAAY,oBAAoB;AAChC,YAAY,sBAAsB;AAClC,YAAY,qBAAqB;AACjC,YAAY,yBAAyB;AACrC,YAAY,6BAA6B;AAEzC,SAAS,0BAA0B;AACnC,SAAS,+BAA+B;AACxC,SAAS,4BAA4B;AACrC,SAAS,0BAA0B;AACnC,SAAS,2BAA2B;AACpC,SAAS,mCAAmC;AAG5C,YAAY,0BAA0B;AACtC,SAA+B,sBAAtBA,2BAAgD;AACzD,SAAoC,2BAA3BC,gCAA0D;AACnE,SAAiC,wBAAxBC,6BAAoD;AAC7D,SAA+B,sBAAtBC,2BAAgD;AACzD,SAAgC,uBAAvBC,4BAAkD;", "names": ["useMessagePartText", "useMessagePartReasoning", "useMessagePartSource", "useMessagePartFile", "useMessagePartImage"]}