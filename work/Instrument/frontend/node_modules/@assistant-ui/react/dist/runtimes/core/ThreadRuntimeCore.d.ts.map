{"version": 3, "file": "ThreadRuntimeCore.d.ts", "sourceRoot": "", "sources": ["../../../src/runtimes/core/ThreadRuntimeCore.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,wBAAwB,CAAC;AAC3D,OAAO,EAAE,YAAY,EAAE,MAAM,qBAAqB,CAAC;AACnD,OAAO,EAAE,aAAa,EAAE,aAAa,EAAE,MAAM,aAAa,CAAC;AAC3D,OAAO,EAAE,SAAS,EAAE,MAAM,4BAA4B,CAAC;AACvD,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,yBAAyB,CAAC;AAC3D,OAAO,EAAE,sBAAsB,EAAE,MAAM,uCAAuC,CAAC;AAC/E,OAAO,EAAE,mBAAmB,EAAE,kBAAkB,EAAE,MAAM,UAAU,CAAC;AACnE,OAAO,EAAE,yBAAyB,EAAE,MAAM,4BAA4B,CAAC;AACvE,OAAO,EAAE,iBAAiB,EAAE,MAAM,mBAAmB,CAAC;AACtD,OAAO,EACL,mBAAmB,EACnB,yBAAyB,EAC1B,MAAM,uBAAuB,CAAC;AAE/B,MAAM,MAAM,mBAAmB,GAAG;IAChC,QAAQ,CAAC,cAAc,EAAE,OAAO,CAAC;IACjC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC;IACvB,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC;IACzB,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC;IACzB,QAAQ,CAAC,aAAa,EAAE,OAAO,CAAC;IAChC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC;IACzB,QAAQ,CAAC,WAAW,EAAE,OAAO,CAAC;IAC9B,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC;CAC5B,CAAC;AAEF,MAAM,MAAM,oBAAoB,GAAG;IACjC,SAAS,EAAE,MAAM,CAAC;IAClB,QAAQ,EAAE,MAAM,CAAC;IACjB,UAAU,EAAE,MAAM,CAAC;IACnB,MAAM,EAAE,iBAAiB,CAAC;IAC1B,OAAO,EAAE,OAAO,CAAC;IACjB,QAAQ,CAAC,EAAE,iBAAiB,GAAG,SAAS,CAAC;CAC1C,CAAC;AAEF,MAAM,MAAM,qBAAqB,GAAG;IAClC,SAAS,EAAE,MAAM,CAAC;IAClB,IAAI,EAAE,UAAU,GAAG,UAAU,CAAC;CAC/B,CAAC;AAEF,MAAM,MAAM,gBAAgB,GAAG;IAC7B,MAAM,EAAE,MAAM,CAAC;CAChB,CAAC;AAEF,MAAM,MAAM,WAAW,GAAG;IACxB,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC;IAC3B,QAAQ,CAAC,MAAM,EAAE,sBAAsB,CAAC,MAAM,CAAC;CAChD,CAAC;AAEF,MAAM,MAAM,iBAAiB,GAAG;IAC9B,QAAQ,CAAC,IAAI,EAAE,UAAU,GAAG,UAAU,CAAC;CACxC,CAAC;AAEF,MAAM,MAAM,sBAAsB,GAC9B,WAAW,GACX,SAAS,GACT,YAAY,GACZ,sBAAsB,CAAC;AAE3B,MAAM,MAAM,cAAc,GAAG;IAC3B,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IACxB,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IACxB,SAAS,EAAE,SAAS,CAAC;CACtB,CAAC;AAEF,MAAM,MAAM,eAAe,GAAG,cAAc,GAAG;IAC7C,MAAM,EAAE,CACN,OAAO,EAAE,mBAAmB,KACzB,cAAc,CAAC,kBAAkB,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;CACxD,CAAC;AAEF,MAAM,MAAM,iBAAiB,GAAG,QAAQ,CAAC;IACvC,cAAc,EAAE,CAAC,SAAS,EAAE,MAAM,KAC9B;QACE,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;QACxB,OAAO,EAAE,aAAa,CAAC;KACxB,GACD,SAAS,CAAC;IAEd,WAAW,EAAE,CAAC,SAAS,EAAE,MAAM,KAAK,SAAS,MAAM,EAAE,CAAC;IACtD,cAAc,EAAE,CAAC,QAAQ,EAAE,MAAM,KAAK,IAAI,CAAC;IAE3C,MAAM,EAAE,CAAC,OAAO,EAAE,aAAa,KAAK,IAAI,CAAC;IACzC,QAAQ,EAAE,CAAC,MAAM,EAAE,cAAc,KAAK,IAAI,CAAC;IAC3C,SAAS,EAAE,CAAC,MAAM,EAAE,eAAe,KAAK,IAAI,CAAC;IAC7C,SAAS,EAAE,MAAM,IAAI,CAAC;IAEtB,aAAa,EAAE,CAAC,OAAO,EAAE,oBAAoB,KAAK,IAAI,CAAC;IAEvD,KAAK,EAAE,CAAC,SAAS,EAAE,MAAM,KAAK,IAAI,CAAC;IACnC,YAAY,EAAE,MAAM,IAAI,CAAC;IAEzB,oBAAoB,EAAE,CAAC,SAAS,EAAE,MAAM,KAAK,iBAAiB,GAAG,SAAS,CAAC;IAC3E,cAAc,EAAE,CAAC,QAAQ,EAAE,qBAAqB,KAAK,IAAI,CAAC;IAE1D,eAAe,EAAE,MAAM,YAAY,CAAC;IAEpC,QAAQ,EAAE,yBAAyB,CAAC;IACpC,eAAe,EAAE,CAAC,SAAS,EAAE,MAAM,KAAK,mBAAmB,GAAG,SAAS,CAAC;IACxE,SAAS,EAAE,CAAC,SAAS,EAAE,MAAM,KAAK,IAAI,CAAC;IAEvC,MAAM,EAAE,WAAW,GAAG,SAAS,CAAC;IAEhC,YAAY,EAAE,QAAQ,CAAC,mBAAmB,CAAC,CAAC;IAC5C,UAAU,EAAE,OAAO,CAAC;IACpB,SAAS,EAAE,OAAO,CAAC;IACnB,QAAQ,EAAE,SAAS,aAAa,EAAE,CAAC;IACnC,KAAK,EAAE,iBAAiB,CAAC;IACzB,WAAW,EAAE,SAAS,gBAAgB,EAAE,CAAC;IAOzC,MAAM,EAAE,OAAO,CAAC;IAEhB,SAAS,EAAE,CAAC,QAAQ,EAAE,MAAM,IAAI,KAAK,WAAW,CAAC;IAEjD,MAAM,CAAC,UAAU,EAAE,yBAAyB,GAAG,IAAI,CAAC;IACpD,MAAM,IAAI,yBAAyB,CAAC;IAEpC,KAAK,CAAC,eAAe,CAAC,EAAE,SAAS,iBAAiB,EAAE,GAAG,IAAI,CAAC;IAE5D,WAAW,CAAC,KAAK,EAAE,sBAAsB,EAAE,QAAQ,EAAE,MAAM,IAAI,GAAG,WAAW,CAAC;CAC/E,CAAC,CAAC"}