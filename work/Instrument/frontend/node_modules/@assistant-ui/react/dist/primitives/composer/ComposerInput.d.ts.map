{"version": 3, "file": "ComposerInput.d.ts", "sourceRoot": "", "sources": ["../../../src/primitives/composer/ComposerInput.tsx"], "names": [], "mappings": "AAaA,OAAyB,EACvB,KAAK,qBAAqB,EAC3B,MAAM,yBAAyB,CAAC;AAUjC,yBAAiB,sBAAsB,CAAC;IACtC,KAAY,OAAO,GAAG,mBAAmB,CAAC;IAC1C,KAAY,KAAK,GAAG,qBAAqB,GAAG;QAC1C;;;WAGG;QACH,OAAO,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;QAC9B;;;WAGG;QACH,aAAa,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;QACpC;;;WAGG;QACH,cAAc,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;QACrC;;;WAGG;QACH,wBAAwB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;QAC/C;;;WAGG;QACH,8BAA8B,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;QACrD;;;WAGG;QACH,8BAA8B,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;QACrD;;;WAGG;QACH,oBAAoB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;KAC5C,CAAC;CACH;AAED;;;;;;;;;;;;;;;GAeG;AACH,eAAO,MAAM,sBAAsB;IAtD/B;;;OAGG;cACO,OAAO,GAAG,SAAS;IAC7B;;;OAGG;oBACa,OAAO,GAAG,SAAS;IACnC;;;OAGG;qBACc,OAAO,GAAG,SAAS;IACpC;;;OAGG;+BACwB,OAAO,GAAG,SAAS;IAC9C;;;OAGG;qCAC8B,OAAO,GAAG,SAAS;IACpD;;;OAGG;qCAC8B,OAAO,GAAG,SAAS;IACpD;;;OAGG;2BACoB,OAAO,GAAG,SAAS;uDAwJ7C,CAAC"}