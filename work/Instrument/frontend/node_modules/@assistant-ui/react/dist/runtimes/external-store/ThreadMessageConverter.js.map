{"version": 3, "sources": ["../../../src/runtimes/external-store/ThreadMessageConverter.ts"], "sourcesContent": ["import { ThreadMessage } from \"../../types\";\n\nexport type ConverterCallback<TIn> = (\n  cache: ThreadMessage | undefined,\n  message: TIn,\n  idx: number,\n) => ThreadMessage;\n\nexport class ThreadMessageConverter {\n  private readonly cache = new WeakMap<WeakKey, ThreadMessage>();\n\n  convertMessages<TIn extends WeakKey>(\n    messages: readonly TIn[],\n    converter: ConverterCallback<TIn>,\n  ): ThreadMessage[] {\n    return messages.map((m, idx) => {\n      const cached = this.cache.get(m);\n      const newMessage = converter(cached, m, idx);\n      this.cache.set(m, newMessage);\n      return newMessage;\n    });\n  }\n}\n"], "mappings": ";AAQO,IAAM,yBAAN,MAA6B;AAAA,EACjB,QAAQ,oBAAI,QAAgC;AAAA,EAE7D,gBACE,UACA,WACiB;AACjB,WAAO,SAAS,IAAI,CAAC,GAAG,QAAQ;AAC9B,YAAM,SAAS,KAAK,MAAM,IAAI,CAAC;AAC/B,YAAM,aAAa,UAAU,QAAQ,GAAG,GAAG;AAC3C,WAAK,MAAM,IAAI,GAAG,UAAU;AAC5B,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AACF;", "names": []}