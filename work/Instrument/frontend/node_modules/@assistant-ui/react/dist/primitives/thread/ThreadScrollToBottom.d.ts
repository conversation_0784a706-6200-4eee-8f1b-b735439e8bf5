import { ActionButtonElement, ActionButtonProps } from "../../utils/createActionButton";
declare const useThreadScrollToBottom: () => (() => void) | null;
export declare namespace ThreadPrimitiveScrollToBottom {
    type Element = ActionButtonElement;
    type Props = ActionButtonProps<typeof useThreadScrollToBottom>;
}
export declare const ThreadPrimitiveScrollToBottom: import("react").ForwardRefExoticComponent<Omit<import("react").ClassAttributes<HTMLButtonElement> & import("react").ButtonHTMLAttributes<HTMLButtonElement> & {
    asChild?: boolean;
}, "ref"> & import("react").RefAttributes<HTMLButtonElement>>;
export {};
//# sourceMappingURL=ThreadScrollToBottom.d.ts.map