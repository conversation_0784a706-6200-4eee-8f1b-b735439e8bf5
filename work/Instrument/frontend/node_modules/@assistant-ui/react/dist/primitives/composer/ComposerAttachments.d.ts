import { ComponentType, type FC } from "react";
export declare namespace ComposerPrimitiveAttachments {
    type Props = {
        components: {
            Image?: ComponentType | undefined;
            Document?: ComponentType | undefined;
            File?: ComponentType | undefined;
            Attachment?: ComponentType | undefined;
        } | undefined;
    };
}
export declare namespace ComposerPrimitiveAttachmentByIndex {
    type Props = {
        index: number;
        components?: ComposerPrimitiveAttachments.Props["components"];
    };
}
/**
 * Renders a single attachment at the specified index within the composer.
 *
 * This component provides direct access to render a specific attachment
 * from the composer's attachment list using the provided component configuration.
 *
 * @example
 * ```tsx
 * <ComposerPrimitive.AttachmentByIndex
 *   index={0}
 *   components={{
 *     Image: MyImageAttachment,
 *     Document: MyDocumentAttachment
 *   }}
 * />
 * ```
 */
export declare const ComposerPrimitiveAttachmentByIndex: FC<ComposerPrimitiveAttachmentByIndex.Props>;
export declare const ComposerPrimitiveAttachments: FC<ComposerPrimitiveAttachments.Props>;
//# sourceMappingURL=ComposerAttachments.d.ts.map