{"version": 3, "sources": ["../../../../src/runtimes/adapters/attachment/SimpleImageAttachmentAdapter.ts"], "sourcesContent": ["import {\n  PendingAttachment,\n  CompleteAttachment,\n} from \"../../../types/AttachmentTypes\";\nimport { AttachmentAdapter } from \"./AttachmentAdapter\";\n\nexport class SimpleImageAttachmentAdapter implements AttachmentAdapter {\n  public accept = \"image/*\";\n\n  public async add(state: { file: File }): Promise<PendingAttachment> {\n    return {\n      id: state.file.name,\n      type: \"image\",\n      name: state.file.name,\n      contentType: state.file.type,\n      file: state.file,\n      status: { type: \"requires-action\", reason: \"composer-send\" },\n    };\n  }\n\n  public async send(\n    attachment: PendingAttachment,\n  ): Promise<CompleteAttachment> {\n    return {\n      ...attachment,\n      status: { type: \"complete\" },\n      content: [\n        {\n          type: \"image\",\n          image: await getFileDataURL(attachment.file),\n        },\n      ],\n    };\n  }\n\n  public async remove() {\n    // noop\n  }\n}\n\nconst getFileDataURL = (file: File) =>\n  new Promise<string>((resolve, reject) => {\n    const reader = new FileReader();\n\n    reader.onload = () => resolve(reader.result as string);\n    reader.onerror = (error) => reject(error);\n\n    reader.readAsDataURL(file);\n  });\n"], "mappings": ";AAMO,IAAM,+BAAN,MAAgE;AAAA,EAC9D,SAAS;AAAA,EAEhB,MAAa,IAAI,OAAmD;AAClE,WAAO;AAAA,MACL,IAAI,MAAM,KAAK;AAAA,MACf,MAAM;AAAA,MACN,MAAM,MAAM,KAAK;AAAA,MACjB,aAAa,MAAM,KAAK;AAAA,MACxB,MAAM,MAAM;AAAA,MACZ,QAAQ,EAAE,MAAM,mBAAmB,QAAQ,gBAAgB;AAAA,IAC7D;AAAA,EACF;AAAA,EAEA,MAAa,KACX,YAC6B;AAC7B,WAAO;AAAA,MACL,GAAG;AAAA,MACH,QAAQ,EAAE,MAAM,WAAW;AAAA,MAC3B,SAAS;AAAA,QACP;AAAA,UACE,MAAM;AAAA,UACN,OAAO,MAAM,eAAe,WAAW,IAAI;AAAA,QAC7C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAa,SAAS;AAAA,EAEtB;AACF;AAEA,IAAM,iBAAiB,CAAC,SACtB,IAAI,QAAgB,CAAC,SAAS,WAAW;AACvC,QAAM,SAAS,IAAI,WAAW;AAE9B,SAAO,SAAS,MAAM,QAAQ,OAAO,MAAgB;AACrD,SAAO,UAAU,CAAC,UAAU,OAAO,KAAK;AAExC,SAAO,cAAc,IAAI;AAC3B,CAAC;", "names": []}