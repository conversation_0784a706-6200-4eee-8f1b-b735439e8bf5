{"version": 3, "sources": ["../../../src/primitives/message/MessageAttachments.tsx"], "sourcesContent": ["\"use client\";\n\nimport { ComponentType, type FC, memo, useMemo } from \"react\";\nimport { useMessage, useMessageRuntime } from \"../../context\";\nimport { useMessageAttachment } from \"../../context/react/AttachmentContext\";\nimport { AttachmentRuntimeProvider } from \"../../context/providers/AttachmentRuntimeProvider\";\nimport { CompleteAttachment } from \"../../types\";\n\nexport namespace MessagePrimitiveAttachments {\n  export type Props = {\n    components:\n      | {\n          Image?: ComponentType | undefined;\n          Document?: ComponentType | undefined;\n          File?: ComponentType | undefined;\n          Attachment?: ComponentType | undefined;\n        }\n      | undefined;\n  };\n}\n\nconst getComponent = (\n  components: MessagePrimitiveAttachments.Props[\"components\"],\n  attachment: CompleteAttachment,\n) => {\n  const type = attachment.type;\n  switch (type) {\n    case \"image\":\n      return components?.Image ?? components?.Attachment;\n    case \"document\":\n      return components?.Document ?? components?.Attachment;\n    case \"file\":\n      return components?.File ?? components?.Attachment;\n    default:\n      const _exhaustiveCheck: never = type;\n      throw new Error(`Unknown attachment type: ${_exhaustiveCheck}`);\n  }\n};\n\nconst AttachmentComponent: FC<{\n  components: MessagePrimitiveAttachments.Props[\"components\"];\n}> = ({ components }) => {\n  const Component = useMessageAttachment((a) => getComponent(components, a));\n\n  if (!Component) return null;\n  return <Component />;\n};\n\nexport namespace MessagePrimitiveAttachmentByIndex {\n  export type Props = {\n    index: number;\n    components?: MessagePrimitiveAttachments.Props[\"components\"];\n  };\n}\n\n/**\n * Renders a single attachment at the specified index within the current message.\n *\n * This component provides direct access to render a specific attachment\n * from the message's attachment list using the provided component configuration.\n *\n * @example\n * ```tsx\n * <MessagePrimitive.AttachmentByIndex\n *   index={0}\n *   components={{\n *     Image: MyImageAttachment,\n *     Document: MyDocumentAttachment\n *   }}\n * />\n * ```\n */\nexport const MessagePrimitiveAttachmentByIndex: FC<MessagePrimitiveAttachmentByIndex.Props> =\n  memo(\n    ({ index, components }) => {\n      const messageRuntime = useMessageRuntime();\n      const runtime = useMemo(\n        () => messageRuntime.getAttachmentByIndex(index),\n        [messageRuntime, index],\n      );\n\n      return (\n        <AttachmentRuntimeProvider runtime={runtime}>\n          <AttachmentComponent components={components} />\n        </AttachmentRuntimeProvider>\n      );\n    },\n    (prev, next) =>\n      prev.index === next.index &&\n      prev.components?.Image === next.components?.Image &&\n      prev.components?.Document === next.components?.Document &&\n      prev.components?.File === next.components?.File &&\n      prev.components?.Attachment === next.components?.Attachment,\n  );\n\nMessagePrimitiveAttachmentByIndex.displayName =\n  \"MessagePrimitive.AttachmentByIndex\";\n\nexport const MessagePrimitiveAttachments: FC<\n  MessagePrimitiveAttachments.Props\n> = ({ components }) => {\n  const attachmentsCount = useMessage((message) => {\n    if (message.role !== \"user\") return 0;\n    return message.attachments.length;\n  });\n\n  const attachmentElements = useMemo(() => {\n    return Array.from({ length: attachmentsCount }, (_, index) => (\n      <MessagePrimitiveAttachmentByIndex\n        key={index}\n        index={index}\n        components={components}\n      />\n    ));\n  }, [attachmentsCount, components]);\n\n  return attachmentElements;\n};\n\nMessagePrimitiveAttachments.displayName = \"MessagePrimitive.Attachments\";\n"], "mappings": ";;;AAEA,SAAiC,MAAM,eAAe;AACtD,SAAS,YAAY,yBAAyB;AAC9C,SAAS,4BAA4B;AACrC,SAAS,iCAAiC;AAwCjC;AAxBT,IAAM,eAAe,CACnB,YACA,eACG;AACH,QAAM,OAAO,WAAW;AACxB,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,aAAO,YAAY,SAAS,YAAY;AAAA,IAC1C,KAAK;AACH,aAAO,YAAY,YAAY,YAAY;AAAA,IAC7C,KAAK;AACH,aAAO,YAAY,QAAQ,YAAY;AAAA,IACzC;AACE,YAAM,mBAA0B;AAChC,YAAM,IAAI,MAAM,4BAA4B,gBAAgB,EAAE;AAAA,EAClE;AACF;AAEA,IAAM,sBAED,CAAC,EAAE,WAAW,MAAM;AACvB,QAAM,YAAY,qBAAqB,CAAC,MAAM,aAAa,YAAY,CAAC,CAAC;AAEzE,MAAI,CAAC,UAAW,QAAO;AACvB,SAAO,oBAAC,aAAU;AACpB;AA0BO,IAAM,oCACX;AAAA,EACE,CAAC,EAAE,OAAO,WAAW,MAAM;AACzB,UAAM,iBAAiB,kBAAkB;AACzC,UAAM,UAAU;AAAA,MACd,MAAM,eAAe,qBAAqB,KAAK;AAAA,MAC/C,CAAC,gBAAgB,KAAK;AAAA,IACxB;AAEA,WACE,oBAAC,6BAA0B,SACzB,8BAAC,uBAAoB,YAAwB,GAC/C;AAAA,EAEJ;AAAA,EACA,CAAC,MAAM,SACL,KAAK,UAAU,KAAK,SACpB,KAAK,YAAY,UAAU,KAAK,YAAY,SAC5C,KAAK,YAAY,aAAa,KAAK,YAAY,YAC/C,KAAK,YAAY,SAAS,KAAK,YAAY,QAC3C,KAAK,YAAY,eAAe,KAAK,YAAY;AACrD;AAEF,kCAAkC,cAChC;AAEK,IAAM,8BAET,CAAC,EAAE,WAAW,MAAM;AACtB,QAAM,mBAAmB,WAAW,CAAC,YAAY;AAC/C,QAAI,QAAQ,SAAS,OAAQ,QAAO;AACpC,WAAO,QAAQ,YAAY;AAAA,EAC7B,CAAC;AAED,QAAM,qBAAqB,QAAQ,MAAM;AACvC,WAAO,MAAM,KAAK,EAAE,QAAQ,iBAAiB,GAAG,CAAC,GAAG,UAClD;AAAA,MAAC;AAAA;AAAA,QAEC;AAAA,QACA;AAAA;AAAA,MAFK;AAAA,IAGP,CACD;AAAA,EACH,GAAG,CAAC,kBAAkB,UAAU,CAAC;AAEjC,SAAO;AACT;AAEA,4BAA4B,cAAc;", "names": []}