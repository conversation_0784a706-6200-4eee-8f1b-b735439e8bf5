# The Instrument 📸🤖

A discreet, efficient system for optically capturing textual or visual content from a computer screen in real-time, with AI-powered analysis using Claude.

## Overview

The Instrument creates a seamless workflow for capturing screen content via an iPhone camera and analyzing it with Claude AI. The system consists of:

- **Backend Server** (Node.js/Express): Handles device discovery, WebSocket communication, and Claude API integration
- **Frontend Web App** (React): Provides an intuitive control interface and chat-based AI analysis
- **iOS Capture App** (separate): Captures screen content and sends images to the backend

## Features

- 🔍 **Automatic Device Discovery**: Uses Bonjour to find and connect to iOS capture device
- 📡 **Real-time Communication**: WebSocket-based bidirectional communication
- 🤖 **AI Analysis**: Streaming responses from <PERSON> for captured content analysis
- 💬 **Chat Interface**: Interactive conversation with AI about captured content
- 🎨 **Modern UI**: Beautiful, responsive interface with floating controls
- 🔒 **Localhost Only**: Secure, privacy-focused local operation

## Prerequisites

- Node.js 18+ installed on your MacBook
- Anthropic API key (get one at https://console.anthropic.com/)
- iPhone and MacBook on the same Wi-Fi network
- iOS capture app (implementation separate)

## Quick Start

### 1. <PERSON><PERSON> and Setup

```bash
# Navigate to your project directory
cd /path/to/your/project

# Install dependencies for all components
npm run install-all
```

### 2. Configure Environment

```bash
# Copy the example environment file
cp .env.example .env

# Edit .env and add your Anthropic API key
# ANTHROPIC_API_KEY=your_api_key_here
```

### 3. Start the Application

```bash
# Start both backend and frontend simultaneously
npm run dev
```

This will start:
- Backend server on http://localhost:3000
- Frontend dev server on http://localhost:5173
- WebSocket server on ws://localhost:8081

### 4. Access the Interface

Open your browser and navigate to http://localhost:5173

## Usage

### Basic Workflow

1. **Device Connection**: The app automatically discovers and connects to your iOS capture device
2. **Capture**: Click the "📸 Capture" button to take a screenshot via the iOS app
3. **Send**: Click the "📤 Send" button to transfer captured images to the web app
4. **Analyze**: Add context (optional) and click "🧠 Analyze" to get AI insights
5. **Chat**: Continue the conversation with follow-up questions

### Interface Elements

- **Status Bar** (top-left): Shows device connection status
- **Capture Button** (top-center): Triggers image capture on iOS device
- **Send Button** (top-right): Requests image transfer from iOS device
- **Chat Interface** (center): Main area for AI conversation
- **Context Input**: Add specific questions or context for analysis
- **Control Buttons**: Analyze, Stop, New Chat

### AI Analysis Features

- **Text Extraction**: Reads and transcribes text from screenshots
- **Code Analysis**: Understands and explains code snippets
- **Visual Description**: Describes UI elements, diagrams, and layouts
- **Contextual Understanding**: Answers specific questions about captured content
- **Streaming Responses**: Real-time AI responses as they're generated

## Configuration

### Environment Variables

```bash
# API Configuration
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Server Ports
BACKEND_PORT=3000
WEBSOCKET_PORT=8081

# Device Discovery
DEVICE_SERVICE_NAME=_instrument._tcp
DEVICE_DISCOVERY_TIMEOUT=30000

# AI Configuration
CLAUDE_MODEL=claude-3-5-sonnet-20241022
MAX_IMAGE_SIZE=50000000
STREAM_TIMEOUT=30000
```

### Customization

- **AI Model**: Change `CLAUDE_MODEL` to use different Claude variants
- **Image Limits**: Adjust `MAX_IMAGE_SIZE` for larger/smaller image support
- **Timeouts**: Modify discovery and streaming timeouts as needed

## Development

### Project Structure

```
instrument-web-app/
├── backend/                 # Node.js server
│   ├── server.js           # Main server file
│   └── package.json        # Backend dependencies
├── frontend/               # React application
│   ├── src/
│   │   ├── App.jsx        # Main React component
│   │   ├── App.css        # Application styles
│   │   └── index.css      # Global styles
│   └── package.json       # Frontend dependencies
├── .env.example           # Environment template
├── .gitignore            # Git ignore rules
└── package.json          # Root package file
```

### Running Components Separately

```bash
# Backend only
cd backend && npm run dev

# Frontend only
cd frontend && npm run dev
```

### API Endpoints

- `GET /api/status` - Device connection status
- `POST /api/chat` - AI analysis with streaming response

### WebSocket Events

- `device_status` - Connection status updates
- `images` - Image data from iOS device
- `capture` - Trigger capture command
- `send` - Request image transfer

## Troubleshooting

### Common Issues

1. **Device Not Found**
   - Ensure iPhone and MacBook are on same Wi-Fi
   - Check iOS app is running and advertising service
   - Verify service name matches in configuration

2. **API Errors**
   - Verify Anthropic API key is correct
   - Check API key has sufficient credits
   - Ensure internet connection for API calls

3. **WebSocket Connection Issues**
   - Check firewall settings
   - Verify ports 3000 and 8081 are available
   - Restart the application

4. **Image Transfer Problems**
   - Check image size limits
   - Verify base64 encoding is correct
   - Monitor console for error messages

### Debug Mode

Enable detailed logging by setting environment variables:

```bash
DEBUG=instrument:* npm run dev
```

## Security Notes

- The application runs localhost-only for privacy
- API keys are stored in environment files (not in code)
- No external network access except for Claude API
- Images are processed locally and not stored permanently

## License

MIT License - see LICENSE file for details

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

---

**Note**: This web application is designed to work with a companion iOS capture app. The iOS app implementation is separate and handles the actual screen capture functionality.
