{"version": 3, "sources": ["../../../src/utils/hooks/useOnScrollToBottom.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport { useEffect } from \"react\";\nimport { useThreadViewport } from \"../../context/react/ThreadViewportContext\";\n\nexport const useOnScrollToBottom = (callback: () => void) => {\n  const callbackRef = useCallbackRef(callback);\n  const onScrollToBottom = useThreadViewport((vp) => vp.onScrollToBottom);\n\n  useEffect(() => {\n    return onScrollToBottom(callbackRef);\n  }, [onScrollToBottom, callbackRef]);\n};\n"], "mappings": ";;;AAEA,SAAS,sBAAsB;AAC/B,SAAS,iBAAiB;AAC1B,SAAS,yBAAyB;AAE3B,IAAM,sBAAsB,CAAC,aAAyB;AAC3D,QAAM,cAAc,eAAe,QAAQ;AAC3C,QAAM,mBAAmB,kBAAkB,CAAC,OAAO,GAAG,gBAAgB;AAEtE,YAAU,MAAM;AACd,WAAO,iBAAiB,WAAW;AAAA,EACrC,GAAG,CAAC,kBAAkB,WAAW,CAAC;AACpC;", "names": []}