{"version": 3, "sources": ["../../../src/primitives/composer/ComposerIf.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { FC, PropsWithChildren } from \"react\";\nimport { useComposer } from \"../../context/react/ComposerContext\";\nimport type { RequireAtLeastOne } from \"../../utils/RequireAtLeastOne\";\n\ntype ComposerIfFilters = {\n  editing: boolean | undefined;\n};\n\nexport type UseComposerIfProps = RequireAtLeastOne<ComposerIfFilters>;\n\nconst useComposerIf = (props: UseComposerIfProps) => {\n  return useComposer((composer) => {\n    if (props.editing === true && !composer.isEditing) return false;\n    if (props.editing === false && composer.isEditing) return false;\n\n    return true;\n  });\n};\n\nexport namespace ComposerPrimitiveIf {\n  export type Props = PropsWithChildren<UseComposerIfProps>;\n}\n\nexport const ComposerPrimitiveIf: FC<ComposerPrimitiveIf.Props> = ({\n  children,\n  ...query\n}) => {\n  const result = useComposerIf(query);\n  return result ? children : null;\n};\n\nComposerPrimitiveIf.displayName = \"ComposerPrimitive.If\";\n"], "mappings": ";;;AAGA,SAAS,mBAAmB;AAS5B,IAAM,gBAAgB,CAAC,UAA8B;AACnD,SAAO,YAAY,CAAC,aAAa;AAC/B,QAAI,MAAM,YAAY,QAAQ,CAAC,SAAS,UAAW,QAAO;AAC1D,QAAI,MAAM,YAAY,SAAS,SAAS,UAAW,QAAO;AAE1D,WAAO;AAAA,EACT,CAAC;AACH;AAMO,IAAM,sBAAqD,CAAC;AAAA,EACjE;AAAA,EACA,GAAG;AACL,MAAM;AACJ,QAAM,SAAS,cAAc,KAAK;AAClC,SAAO,SAAS,WAAW;AAC7B;AAEA,oBAAoB,cAAc;", "names": []}