{"version": 3, "sources": ["../../../src/runtimes/local/LocalRuntimeCore.tsx"], "sourcesContent": ["import { BaseAssistantRuntimeCore } from \"../core/BaseAssistantRuntimeCore\";\nimport { LocalThreadRuntimeCore } from \"./LocalThreadRuntimeCore\";\nimport { LocalRuntimeOptionsBase } from \"./LocalRuntimeOptions\";\nimport { LocalThreadListRuntimeCore } from \"./LocalThreadListRuntimeCore\";\nimport { ExportedMessageRepository } from \"../utils/MessageRepository\";\nimport { ThreadMessageLike } from \"../external-store\";\n\nexport class LocalRuntimeCore extends BaseAssistantRuntimeCore {\n  public readonly threads;\n  public readonly Provider = undefined;\n\n  private _options: LocalRuntimeOptionsBase;\n\n  constructor(\n    options: LocalRuntimeOptionsBase,\n    initialMessages: readonly ThreadMessageLike[] | undefined,\n  ) {\n    super();\n\n    this._options = options;\n\n    this.threads = new LocalThreadListRuntimeCore(() => {\n      return new LocalThreadRuntimeCore(this._contextProvider, this._options);\n    });\n\n    if (initialMessages) {\n      this.threads\n        .getMainThreadRuntimeCore()\n        .import(ExportedMessageRepository.fromArray(initialMessages));\n    }\n  }\n}\n"], "mappings": ";AAAA,SAAS,gCAAgC;AACzC,SAAS,8BAA8B;AAEvC,SAAS,kCAAkC;AAC3C,SAAS,iCAAiC;AAGnC,IAAM,mBAAN,cAA+B,yBAAyB;AAAA,EAC7C;AAAA,EACA,WAAW;AAAA,EAEnB;AAAA,EAER,YACE,SACA,iBACA;AACA,UAAM;AAEN,SAAK,WAAW;AAEhB,SAAK,UAAU,IAAI,2BAA2B,MAAM;AAClD,aAAO,IAAI,uBAAuB,KAAK,kBAAkB,KAAK,QAAQ;AAAA,IACxE,CAAC;AAED,QAAI,iBAAiB;AACnB,WAAK,QACF,yBAAyB,EACzB,OAAO,0BAA0B,UAAU,eAAe,CAAC;AAAA,IAChE;AAAA,EACF;AACF;", "names": []}