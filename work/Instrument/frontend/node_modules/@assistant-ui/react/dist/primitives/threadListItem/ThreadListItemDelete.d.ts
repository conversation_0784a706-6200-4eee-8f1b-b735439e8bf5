import { ActionButtonElement, ActionButtonProps } from "../../utils/createActionButton";
declare const useThreadListItemDelete: () => () => void;
export declare namespace ThreadListItemPrimitiveDelete {
    type Element = ActionButtonElement;
    type Props = ActionButtonProps<typeof useThreadListItemDelete>;
}
export declare const ThreadListItemPrimitiveDelete: import("react").ForwardRefExoticComponent<Omit<import("react").ClassAttributes<HTMLButtonElement> & import("react").ButtonHTMLAttributes<HTMLButtonElement> & {
    asChild?: boolean;
}, "ref"> & import("react").RefAttributes<HTMLButtonElement>>;
export {};
//# sourceMappingURL=ThreadListItemDelete.d.ts.map