{"version": 3, "sources": ["../../../src/primitives/threadListItem/ThreadListItemUnarchive.ts"], "sourcesContent": ["\"use client\";\n\nimport {\n  ActionButtonElement,\n  ActionButtonProps,\n  createActionButton,\n} from \"../../utils/createActionButton\";\nimport { useThreadListItemRuntime } from \"../../context/react/ThreadListItemContext\";\n\nconst useThreadListItemUnarchive = () => {\n  const runtime = useThreadListItemRuntime();\n  return () => {\n    runtime.unarchive();\n  };\n};\n\nexport namespace ThreadListItemPrimitiveUnarchive {\n  export type Element = ActionButtonElement;\n  export type Props = ActionButtonProps<typeof useThreadListItemUnarchive>;\n}\n\nexport const ThreadListItemPrimitiveUnarchive = createActionButton(\n  \"ThreadListItemPrimitive.Unarchive\",\n  useThreadListItemUnarchive,\n);\n"], "mappings": ";;;AAEA;AAAA,EAGE;AAAA,OACK;AACP,SAAS,gCAAgC;AAEzC,IAAM,6BAA6B,MAAM;AACvC,QAAM,UAAU,yBAAyB;AACzC,SAAO,MAAM;AACX,YAAQ,UAAU;AAAA,EACpB;AACF;AAOO,IAAM,mCAAmC;AAAA,EAC9C;AAAA,EACA;AACF;", "names": []}