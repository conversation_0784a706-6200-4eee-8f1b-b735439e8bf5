{"version": 3, "sources": ["../../../src/primitives/thread/ThreadViewport.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { type ComponentRef, forwardRef, ComponentPropsWithoutRef } from \"react\";\nimport { useThreadViewportAutoScroll } from \"./useThreadViewportAutoScroll\";\nimport { ThreadViewportProvider } from \"../../context/providers/ThreadViewportProvider\";\n\nexport namespace ThreadPrimitiveViewport {\n  export type Element = ComponentRef<typeof Primitive.div>;\n  export type Props = ComponentPropsWithoutRef<typeof Primitive.div> & {\n    /**\n     * Whether to automatically scroll to the bottom when new messages are added.\n     * When enabled, the viewport will automatically scroll to show the latest content.\n     * @default true\n     */\n    autoScroll?: boolean | undefined;\n  };\n}\n\nconst ThreadPrimitiveViewportScrollable = forwardRef<\n  ThreadPrimitiveViewport.Element,\n  ThreadPrimitiveViewport.Props\n>(({ autoScroll, children, ...rest }, forwardedRef) => {\n  const autoScrollRef = useThreadViewportAutoScroll<HTMLDivElement>({\n    autoScroll,\n  });\n\n  const ref = useComposedRefs(forwardedRef, autoScrollRef);\n\n  return (\n    <Primitive.div {...rest} ref={ref}>\n      {children}\n    </Primitive.div>\n  );\n});\n\nThreadPrimitiveViewportScrollable.displayName =\n  \"ThreadPrimitive.ViewportScrollable\";\n\n/**\n * A scrollable viewport container for thread messages.\n *\n * This component provides a scrollable area for displaying thread messages with\n * automatic scrolling capabilities. It manages the viewport state and provides\n * context for child components to access viewport-related functionality.\n *\n * @example\n * ```tsx\n * <ThreadPrimitive.Viewport autoScroll={true}>\n *   <ThreadPrimitive.Messages components={{ Message: MyMessage }} />\n * </ThreadPrimitive.Viewport>\n * ```\n */\nexport const ThreadPrimitiveViewport = forwardRef<\n  ThreadPrimitiveViewport.Element,\n  ThreadPrimitiveViewport.Props\n>((props, ref) => {\n  return (\n    <ThreadViewportProvider>\n      <ThreadPrimitiveViewportScrollable {...props} ref={ref} />\n    </ThreadViewportProvider>\n  );\n});\n\nThreadPrimitiveViewport.displayName = \"ThreadPrimitive.Viewport\";\n"], "mappings": ";;;AAEA,SAAS,uBAAuB;AAChC,SAAS,iBAAiB;AAC1B,SAA4B,kBAA4C;AACxE,SAAS,mCAAmC;AAC5C,SAAS,8BAA8B;AAyBnC;AAXJ,IAAM,oCAAoC,WAGxC,CAAC,EAAE,YAAY,UAAU,GAAG,KAAK,GAAG,iBAAiB;AACrD,QAAM,gBAAgB,4BAA4C;AAAA,IAChE;AAAA,EACF,CAAC;AAED,QAAM,MAAM,gBAAgB,cAAc,aAAa;AAEvD,SACE,oBAAC,UAAU,KAAV,EAAe,GAAG,MAAM,KACtB,UACH;AAEJ,CAAC;AAED,kCAAkC,cAChC;AAgBK,IAAM,0BAA0B,WAGrC,CAAC,OAAO,QAAQ;AAChB,SACE,oBAAC,0BACC,8BAAC,qCAAmC,GAAG,OAAO,KAAU,GAC1D;AAEJ,CAAC;AAED,wBAAwB,cAAc;", "names": []}