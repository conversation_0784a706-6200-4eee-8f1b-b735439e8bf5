import { ActionButtonProps } from "../../utils/createActionButton";
declare const useActionBarStopSpeaking: () => (() => void) | null;
export declare namespace ActionBarPrimitiveStopSpeaking {
    type Element = HTMLButtonElement;
    type Props = ActionButtonProps<typeof useActionBarStopSpeaking>;
}
export declare const ActionBarPrimitiveStopSpeaking: import("react").ForwardRefExoticComponent<Omit<import("react").ClassAttributes<HTMLButtonElement> & import("react").ButtonHTMLAttributes<HTMLButtonElement> & {
    asChild?: boolean;
}, "ref"> & import("react").RefAttributes<HTMLButtonElement>>;
export {};
//# sourceMappingURL=ActionBarStopSpeaking.d.ts.map