{"version": 3, "sources": ["../../../src/runtimes/composer/BaseComposerRuntimeCore.tsx"], "sourcesContent": ["import {\n  Attachment,\n  CompleteAttachment,\n  PendingAttachment,\n} from \"../../types/AttachmentTypes\";\nimport { AppendMessage } from \"../../types\";\nimport { AttachmentAdapter } from \"../adapters/attachment\";\nimport {\n  ComposerRuntimeCore,\n  ComposerRuntimeEventType,\n} from \"../core/ComposerRuntimeCore\";\nimport { MessageRole, RunConfig } from \"../../types/AssistantTypes\";\nimport { BaseSubscribable } from \"../remote-thread-list/BaseSubscribable\";\n\nconst isAttachmentComplete = (a: Attachment): a is CompleteAttachment =>\n  a.status.type === \"complete\";\n\nexport abstract class BaseComposerRuntimeCore\n  extends BaseSubscribable\n  implements ComposerRuntimeCore\n{\n  public readonly isEditing = true;\n\n  protected abstract getAttachmentAdapter(): AttachmentAdapter | undefined;\n\n  public getAttachmentAccept(): string {\n    return this.getAttachmentAdapter()?.accept ?? \"*\";\n  }\n\n  private _attachments: readonly Attachment[] = [];\n  public get attachments() {\n    return this._attachments;\n  }\n\n  protected setAttachments(value: readonly Attachment[]) {\n    this._attachments = value;\n    this._notifySubscribers();\n  }\n\n  public abstract get canCancel(): boolean;\n\n  public get isEmpty() {\n    return !this.text.trim() && !this.attachments.length;\n  }\n\n  private _text = \"\";\n\n  get text() {\n    return this._text;\n  }\n\n  private _role: MessageRole = \"user\";\n\n  get role() {\n    return this._role;\n  }\n\n  private _runConfig: RunConfig = {};\n\n  get runConfig() {\n    return this._runConfig;\n  }\n\n  public setText(value: string) {\n    if (this._text === value) return;\n\n    this._text = value;\n    this._notifySubscribers();\n  }\n\n  public setRole(role: MessageRole) {\n    if (this._role === role) return;\n\n    this._role = role;\n    this._notifySubscribers();\n  }\n\n  public setRunConfig(runConfig: RunConfig) {\n    if (this._runConfig === runConfig) return;\n\n    this._runConfig = runConfig;\n    this._notifySubscribers();\n  }\n\n  private _emptyTextAndAttachments() {\n    this._attachments = [];\n    this._text = \"\";\n    this._notifySubscribers();\n  }\n\n  private async _onClearAttachments() {\n    const adapter = this.getAttachmentAdapter();\n    if (adapter) {\n      await Promise.all(this._attachments.map((a) => adapter.remove(a)));\n    }\n  }\n\n  public async reset() {\n    if (\n      this._attachments.length === 0 &&\n      this._text === \"\" &&\n      this._role === \"user\" &&\n      Object.keys(this._runConfig).length === 0\n    ) {\n      return;\n    }\n\n    this._role = \"user\";\n    this._runConfig = {};\n\n    const task = this._onClearAttachments();\n    this._emptyTextAndAttachments();\n    await task;\n  }\n\n  public async clearAttachments() {\n    const task = this._onClearAttachments();\n    this.setAttachments([]);\n\n    await task;\n  }\n\n  public async send() {\n    const adapter = this.getAttachmentAdapter();\n    const attachments =\n      adapter && this.attachments.length > 0\n        ? await Promise.all(\n            this.attachments.map(async (a) => {\n              if (isAttachmentComplete(a)) return a;\n              const result = await adapter.send(a);\n              return result as CompleteAttachment;\n            }),\n          )\n        : [];\n\n    const message: Omit<AppendMessage, \"parentId\" | \"sourceId\"> = {\n      createdAt: new Date(),\n      role: this.role,\n      content: this.text ? [{ type: \"text\", text: this.text }] : [],\n      attachments,\n      runConfig: this.runConfig,\n      metadata: { custom: {} },\n    };\n    this._emptyTextAndAttachments();\n\n    this.handleSend(message);\n    this._notifyEventSubscribers(\"send\");\n  }\n\n  public cancel() {\n    this.handleCancel();\n  }\n\n  protected abstract handleSend(\n    message: Omit<AppendMessage, \"parentId\" | \"sourceId\">,\n  ): void;\n  protected abstract handleCancel(): void;\n\n  async addAttachment(file: File) {\n    const adapter = this.getAttachmentAdapter();\n    if (!adapter) throw new Error(\"Attachments are not supported\");\n\n    const upsertAttachment = (a: PendingAttachment) => {\n      const idx = this._attachments.findIndex(\n        (attachment) => attachment.id === a.id,\n      );\n      if (idx !== -1)\n        this._attachments = [\n          ...this._attachments.slice(0, idx),\n          a,\n          ...this._attachments.slice(idx + 1),\n        ];\n      else {\n        this._attachments = [...this._attachments, a];\n        this._notifyEventSubscribers(\"attachment_add\");\n      }\n\n      this._notifySubscribers();\n    };\n\n    const promiseOrGenerator = adapter.add({ file });\n    if (Symbol.asyncIterator in promiseOrGenerator) {\n      for await (const r of promiseOrGenerator) {\n        upsertAttachment(r);\n      }\n    } else {\n      upsertAttachment(await promiseOrGenerator);\n    }\n\n    this._notifyEventSubscribers(\"attachment_add\");\n    this._notifySubscribers();\n  }\n\n  async removeAttachment(attachmentId: string) {\n    const adapter = this.getAttachmentAdapter();\n    if (!adapter) throw new Error(\"Attachments are not supported\");\n\n    const index = this._attachments.findIndex((a) => a.id === attachmentId);\n    if (index === -1) throw new Error(\"Attachment not found\");\n    const attachment = this._attachments[index]!;\n\n    await adapter.remove(attachment);\n\n    // this._attachments.toSpliced(index, 1); - not yet widely supported\n    this._attachments = [\n      ...this._attachments.slice(0, index),\n      ...this._attachments.slice(index + 1),\n    ];\n    this._notifySubscribers();\n  }\n\n  private _eventSubscribers = new Map<\n    ComposerRuntimeEventType,\n    Set<() => void>\n  >();\n\n  protected _notifyEventSubscribers(event: ComposerRuntimeEventType) {\n    const subscribers = this._eventSubscribers.get(event);\n    if (!subscribers) return;\n\n    for (const callback of subscribers) callback();\n  }\n\n  public unstable_on(event: ComposerRuntimeEventType, callback: () => void) {\n    const subscribers = this._eventSubscribers.get(event);\n    if (!subscribers) {\n      this._eventSubscribers.set(event, new Set([callback]));\n    } else {\n      subscribers.add(callback);\n    }\n\n    return () => {\n      const subscribers = this._eventSubscribers.get(event);\n      if (!subscribers) return;\n      subscribers.delete(callback);\n    };\n  }\n}\n"], "mappings": ";AAYA,SAAS,wBAAwB;AAEjC,IAAM,uBAAuB,CAAC,MAC5B,EAAE,OAAO,SAAS;AAEb,IAAe,0BAAf,cACG,iBAEV;AAAA,EACkB,YAAY;AAAA,EAIrB,sBAA8B;AACnC,WAAO,KAAK,qBAAqB,GAAG,UAAU;AAAA,EAChD;AAAA,EAEQ,eAAsC,CAAC;AAAA,EAC/C,IAAW,cAAc;AACvB,WAAO,KAAK;AAAA,EACd;AAAA,EAEU,eAAe,OAA8B;AACrD,SAAK,eAAe;AACpB,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EAIA,IAAW,UAAU;AACnB,WAAO,CAAC,KAAK,KAAK,KAAK,KAAK,CAAC,KAAK,YAAY;AAAA,EAChD;AAAA,EAEQ,QAAQ;AAAA,EAEhB,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EAEQ,QAAqB;AAAA,EAE7B,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EAEQ,aAAwB,CAAC;AAAA,EAEjC,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EAEO,QAAQ,OAAe;AAC5B,QAAI,KAAK,UAAU,MAAO;AAE1B,SAAK,QAAQ;AACb,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EAEO,QAAQ,MAAmB;AAChC,QAAI,KAAK,UAAU,KAAM;AAEzB,SAAK,QAAQ;AACb,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EAEO,aAAa,WAAsB;AACxC,QAAI,KAAK,eAAe,UAAW;AAEnC,SAAK,aAAa;AAClB,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EAEQ,2BAA2B;AACjC,SAAK,eAAe,CAAC;AACrB,SAAK,QAAQ;AACb,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EAEA,MAAc,sBAAsB;AAClC,UAAM,UAAU,KAAK,qBAAqB;AAC1C,QAAI,SAAS;AACX,YAAM,QAAQ,IAAI,KAAK,aAAa,IAAI,CAAC,MAAM,QAAQ,OAAO,CAAC,CAAC,CAAC;AAAA,IACnE;AAAA,EACF;AAAA,EAEA,MAAa,QAAQ;AACnB,QACE,KAAK,aAAa,WAAW,KAC7B,KAAK,UAAU,MACf,KAAK,UAAU,UACf,OAAO,KAAK,KAAK,UAAU,EAAE,WAAW,GACxC;AACA;AAAA,IACF;AAEA,SAAK,QAAQ;AACb,SAAK,aAAa,CAAC;AAEnB,UAAM,OAAO,KAAK,oBAAoB;AACtC,SAAK,yBAAyB;AAC9B,UAAM;AAAA,EACR;AAAA,EAEA,MAAa,mBAAmB;AAC9B,UAAM,OAAO,KAAK,oBAAoB;AACtC,SAAK,eAAe,CAAC,CAAC;AAEtB,UAAM;AAAA,EACR;AAAA,EAEA,MAAa,OAAO;AAClB,UAAM,UAAU,KAAK,qBAAqB;AAC1C,UAAM,cACJ,WAAW,KAAK,YAAY,SAAS,IACjC,MAAM,QAAQ;AAAA,MACZ,KAAK,YAAY,IAAI,OAAO,MAAM;AAChC,YAAI,qBAAqB,CAAC,EAAG,QAAO;AACpC,cAAM,SAAS,MAAM,QAAQ,KAAK,CAAC;AACnC,eAAO;AAAA,MACT,CAAC;AAAA,IACH,IACA,CAAC;AAEP,UAAM,UAAwD;AAAA,MAC5D,WAAW,oBAAI,KAAK;AAAA,MACpB,MAAM,KAAK;AAAA,MACX,SAAS,KAAK,OAAO,CAAC,EAAE,MAAM,QAAQ,MAAM,KAAK,KAAK,CAAC,IAAI,CAAC;AAAA,MAC5D;AAAA,MACA,WAAW,KAAK;AAAA,MAChB,UAAU,EAAE,QAAQ,CAAC,EAAE;AAAA,IACzB;AACA,SAAK,yBAAyB;AAE9B,SAAK,WAAW,OAAO;AACvB,SAAK,wBAAwB,MAAM;AAAA,EACrC;AAAA,EAEO,SAAS;AACd,SAAK,aAAa;AAAA,EACpB;AAAA,EAOA,MAAM,cAAc,MAAY;AAC9B,UAAM,UAAU,KAAK,qBAAqB;AAC1C,QAAI,CAAC,QAAS,OAAM,IAAI,MAAM,+BAA+B;AAE7D,UAAM,mBAAmB,CAAC,MAAyB;AACjD,YAAM,MAAM,KAAK,aAAa;AAAA,QAC5B,CAAC,eAAe,WAAW,OAAO,EAAE;AAAA,MACtC;AACA,UAAI,QAAQ;AACV,aAAK,eAAe;AAAA,UAClB,GAAG,KAAK,aAAa,MAAM,GAAG,GAAG;AAAA,UACjC;AAAA,UACA,GAAG,KAAK,aAAa,MAAM,MAAM,CAAC;AAAA,QACpC;AAAA,WACG;AACH,aAAK,eAAe,CAAC,GAAG,KAAK,cAAc,CAAC;AAC5C,aAAK,wBAAwB,gBAAgB;AAAA,MAC/C;AAEA,WAAK,mBAAmB;AAAA,IAC1B;AAEA,UAAM,qBAAqB,QAAQ,IAAI,EAAE,KAAK,CAAC;AAC/C,QAAI,OAAO,iBAAiB,oBAAoB;AAC9C,uBAAiB,KAAK,oBAAoB;AACxC,yBAAiB,CAAC;AAAA,MACpB;AAAA,IACF,OAAO;AACL,uBAAiB,MAAM,kBAAkB;AAAA,IAC3C;AAEA,SAAK,wBAAwB,gBAAgB;AAC7C,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EAEA,MAAM,iBAAiB,cAAsB;AAC3C,UAAM,UAAU,KAAK,qBAAqB;AAC1C,QAAI,CAAC,QAAS,OAAM,IAAI,MAAM,+BAA+B;AAE7D,UAAM,QAAQ,KAAK,aAAa,UAAU,CAAC,MAAM,EAAE,OAAO,YAAY;AACtE,QAAI,UAAU,GAAI,OAAM,IAAI,MAAM,sBAAsB;AACxD,UAAM,aAAa,KAAK,aAAa,KAAK;AAE1C,UAAM,QAAQ,OAAO,UAAU;AAG/B,SAAK,eAAe;AAAA,MAClB,GAAG,KAAK,aAAa,MAAM,GAAG,KAAK;AAAA,MACnC,GAAG,KAAK,aAAa,MAAM,QAAQ,CAAC;AAAA,IACtC;AACA,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EAEQ,oBAAoB,oBAAI,IAG9B;AAAA,EAEQ,wBAAwB,OAAiC;AACjE,UAAM,cAAc,KAAK,kBAAkB,IAAI,KAAK;AACpD,QAAI,CAAC,YAAa;AAElB,eAAW,YAAY,YAAa,UAAS;AAAA,EAC/C;AAAA,EAEO,YAAY,OAAiC,UAAsB;AACxE,UAAM,cAAc,KAAK,kBAAkB,IAAI,KAAK;AACpD,QAAI,CAAC,aAAa;AAChB,WAAK,kBAAkB,IAAI,OAAO,oBAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;AAAA,IACvD,OAAO;AACL,kBAAY,IAAI,QAAQ;AAAA,IAC1B;AAEA,WAAO,MAAM;AACX,YAAMA,eAAc,KAAK,kBAAkB,IAAI,KAAK;AACpD,UAAI,CAACA,aAAa;AAClB,MAAAA,aAAY,OAAO,QAAQ;AAAA,IAC7B;AAAA,EACF;AACF;", "names": ["subscribers"]}