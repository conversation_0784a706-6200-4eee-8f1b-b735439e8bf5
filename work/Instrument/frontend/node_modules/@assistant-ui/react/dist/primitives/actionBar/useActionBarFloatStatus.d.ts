export declare enum HideAndFloatStatus {
    Hidden = "hidden",
    Floating = "floating",
    Normal = "normal"
}
export type UseActionBarFloatStatusProps = {
    hideWhenRunning?: boolean | undefined;
    autohide?: "always" | "not-last" | "never" | undefined;
    autohideFloat?: "always" | "single-branch" | "never" | undefined;
};
export declare const useActionBarFloatStatus: ({ hideWhenRunning, autohide, autohideFloat, }: UseActionBarFloatStatusProps) => HideAndFloatStatus;
//# sourceMappingURL=useActionBarFloatStatus.d.ts.map