{"version": 3, "sources": ["../../../src/runtimes/external-store/createMessageConverter.tsx"], "sourcesContent": ["\"use client\";\nimport { ThreadState } from \"../../api\";\nimport { useMessagePart, useMessage } from \"../../context\";\nimport { ThreadMessage } from \"../../types\";\nimport {\n  useExternalMessageConverter,\n  convertExternalMessages,\n} from \"./external-message-converter\";\nimport { getExternalStoreMessages } from \"./getExternalStoreMessage\";\n\nexport const createMessageConverter = <T extends object>(\n  callback: useExternalMessageConverter.Callback<T>,\n) => {\n  const result = {\n    useThreadMessages: ({\n      messages,\n      isRunning,\n      joinStrategy,\n    }: {\n      messages: T[];\n      isRunning: boolean;\n      joinStrategy?: \"concat-content\" | \"none\" | undefined;\n    }) => {\n      return useExternalMessageConverter<T>({\n        callback,\n        messages,\n        isRunning,\n        joinStrategy,\n      });\n    },\n    toThreadMessages: (messages: T[]) => {\n      return convertExternalMessages(messages, callback, false); // TODO figure out isRunning\n    },\n    toOriginalMessages: (\n      input: ThreadState | ThreadMessage | ThreadMessage[\"content\"][number],\n    ) => {\n      const messages = getExternalStoreMessages(input);\n      if (messages.length === 0) throw new Error(\"No original messages found\");\n      return messages;\n    },\n    toOriginalMessage: (\n      input: ThreadState | ThreadMessage | ThreadMessage[\"content\"][number],\n    ) => {\n      const messages = result.toOriginalMessages(input);\n      return messages[0]!;\n    },\n    useOriginalMessage: () => {\n      const messageMessages = result.useOriginalMessages();\n      const first = messageMessages[0]!;\n      return first;\n    },\n    useOriginalMessages: () => {\n      const MessagePartMessages = useMessagePart<T[]>({\n        optional: true,\n        selector: getExternalStoreMessages,\n      });\n\n      const messageMessages = useMessage<T[]>(getExternalStoreMessages);\n      const messages = MessagePartMessages ?? messageMessages;\n      if (messages.length === 0) throw new Error(\"No original messages found\");\n      return messages;\n    },\n  };\n\n  return result;\n};\n"], "mappings": ";;;AAEA,SAAS,gBAAgB,kBAAkB;AAE3C;AAAA,EACE;AAAA,EACA;AAAA,OACK;AACP,SAAS,gCAAgC;AAElC,IAAM,yBAAyB,CACpC,aACG;AACH,QAAM,SAAS;AAAA,IACb,mBAAmB,CAAC;AAAA,MAClB;AAAA,MACA;AAAA,MACA;AAAA,IACF,MAIM;AACJ,aAAO,4BAA+B;AAAA,QACpC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,kBAAkB,CAAC,aAAkB;AACnC,aAAO,wBAAwB,UAAU,UAAU,KAAK;AAAA,IAC1D;AAAA,IACA,oBAAoB,CAClB,UACG;AACH,YAAM,WAAW,yBAAyB,KAAK;AAC/C,UAAI,SAAS,WAAW,EAAG,OAAM,IAAI,MAAM,4BAA4B;AACvE,aAAO;AAAA,IACT;AAAA,IACA,mBAAmB,CACjB,UACG;AACH,YAAM,WAAW,OAAO,mBAAmB,KAAK;AAChD,aAAO,SAAS,CAAC;AAAA,IACnB;AAAA,IACA,oBAAoB,MAAM;AACxB,YAAM,kBAAkB,OAAO,oBAAoB;AACnD,YAAM,QAAQ,gBAAgB,CAAC;AAC/B,aAAO;AAAA,IACT;AAAA,IACA,qBAAqB,MAAM;AACzB,YAAM,sBAAsB,eAAoB;AAAA,QAC9C,UAAU;AAAA,QACV,UAAU;AAAA,MACZ,CAAC;AAED,YAAM,kBAAkB,WAAgB,wBAAwB;AAChE,YAAM,WAAW,uBAAuB;AACxC,UAAI,SAAS,WAAW,EAAG,OAAM,IAAI,MAAM,4BAA4B;AACvE,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;", "names": []}