{"version": 3, "sources": ["../../../src/primitives/composer/ComposerAddAttachment.tsx"], "sourcesContent": ["\"use client\";\n\nimport {\n  ActionButtonElement,\n  ActionButtonProps,\n  createActionButton,\n} from \"../../utils/createActionButton\";\nimport { useCallback } from \"react\";\nimport { useComposer, useComposerRuntime } from \"../../context\";\n\nconst useComposerAddAttachment = ({\n  multiple = true,\n}: {\n  /** allow selecting multiple files */\n  multiple?: boolean | undefined;\n} = {}) => {\n  const disabled = useComposer((c) => !c.isEditing);\n\n  const composerRuntime = useComposerRuntime();\n  const callback = useCallback(() => {\n    const input = document.createElement(\"input\");\n    input.type = \"file\";\n    input.multiple = multiple;\n    input.hidden = true;\n\n    const attachmentAccept = composerRuntime.getAttachmentAccept();\n    if (attachmentAccept !== \"*\") {\n      input.accept = attachmentAccept;\n    }\n\n    document.body.appendChild(input);\n\n    input.onchange = (e) => {\n      const fileList = (e.target as HTMLInputElement).files;\n      if (!fileList) return;\n      for (const file of fileList) {\n        composerRuntime.addAttachment(file);\n      }\n\n      document.body.removeChild(input);\n    };\n\n    input.oncancel = () => {\n      if (!input.files || input.files.length === 0) {\n        document.body.removeChild(input);\n      }\n    };\n\n    input.click();\n  }, [composerRuntime, multiple]);\n\n  if (disabled) return null;\n  return callback;\n};\n\nexport namespace ComposerPrimitiveAddAttachment {\n  export type Element = ActionButtonElement;\n  export type Props = ActionButtonProps<typeof useComposerAddAttachment>;\n}\n\nexport const ComposerPrimitiveAddAttachment = createActionButton(\n  \"ComposerPrimitive.AddAttachment\",\n  useComposerAddAttachment,\n  [\"multiple\"],\n);\n"], "mappings": ";;;AAEA;AAAA,EAGE;AAAA,OACK;AACP,SAAS,mBAAmB;AAC5B,SAAS,aAAa,0BAA0B;AAEhD,IAAM,2BAA2B,CAAC;AAAA,EAChC,WAAW;AACb,IAGI,CAAC,MAAM;AACT,QAAM,WAAW,YAAY,CAAC,MAAM,CAAC,EAAE,SAAS;AAEhD,QAAM,kBAAkB,mBAAmB;AAC3C,QAAM,WAAW,YAAY,MAAM;AACjC,UAAM,QAAQ,SAAS,cAAc,OAAO;AAC5C,UAAM,OAAO;AACb,UAAM,WAAW;AACjB,UAAM,SAAS;AAEf,UAAM,mBAAmB,gBAAgB,oBAAoB;AAC7D,QAAI,qBAAqB,KAAK;AAC5B,YAAM,SAAS;AAAA,IACjB;AAEA,aAAS,KAAK,YAAY,KAAK;AAE/B,UAAM,WAAW,CAAC,MAAM;AACtB,YAAM,WAAY,EAAE,OAA4B;AAChD,UAAI,CAAC,SAAU;AACf,iBAAW,QAAQ,UAAU;AAC3B,wBAAgB,cAAc,IAAI;AAAA,MACpC;AAEA,eAAS,KAAK,YAAY,KAAK;AAAA,IACjC;AAEA,UAAM,WAAW,MAAM;AACrB,UAAI,CAAC,MAAM,SAAS,MAAM,MAAM,WAAW,GAAG;AAC5C,iBAAS,KAAK,YAAY,KAAK;AAAA,MACjC;AAAA,IACF;AAEA,UAAM,MAAM;AAAA,EACd,GAAG,CAAC,iBAAiB,QAAQ,CAAC;AAE9B,MAAI,SAAU,QAAO;AACrB,SAAO;AACT;AAOO,IAAM,iCAAiC;AAAA,EAC5C;AAAA,EACA;AAAA,EACA,CAAC,UAAU;AACb;", "names": []}