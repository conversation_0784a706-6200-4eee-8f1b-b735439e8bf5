{"version": 3, "sources": ["../../../src/primitives/composer/index.ts"], "sourcesContent": ["export { ComposerPrimitiveRoot as Root } from \"./ComposerRoot\";\nexport { ComposerPrimitiveInput as Input } from \"./ComposerInput\";\nexport { ComposerPrimitiveSend as Send } from \"./ComposerSend\";\nexport { ComposerPrimitiveCancel as Cancel } from \"./ComposerCancel\";\nexport { ComposerPrimitiveAddAttachment as AddAttachment } from \"./ComposerAddAttachment\";\nexport { ComposerPrimitiveAttachments as Attachments } from \"./ComposerAttachments\";\nexport { ComposerPrimitiveAttachmentByIndex as AttachmentByIndex } from \"./ComposerAttachments\";\nexport { ComposerPrimitiveIf as If } from \"./ComposerIf\";\n"], "mappings": ";AAAA,SAAkC,6BAAY;AAC9C,SAAmC,8BAAa;AAChD,SAAkC,6BAAY;AAC9C,SAAoC,+BAAc;AAClD,SAA2C,sCAAqB;AAChE,SAAyC,oCAAmB;AAC5D,SAA+C,0CAAyB;AACxE,SAAgC,2BAAU;", "names": []}