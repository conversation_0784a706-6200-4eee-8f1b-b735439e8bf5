{"version": 3, "sources": ["../../../src/runtimes/local/LocalThreadRuntimeCore.tsx"], "sourcesContent": ["import { fromThreadMessageLike, generateId } from \"../../internal\";\nimport type { AppendMessage, ThreadAssistantMessage } from \"../../types\";\nimport type { ChatModelAdapter, ChatModelRunResult } from \"./ChatModelAdapter\";\nimport { shouldContinue } from \"./shouldContinue\";\nimport { LocalRuntimeOptionsBase } from \"./LocalRuntimeOptions\";\nimport {\n  AddToolResultOptions,\n  ThreadSuggestion,\n  ThreadRuntimeCore,\n  StartRunConfig,\n  ResumeRunConfig,\n} from \"../core/ThreadRuntimeCore\";\nimport { BaseThreadRuntimeCore } from \"../core/BaseThreadRuntimeCore\";\nimport { RunConfig } from \"../../types/AssistantTypes\";\nimport { ModelContextProvider } from \"../../model-context\";\n\nclass AbortError extends Error {\n  override name = \"AbortError\";\n  detach: boolean;\n\n  constructor(detach: boolean, message?: string) {\n    super(message);\n    this.detach = detach;\n  }\n}\n\nexport class LocalThreadRuntimeCore\n  extends BaseThreadRuntimeCore\n  implements ThreadRuntimeCore\n{\n  public readonly capabilities = {\n    switchToBranch: true,\n    edit: true,\n    reload: true,\n    cancel: true,\n    unstable_copy: true,\n    speech: false,\n    attachments: false,\n    feedback: false,\n  };\n\n  private abortController: AbortController | null = null;\n\n  public readonly isDisabled = false;\n\n  private _isLoading = false;\n  public get isLoading() {\n    return this._isLoading;\n  }\n\n  private _suggestions: readonly ThreadSuggestion[] = [];\n  private _suggestionsController: AbortController | null = null;\n  public get suggestions(): readonly ThreadSuggestion[] {\n    return this._suggestions;\n  }\n\n  public get adapters() {\n    return this._options.adapters;\n  }\n\n  constructor(\n    contextProvider: ModelContextProvider,\n    options: LocalRuntimeOptionsBase,\n  ) {\n    super(contextProvider);\n    this.__internal_setOptions(options);\n  }\n\n  private _options!: LocalRuntimeOptionsBase;\n\n  private _lastRunConfig: RunConfig = {};\n\n  public get extras() {\n    return undefined;\n  }\n\n  public __internal_setOptions(options: LocalRuntimeOptionsBase) {\n    if (this._options === options) return;\n\n    this._options = options;\n\n    let hasUpdates = false;\n\n    const canSpeak = options.adapters?.speech !== undefined;\n    if (this.capabilities.speech !== canSpeak) {\n      this.capabilities.speech = canSpeak;\n      hasUpdates = true;\n    }\n\n    const canAttach = options.adapters?.attachments !== undefined;\n    if (this.capabilities.attachments !== canAttach) {\n      this.capabilities.attachments = canAttach;\n      hasUpdates = true;\n    }\n\n    const canFeedback = options.adapters?.feedback !== undefined;\n    if (this.capabilities.feedback !== canFeedback) {\n      this.capabilities.feedback = canFeedback;\n      hasUpdates = true;\n    }\n\n    if (hasUpdates) this._notifySubscribers();\n  }\n\n  private _loadPromise: Promise<void> | undefined;\n  public __internal_load() {\n    if (this._loadPromise) return this._loadPromise;\n\n    const promise = this.adapters.history?.load() ?? Promise.resolve(null);\n\n    this._isLoading = true;\n    this._notifySubscribers();\n\n    this._loadPromise = promise\n      .then((repo) => {\n        if (!repo) return;\n        this.repository.import(repo);\n        this._notifySubscribers();\n\n        const resume = this.adapters.history?.resume?.bind(\n          this.adapters.history,\n        );\n        if (repo.unstable_resume && resume) {\n          this.startRun(\n            {\n              parentId: this.repository.headId,\n              sourceId: this.repository.headId,\n              runConfig: this._lastRunConfig,\n            },\n            resume,\n          );\n        }\n      })\n      .finally(() => {\n        this._isLoading = false;\n        this._notifySubscribers();\n      });\n\n    return this._loadPromise;\n  }\n\n  public async append(message: AppendMessage): Promise<void> {\n    this.ensureInitialized();\n\n    const newMessage = fromThreadMessageLike(message, generateId(), {\n      type: \"complete\",\n      reason: \"unknown\",\n    });\n    this.repository.addOrUpdateMessage(message.parentId, newMessage);\n    this._options.adapters.history?.append({\n      parentId: message.parentId,\n      message: newMessage,\n    });\n\n    const startRun = message.startRun ?? message.role === \"user\";\n    if (startRun) {\n      await this.startRun({\n        parentId: newMessage.id,\n        sourceId: message.sourceId,\n        runConfig: message.runConfig ?? {},\n      });\n    } else {\n      this.repository.resetHead(newMessage.id);\n      this._notifySubscribers();\n    }\n  }\n\n  public resumeRun({ stream, ...startConfig }: ResumeRunConfig): Promise<void> {\n    return this.startRun(startConfig, stream);\n  }\n\n  public async startRun(\n    { parentId, runConfig }: StartRunConfig,\n    runCallback?: ChatModelAdapter[\"run\"],\n  ): Promise<void> {\n    this.ensureInitialized();\n\n    this.repository.resetHead(parentId);\n\n    // add assistant message\n    const id = generateId();\n    let message: ThreadAssistantMessage = {\n      id,\n      role: \"assistant\",\n      status: { type: \"running\" },\n      content: [],\n      metadata: {\n        unstable_state: this.state,\n        unstable_annotations: [],\n        unstable_data: [],\n        steps: [],\n        custom: {},\n      },\n      createdAt: new Date(),\n    };\n\n    this._notifyEventSubscribers(\"run-start\");\n\n    try {\n      this._suggestions = [];\n      this._suggestionsController?.abort();\n      this._suggestionsController = null;\n\n      do {\n        message = await this.performRoundtrip(\n          parentId,\n          message,\n          runConfig,\n          runCallback,\n        );\n        runCallback = undefined;\n      } while (shouldContinue(message, this._options.unstable_humanToolNames));\n    } finally {\n      this._notifyEventSubscribers(\"run-end\");\n    }\n\n    this._suggestionsController = new AbortController();\n    const signal = this._suggestionsController.signal;\n    if (\n      this.adapters.suggestion &&\n      message.status?.type !== \"requires-action\"\n    ) {\n      const promiseOrGenerator = this.adapters.suggestion?.generate({\n        messages: this.messages,\n      });\n\n      if (Symbol.asyncIterator in promiseOrGenerator) {\n        for await (const r of promiseOrGenerator) {\n          if (signal.aborted) break;\n          this._suggestions = r;\n        }\n      } else {\n        const result = await promiseOrGenerator;\n        if (signal.aborted) return;\n        this._suggestions = result;\n      }\n    }\n  }\n\n  private async performRoundtrip(\n    parentId: string | null,\n    message: ThreadAssistantMessage,\n    runConfig: RunConfig | undefined,\n    runCallback?: ChatModelAdapter[\"run\"],\n  ) {\n    const messages = this.repository.getMessages();\n\n    // abort existing run\n    this.abortController?.abort();\n    this.abortController = new AbortController();\n\n    const initialContent = message.content;\n    const initialAnnotations = message.metadata?.unstable_annotations;\n    const initialData = message.metadata?.unstable_data;\n    const initialSteps = message.metadata?.steps;\n    const initalCustom = message.metadata?.custom;\n    const updateMessage = (m: Partial<ChatModelRunResult>) => {\n      const newSteps = m.metadata?.steps;\n      const steps = newSteps\n        ? [...(initialSteps ?? []), ...newSteps]\n        : undefined;\n\n      const newAnnotations = m.metadata?.unstable_annotations;\n      const newData = m.metadata?.unstable_data;\n      const annotations = newAnnotations\n        ? [...(initialAnnotations ?? []), ...newAnnotations]\n        : undefined;\n      const data = newData ? [...(initialData ?? []), ...newData] : undefined;\n\n      message = {\n        ...message,\n        ...(m.content\n          ? { content: [...initialContent, ...(m.content ?? [])] }\n          : undefined),\n        status: m.status ?? message.status,\n        ...(m.metadata\n          ? {\n              metadata: {\n                ...message.metadata,\n                ...(m.metadata.unstable_state\n                  ? { unstable_state: m.metadata.unstable_state }\n                  : undefined),\n                ...(annotations\n                  ? { unstable_annotations: annotations }\n                  : undefined),\n                ...(data ? { unstable_data: data } : undefined),\n                ...(steps ? { steps } : undefined),\n                ...(m.metadata?.custom\n                  ? {\n                      custom: { ...(initalCustom ?? {}), ...m.metadata.custom },\n                    }\n                  : undefined),\n              },\n            }\n          : undefined),\n      };\n      this.repository.addOrUpdateMessage(parentId, message);\n      this._notifySubscribers();\n    };\n\n    const maxSteps = this._options.maxSteps ?? 2;\n\n    const steps = message.metadata?.steps?.length ?? 0;\n    if (steps >= maxSteps) {\n      // reached max tool steps\n      updateMessage({\n        status: {\n          type: \"incomplete\",\n          reason: \"tool-calls\",\n        },\n      });\n      return message;\n    } else {\n      updateMessage({\n        status: {\n          type: \"running\",\n        },\n      });\n    }\n\n    try {\n      this._lastRunConfig = runConfig ?? {};\n      const context = this.getModelContext();\n\n      runCallback =\n        runCallback ??\n        this.adapters.chatModel.run.bind(this.adapters.chatModel);\n\n      const abortSignal = this.abortController.signal;\n      const promiseOrGenerator = runCallback({\n        messages,\n        runConfig: this._lastRunConfig,\n        abortSignal,\n        context,\n        config: context,\n        unstable_assistantMessageId: message.id,\n        unstable_getMessage() {\n          return message;\n        },\n      });\n\n      // handle async iterator for streaming results\n      if (Symbol.asyncIterator in promiseOrGenerator) {\n        for await (const r of promiseOrGenerator) {\n          if (abortSignal.aborted) {\n            updateMessage({\n              status: { type: \"incomplete\", reason: \"cancelled\" },\n            });\n            break;\n          }\n\n          updateMessage(r);\n        }\n      } else {\n        updateMessage(await promiseOrGenerator);\n      }\n\n      if (message.status.type === \"running\") {\n        updateMessage({\n          status: { type: \"complete\", reason: \"unknown\" },\n        });\n      }\n    } catch (e) {\n      // TODO this should be handled by the run result stream\n      if (e instanceof AbortError) {\n        updateMessage({\n          status: { type: \"incomplete\", reason: \"cancelled\" },\n        });\n      } else {\n        updateMessage({\n          status: {\n            type: \"incomplete\",\n            reason: \"error\",\n            error:\n              e instanceof Error\n                ? e.message\n                : `[${typeof e}] ${new String(e).toString()}`,\n          },\n        });\n\n        throw e;\n      }\n    } finally {\n      this.abortController = null;\n\n      if (\n        message.status.type === \"complete\" ||\n        message.status.type === \"incomplete\"\n      ) {\n        await this._options.adapters.history?.append({\n          parentId,\n          message: message,\n        });\n      }\n    }\n    return message;\n  }\n\n  public detach() {\n    const error = new AbortError(true);\n    this.abortController?.abort(error);\n    this.abortController = null;\n  }\n\n  public cancelRun() {\n    const error = new AbortError(false);\n    this.abortController?.abort(error);\n    this.abortController = null;\n  }\n\n  public addToolResult({\n    messageId,\n    toolCallId,\n    result,\n    isError,\n    artifact,\n  }: AddToolResultOptions) {\n    const messageData = this.repository.getMessage(messageId);\n    const { parentId } = messageData;\n    let { message } = messageData;\n\n    if (message.role !== \"assistant\")\n      throw new Error(\"Tried to add tool result to non-assistant message\");\n\n    let added = false;\n    let found = false;\n    const newContent = message.content.map((c) => {\n      if (c.type !== \"tool-call\") return c;\n      if (c.toolCallId !== toolCallId) return c;\n      found = true;\n      if (!c.result) added = true;\n      return {\n        ...c,\n        result,\n        artifact,\n        isError,\n      };\n    });\n\n    if (!found)\n      throw new Error(\"Tried to add tool result to non-existing tool call\");\n\n    message = {\n      ...message,\n      content: newContent,\n    };\n    this.repository.addOrUpdateMessage(parentId, message);\n\n    if (\n      added &&\n      shouldContinue(message, this._options.unstable_humanToolNames)\n    ) {\n      this.performRoundtrip(parentId, message, this._lastRunConfig);\n    }\n  }\n}\n"], "mappings": ";AAAA,SAAS,uBAAuB,kBAAkB;AAGlD,SAAS,sBAAsB;AAS/B,SAAS,6BAA6B;AAItC,IAAM,aAAN,cAAyB,MAAM;AAAA,EACpB,OAAO;AAAA,EAChB;AAAA,EAEA,YAAY,QAAiB,SAAkB;AAC7C,UAAM,OAAO;AACb,SAAK,SAAS;AAAA,EAChB;AACF;AAEO,IAAM,yBAAN,cACG,sBAEV;AAAA,EACkB,eAAe;AAAA,IAC7B,gBAAgB;AAAA,IAChB,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,UAAU;AAAA,EACZ;AAAA,EAEQ,kBAA0C;AAAA,EAElC,aAAa;AAAA,EAErB,aAAa;AAAA,EACrB,IAAW,YAAY;AACrB,WAAO,KAAK;AAAA,EACd;AAAA,EAEQ,eAA4C,CAAC;AAAA,EAC7C,yBAAiD;AAAA,EACzD,IAAW,cAA2C;AACpD,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAW,WAAW;AACpB,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EAEA,YACE,iBACA,SACA;AACA,UAAM,eAAe;AACrB,SAAK,sBAAsB,OAAO;AAAA,EACpC;AAAA,EAEQ;AAAA,EAEA,iBAA4B,CAAC;AAAA,EAErC,IAAW,SAAS;AAClB,WAAO;AAAA,EACT;AAAA,EAEO,sBAAsB,SAAkC;AAC7D,QAAI,KAAK,aAAa,QAAS;AAE/B,SAAK,WAAW;AAEhB,QAAI,aAAa;AAEjB,UAAM,WAAW,QAAQ,UAAU,WAAW;AAC9C,QAAI,KAAK,aAAa,WAAW,UAAU;AACzC,WAAK,aAAa,SAAS;AAC3B,mBAAa;AAAA,IACf;AAEA,UAAM,YAAY,QAAQ,UAAU,gBAAgB;AACpD,QAAI,KAAK,aAAa,gBAAgB,WAAW;AAC/C,WAAK,aAAa,cAAc;AAChC,mBAAa;AAAA,IACf;AAEA,UAAM,cAAc,QAAQ,UAAU,aAAa;AACnD,QAAI,KAAK,aAAa,aAAa,aAAa;AAC9C,WAAK,aAAa,WAAW;AAC7B,mBAAa;AAAA,IACf;AAEA,QAAI,WAAY,MAAK,mBAAmB;AAAA,EAC1C;AAAA,EAEQ;AAAA,EACD,kBAAkB;AACvB,QAAI,KAAK,aAAc,QAAO,KAAK;AAEnC,UAAM,UAAU,KAAK,SAAS,SAAS,KAAK,KAAK,QAAQ,QAAQ,IAAI;AAErE,SAAK,aAAa;AAClB,SAAK,mBAAmB;AAExB,SAAK,eAAe,QACjB,KAAK,CAAC,SAAS;AACd,UAAI,CAAC,KAAM;AACX,WAAK,WAAW,OAAO,IAAI;AAC3B,WAAK,mBAAmB;AAExB,YAAM,SAAS,KAAK,SAAS,SAAS,QAAQ;AAAA,QAC5C,KAAK,SAAS;AAAA,MAChB;AACA,UAAI,KAAK,mBAAmB,QAAQ;AAClC,aAAK;AAAA,UACH;AAAA,YACE,UAAU,KAAK,WAAW;AAAA,YAC1B,UAAU,KAAK,WAAW;AAAA,YAC1B,WAAW,KAAK;AAAA,UAClB;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC,EACA,QAAQ,MAAM;AACb,WAAK,aAAa;AAClB,WAAK,mBAAmB;AAAA,IAC1B,CAAC;AAEH,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,MAAa,OAAO,SAAuC;AACzD,SAAK,kBAAkB;AAEvB,UAAM,aAAa,sBAAsB,SAAS,WAAW,GAAG;AAAA,MAC9D,MAAM;AAAA,MACN,QAAQ;AAAA,IACV,CAAC;AACD,SAAK,WAAW,mBAAmB,QAAQ,UAAU,UAAU;AAC/D,SAAK,SAAS,SAAS,SAAS,OAAO;AAAA,MACrC,UAAU,QAAQ;AAAA,MAClB,SAAS;AAAA,IACX,CAAC;AAED,UAAM,WAAW,QAAQ,YAAY,QAAQ,SAAS;AACtD,QAAI,UAAU;AACZ,YAAM,KAAK,SAAS;AAAA,QAClB,UAAU,WAAW;AAAA,QACrB,UAAU,QAAQ;AAAA,QAClB,WAAW,QAAQ,aAAa,CAAC;AAAA,MACnC,CAAC;AAAA,IACH,OAAO;AACL,WAAK,WAAW,UAAU,WAAW,EAAE;AACvC,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA,EAEO,UAAU,EAAE,QAAQ,GAAG,YAAY,GAAmC;AAC3E,WAAO,KAAK,SAAS,aAAa,MAAM;AAAA,EAC1C;AAAA,EAEA,MAAa,SACX,EAAE,UAAU,UAAU,GACtB,aACe;AACf,SAAK,kBAAkB;AAEvB,SAAK,WAAW,UAAU,QAAQ;AAGlC,UAAM,KAAK,WAAW;AACtB,QAAI,UAAkC;AAAA,MACpC;AAAA,MACA,MAAM;AAAA,MACN,QAAQ,EAAE,MAAM,UAAU;AAAA,MAC1B,SAAS,CAAC;AAAA,MACV,UAAU;AAAA,QACR,gBAAgB,KAAK;AAAA,QACrB,sBAAsB,CAAC;AAAA,QACvB,eAAe,CAAC;AAAA,QAChB,OAAO,CAAC;AAAA,QACR,QAAQ,CAAC;AAAA,MACX;AAAA,MACA,WAAW,oBAAI,KAAK;AAAA,IACtB;AAEA,SAAK,wBAAwB,WAAW;AAExC,QAAI;AACF,WAAK,eAAe,CAAC;AACrB,WAAK,wBAAwB,MAAM;AACnC,WAAK,yBAAyB;AAE9B,SAAG;AACD,kBAAU,MAAM,KAAK;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AACA,sBAAc;AAAA,MAChB,SAAS,eAAe,SAAS,KAAK,SAAS,uBAAuB;AAAA,IACxE,UAAE;AACA,WAAK,wBAAwB,SAAS;AAAA,IACxC;AAEA,SAAK,yBAAyB,IAAI,gBAAgB;AAClD,UAAM,SAAS,KAAK,uBAAuB;AAC3C,QACE,KAAK,SAAS,cACd,QAAQ,QAAQ,SAAS,mBACzB;AACA,YAAM,qBAAqB,KAAK,SAAS,YAAY,SAAS;AAAA,QAC5D,UAAU,KAAK;AAAA,MACjB,CAAC;AAED,UAAI,OAAO,iBAAiB,oBAAoB;AAC9C,yBAAiB,KAAK,oBAAoB;AACxC,cAAI,OAAO,QAAS;AACpB,eAAK,eAAe;AAAA,QACtB;AAAA,MACF,OAAO;AACL,cAAM,SAAS,MAAM;AACrB,YAAI,OAAO,QAAS;AACpB,aAAK,eAAe;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAc,iBACZ,UACA,SACA,WACA,aACA;AACA,UAAM,WAAW,KAAK,WAAW,YAAY;AAG7C,SAAK,iBAAiB,MAAM;AAC5B,SAAK,kBAAkB,IAAI,gBAAgB;AAE3C,UAAM,iBAAiB,QAAQ;AAC/B,UAAM,qBAAqB,QAAQ,UAAU;AAC7C,UAAM,cAAc,QAAQ,UAAU;AACtC,UAAM,eAAe,QAAQ,UAAU;AACvC,UAAM,eAAe,QAAQ,UAAU;AACvC,UAAM,gBAAgB,CAAC,MAAmC;AACxD,YAAM,WAAW,EAAE,UAAU;AAC7B,YAAMA,SAAQ,WACV,CAAC,GAAI,gBAAgB,CAAC,GAAI,GAAG,QAAQ,IACrC;AAEJ,YAAM,iBAAiB,EAAE,UAAU;AACnC,YAAM,UAAU,EAAE,UAAU;AAC5B,YAAM,cAAc,iBAChB,CAAC,GAAI,sBAAsB,CAAC,GAAI,GAAG,cAAc,IACjD;AACJ,YAAM,OAAO,UAAU,CAAC,GAAI,eAAe,CAAC,GAAI,GAAG,OAAO,IAAI;AAE9D,gBAAU;AAAA,QACR,GAAG;AAAA,QACH,GAAI,EAAE,UACF,EAAE,SAAS,CAAC,GAAG,gBAAgB,GAAI,EAAE,WAAW,CAAC,CAAE,EAAE,IACrD;AAAA,QACJ,QAAQ,EAAE,UAAU,QAAQ;AAAA,QAC5B,GAAI,EAAE,WACF;AAAA,UACE,UAAU;AAAA,YACR,GAAG,QAAQ;AAAA,YACX,GAAI,EAAE,SAAS,iBACX,EAAE,gBAAgB,EAAE,SAAS,eAAe,IAC5C;AAAA,YACJ,GAAI,cACA,EAAE,sBAAsB,YAAY,IACpC;AAAA,YACJ,GAAI,OAAO,EAAE,eAAe,KAAK,IAAI;AAAA,YACrC,GAAIA,SAAQ,EAAE,OAAAA,OAAM,IAAI;AAAA,YACxB,GAAI,EAAE,UAAU,SACZ;AAAA,cACE,QAAQ,EAAE,GAAI,gBAAgB,CAAC,GAAI,GAAG,EAAE,SAAS,OAAO;AAAA,YAC1D,IACA;AAAA,UACN;AAAA,QACF,IACA;AAAA,MACN;AACA,WAAK,WAAW,mBAAmB,UAAU,OAAO;AACpD,WAAK,mBAAmB;AAAA,IAC1B;AAEA,UAAM,WAAW,KAAK,SAAS,YAAY;AAE3C,UAAM,QAAQ,QAAQ,UAAU,OAAO,UAAU;AACjD,QAAI,SAAS,UAAU;AAErB,oBAAc;AAAA,QACZ,QAAQ;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,QACV;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT,OAAO;AACL,oBAAc;AAAA,QACZ,QAAQ;AAAA,UACN,MAAM;AAAA,QACR;AAAA,MACF,CAAC;AAAA,IACH;AAEA,QAAI;AACF,WAAK,iBAAiB,aAAa,CAAC;AACpC,YAAM,UAAU,KAAK,gBAAgB;AAErC,oBACE,eACA,KAAK,SAAS,UAAU,IAAI,KAAK,KAAK,SAAS,SAAS;AAE1D,YAAM,cAAc,KAAK,gBAAgB;AACzC,YAAM,qBAAqB,YAAY;AAAA,QACrC;AAAA,QACA,WAAW,KAAK;AAAA,QAChB;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,QACR,6BAA6B,QAAQ;AAAA,QACrC,sBAAsB;AACpB,iBAAO;AAAA,QACT;AAAA,MACF,CAAC;AAGD,UAAI,OAAO,iBAAiB,oBAAoB;AAC9C,yBAAiB,KAAK,oBAAoB;AACxC,cAAI,YAAY,SAAS;AACvB,0BAAc;AAAA,cACZ,QAAQ,EAAE,MAAM,cAAc,QAAQ,YAAY;AAAA,YACpD,CAAC;AACD;AAAA,UACF;AAEA,wBAAc,CAAC;AAAA,QACjB;AAAA,MACF,OAAO;AACL,sBAAc,MAAM,kBAAkB;AAAA,MACxC;AAEA,UAAI,QAAQ,OAAO,SAAS,WAAW;AACrC,sBAAc;AAAA,UACZ,QAAQ,EAAE,MAAM,YAAY,QAAQ,UAAU;AAAA,QAChD,CAAC;AAAA,MACH;AAAA,IACF,SAAS,GAAG;AAEV,UAAI,aAAa,YAAY;AAC3B,sBAAc;AAAA,UACZ,QAAQ,EAAE,MAAM,cAAc,QAAQ,YAAY;AAAA,QACpD,CAAC;AAAA,MACH,OAAO;AACL,sBAAc;AAAA,UACZ,QAAQ;AAAA,YACN,MAAM;AAAA,YACN,QAAQ;AAAA,YACR,OACE,aAAa,QACT,EAAE,UACF,IAAI,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,EAAE,SAAS,CAAC;AAAA,UACjD;AAAA,QACF,CAAC;AAED,cAAM;AAAA,MACR;AAAA,IACF,UAAE;AACA,WAAK,kBAAkB;AAEvB,UACE,QAAQ,OAAO,SAAS,cACxB,QAAQ,OAAO,SAAS,cACxB;AACA,cAAM,KAAK,SAAS,SAAS,SAAS,OAAO;AAAA,UAC3C;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EAEO,SAAS;AACd,UAAM,QAAQ,IAAI,WAAW,IAAI;AACjC,SAAK,iBAAiB,MAAM,KAAK;AACjC,SAAK,kBAAkB;AAAA,EACzB;AAAA,EAEO,YAAY;AACjB,UAAM,QAAQ,IAAI,WAAW,KAAK;AAClC,SAAK,iBAAiB,MAAM,KAAK;AACjC,SAAK,kBAAkB;AAAA,EACzB;AAAA,EAEO,cAAc;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAyB;AACvB,UAAM,cAAc,KAAK,WAAW,WAAW,SAAS;AACxD,UAAM,EAAE,SAAS,IAAI;AACrB,QAAI,EAAE,QAAQ,IAAI;AAElB,QAAI,QAAQ,SAAS;AACnB,YAAM,IAAI,MAAM,mDAAmD;AAErE,QAAI,QAAQ;AACZ,QAAI,QAAQ;AACZ,UAAM,aAAa,QAAQ,QAAQ,IAAI,CAAC,MAAM;AAC5C,UAAI,EAAE,SAAS,YAAa,QAAO;AACnC,UAAI,EAAE,eAAe,WAAY,QAAO;AACxC,cAAQ;AACR,UAAI,CAAC,EAAE,OAAQ,SAAQ;AACvB,aAAO;AAAA,QACL,GAAG;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC;AAED,QAAI,CAAC;AACH,YAAM,IAAI,MAAM,oDAAoD;AAEtE,cAAU;AAAA,MACR,GAAG;AAAA,MACH,SAAS;AAAA,IACX;AACA,SAAK,WAAW,mBAAmB,UAAU,OAAO;AAEpD,QACE,SACA,eAAe,SAAS,KAAK,SAAS,uBAAuB,GAC7D;AACA,WAAK,iBAAiB,UAAU,SAAS,KAAK,cAAc;AAAA,IAC9D;AAAA,EACF;AACF;", "names": ["steps"]}