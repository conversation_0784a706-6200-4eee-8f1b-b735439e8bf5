{"version": 3, "sources": ["../../../src/runtimes/external-store/auto-status.tsx"], "sourcesContent": ["import { MessageStatus } from \"../../types\";\n\nconst AUTO_STATUS_RUNNING = Object.freeze({ type: \"running\" });\nconst AUTO_STATUS_COMPLETE = Object.freeze({\n  type: \"complete\",\n  reason: \"unknown\",\n});\n\nconst AUTO_STATUS_PENDING = Object.freeze({\n  type: \"requires-action\",\n  reason: \"tool-calls\",\n});\n\nexport const isAutoStatus = (status: MessageStatus) =>\n  status === AUTO_STATUS_RUNNING || status === AUTO_STATUS_COMPLETE;\n\nexport const getAutoStatus = (\n  isLast: boolean,\n  isRunning: boolean,\n  hasPendingToolCalls: boolean,\n) =>\n  isLast && isRunning\n    ? AUTO_STATUS_RUNNING\n    : hasPendingToolCalls\n      ? AUTO_STATUS_PENDING\n      : AUTO_STATUS_COMPLETE;\n"], "mappings": ";AAEA,IAAM,sBAAsB,OAAO,OAAO,EAAE,MAAM,UAAU,CAAC;AAC7D,IAAM,uBAAuB,OAAO,OAAO;AAAA,EACzC,MAAM;AAAA,EACN,QAAQ;AACV,CAAC;AAED,IAAM,sBAAsB,OAAO,OAAO;AAAA,EACxC,MAAM;AAAA,EACN,QAAQ;AACV,CAAC;AAEM,IAAM,eAAe,CAAC,WAC3B,WAAW,uBAAuB,WAAW;AAExC,IAAM,gBAAgB,CAC3B,QACA,WACA,wBAEA,UAAU,YACN,sBACA,sBACE,sBACA;", "names": []}