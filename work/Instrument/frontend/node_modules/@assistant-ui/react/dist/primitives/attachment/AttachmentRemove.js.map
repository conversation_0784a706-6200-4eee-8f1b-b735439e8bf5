{"version": 3, "sources": ["../../../src/primitives/attachment/AttachmentRemove.tsx"], "sourcesContent": ["\"use client\";\n\nimport {\n  ActionButtonElement,\n  ActionButtonProps,\n  createActionButton,\n} from \"../../utils/createActionButton\";\nimport { useCallback } from \"react\";\nimport { useAttachmentRuntime } from \"../../context/react/AttachmentContext\";\n\nconst useAttachmentRemove = () => {\n  const attachmentRuntime = useAttachmentRuntime();\n\n  const handleRemoveAttachment = useCallback(() => {\n    attachmentRuntime.remove();\n  }, [attachmentRuntime]);\n\n  return handleRemoveAttachment;\n};\n\nexport namespace AttachmentPrimitiveRemove {\n  export type Element = ActionButtonElement;\n  export type Props = ActionButtonProps<typeof useAttachmentRemove>;\n}\n\nexport const AttachmentPrimitiveRemove = createActionButton(\n  \"AttachmentPrimitive.Remove\",\n  useAttachmentRemove,\n);\n"], "mappings": ";;;AAEA;AAAA,EAGE;AAAA,OACK;AACP,SAAS,mBAAmB;AAC5B,SAAS,4BAA4B;AAErC,IAAM,sBAAsB,MAAM;AAChC,QAAM,oBAAoB,qBAAqB;AAE/C,QAAM,yBAAyB,YAAY,MAAM;AAC/C,sBAAkB,OAAO;AAAA,EAC3B,GAAG,CAAC,iBAAiB,CAAC;AAEtB,SAAO;AACT;AAOO,IAAM,4BAA4B;AAAA,EACvC;AAAA,EACA;AACF;", "names": []}