"use client";

// src/primitives/messagePart/MessagePartImage.tsx
import { Primitive } from "@radix-ui/react-primitive";
import { forwardRef } from "react";
import { useMessagePartImage } from "./useMessagePartImage.js";
import { jsx } from "react/jsx-runtime";
var MessagePartPrimitiveImage = forwardRef((props, forwardedRef) => {
  const { image } = useMessagePartImage();
  return /* @__PURE__ */ jsx(Primitive.img, { src: image, ...props, ref: forwardedRef });
});
MessagePartPrimitiveImage.displayName = "MessagePartPrimitive.Image";
export {
  MessagePartPrimitiveImage
};
//# sourceMappingURL=MessagePartImage.js.map