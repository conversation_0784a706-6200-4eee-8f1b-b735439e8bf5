"use client";

// src/primitives/assistantModal/AssistantModalAnchor.tsx
import { forwardRef } from "react";
import * as PopoverPrimitive from "@radix-ui/react-popover";
import { usePopoverScope } from "./scope.js";
import { jsx } from "react/jsx-runtime";
var AssistantModalPrimitiveAnchor = forwardRef(
  ({
    __scopeAssistantModal,
    ...rest
  }, ref) => {
    const scope = usePopoverScope(__scopeAssistantModal);
    return /* @__PURE__ */ jsx(PopoverPrimitive.Anchor, { ...scope, ...rest, ref });
  }
);
AssistantModalPrimitiveAnchor.displayName = "AssistantModalPrimitive.Anchor";
export {
  AssistantModalPrimitiveAnchor
};
//# sourceMappingURL=AssistantModalAnchor.js.map