import { LocalRuntime } from "@assistant-ui/react";
import type { 
  AppendMessage, 
  ThreadMessage,
  CoreMessage,
  TextContentPart,
  ImageContentPart
} from "@assistant-ui/react";

export interface DeviceInfo {
  name: string;
  host: string;
  port: number;
}

export interface InstrumentRuntimeConfig {
  backendUrl: string;
  wsUrl: string;
  onDeviceStatusChange?: (connected: boolean, deviceInfo: DeviceInfo | null) => void;
  onImagesReceived?: (images: string[]) => void;
}

export class InstrumentRuntime extends LocalRuntime {
  private ws: WebSocket | null = null;
  private config: InstrumentRuntimeConfig;
  private deviceConnected = false;
  private deviceInfo: DeviceInfo | null = null;
  private currentImages: string[] = [];

  constructor(config: InstrumentRuntimeConfig) {
    super();
    this.config = config;
    this.connectWebSocket();
  }

  private connectWebSocket() {
    try {
      this.ws = new WebSocket(this.config.wsUrl);

      this.ws.onopen = () => {
        console.log('Connected to backend WebSocket');
      };

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          this.handleWebSocketMessage(data);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      this.ws.onclose = () => {
        console.log('WebSocket connection closed');
        // Attempt to reconnect after 3 seconds
        setTimeout(() => this.connectWebSocket(), 3000);
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
      };
    } catch (error) {
      console.error('Failed to connect WebSocket:', error);
    }
  }

  private handleWebSocketMessage(data: any) {
    switch (data.type) {
      case 'device_status':
        this.deviceConnected = data.connected;
        this.deviceInfo = data.deviceInfo;
        this.config.onDeviceStatusChange?.(this.deviceConnected, this.deviceInfo);
        break;

      case 'images':
        if (data.images && Array.isArray(data.images)) {
          this.currentImages = data.images;
          this.config.onImagesReceived?.(data.images);
          console.log(`Received ${data.images.length} images`);
        }
        break;

      case 'error':
        console.error('Device error:', data.message);
        break;

      default:
        console.log('Unknown message type:', data.type);
    }
  }

  // Send command to device via WebSocket
  public sendDeviceCommand(command: string, params: any = {}) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({
        type: command,
        ...params
      }));
    } else {
      throw new Error('Not connected to backend');
    }
  }

  // Capture images from device
  public captureImages(count: number = 1) {
    this.sendDeviceCommand('capture', { count });
  }

  // Send command to device
  public sendToDevice() {
    this.sendDeviceCommand('send');
  }

  // Get current device status
  public getDeviceStatus() {
    return {
      connected: this.deviceConnected,
      deviceInfo: this.deviceInfo
    };
  }

  // Get current images
  public getCurrentImages() {
    return this.currentImages;
  }

  // Override the append method to handle image analysis
  public override async append(message: AppendMessage): Promise<void> {
    // If this is a user message and we have images, include them
    if (message.role === "user" && this.currentImages.length > 0) {
      const content: (TextContentPart | ImageContentPart)[] = [];
      
      // Add text content if present
      if (typeof message.content === "string") {
        content.push({
          type: "text",
          text: message.content
        });
      } else if (Array.isArray(message.content)) {
        content.push(...message.content);
      }

      // Add images as content parts
      this.currentImages.forEach(imageBase64 => {
        content.push({
          type: "image",
          image: `data:image/jpeg;base64,${imageBase64}`
        });
      });

      // Create message with images
      const messageWithImages: AppendMessage = {
        ...message,
        content
      };

      // Call the parent append method
      await super.append(messageWithImages);
    } else {
      // No images, proceed normally
      await super.append(message);
    }
  }

  // Custom adapter for the backend streaming API
  protected override async run({ messages }: { messages: readonly CoreMessage[] }): Promise<void> {
    try {
      // Extract images from the last user message
      const lastUserMessage = messages.findLast(m => m.role === "user");
      const images: string[] = [];
      let context = "";

      if (lastUserMessage) {
        if (typeof lastUserMessage.content === "string") {
          context = lastUserMessage.content;
        } else if (Array.isArray(lastUserMessage.content)) {
          for (const part of lastUserMessage.content) {
            if (part.type === "text") {
              context += part.text;
            } else if (part.type === "image") {
              // Extract base64 from data URL
              const base64 = part.image.replace(/^data:image\/[a-z]+;base64,/, '');
              images.push(base64);
            }
          }
        }
      }

      // If no images in message content, use current images
      const imagesToSend = images.length > 0 ? images : this.currentImages;

      if (imagesToSend.length === 0) {
        throw new Error('No images available for analysis');
      }

      // Make request to backend
      const response = await fetch(`${this.config.backendUrl}/api/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          images: imagesToSend,
          context: context.trim()
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Handle streaming response
      const reader = response.body?.getReader();
      const decoder = new TextDecoder();

      if (!reader) {
        throw new Error('No response body');
      }

      while (true) {
        const { done, value } = await reader.read();

        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);

            if (data === '[DONE]') {
              return;
            }

            try {
              const parsed = JSON.parse(data);
              if (parsed.text) {
                // Stream the text to the assistant message
                this.appendText(parsed.text);
              } else if (parsed.error) {
                throw new Error(parsed.error);
              }
            } catch (parseError) {
              // Ignore parsing errors for partial chunks
            }
          }
        }
      }
    } catch (error) {
      console.error('Analysis error:', error);
      throw error;
    }
  }

  // Clean up WebSocket connection
  public destroy() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }
}
