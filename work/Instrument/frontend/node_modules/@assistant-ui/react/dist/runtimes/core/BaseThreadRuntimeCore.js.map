{"version": 3, "sources": ["../../../src/runtimes/core/BaseThreadRuntimeCore.tsx"], "sourcesContent": ["import type { AppendMessage, Unsubscribe } from \"../../types\";\nimport {\n  ExportedMessageRepository,\n  MessageRepository,\n} from \"../utils/MessageRepository\";\nimport { DefaultThreadComposerRuntimeCore } from \"../composer/DefaultThreadComposerRuntimeCore\";\nimport {\n  AddToolResultOptions,\n  ThreadSuggestion,\n  SubmitFeedbackOptions,\n  ThreadRuntimeCore,\n  SpeechState,\n  RuntimeCapabilities,\n  SubmittedFeedback,\n  ThreadRuntimeEventType,\n  StartRunConfig,\n  ResumeRunConfig,\n} from \"../core/ThreadRuntimeCore\";\nimport { DefaultEditComposerRuntimeCore } from \"../composer/DefaultEditComposerRuntimeCore\";\nimport { SpeechSynthesisAdapter } from \"../adapters/speech/SpeechAdapterTypes\";\nimport { FeedbackAdapter } from \"../adapters/feedback/FeedbackAdapter\";\nimport { AttachmentAdapter } from \"../adapters/attachment\";\nimport { getThreadMessageText } from \"../../utils/getThreadMessageText\";\nimport { ModelContextProvider } from \"../../model-context\";\nimport { ThreadMessageLike } from \"../external-store\";\n\ntype BaseThreadAdapters = {\n  speech?: SpeechSynthesisAdapter | undefined;\n  feedback?: FeedbackAdapter | undefined;\n  attachments?: AttachmentAdapter | undefined;\n};\n\nexport abstract class BaseThreadRuntimeCore implements ThreadRuntimeCore {\n  private _subscriptions = new Set<() => void>();\n  private _isInitialized = false;\n\n  protected readonly repository = new MessageRepository();\n  public abstract get adapters(): BaseThreadAdapters | undefined;\n  public abstract get isDisabled(): boolean;\n  public abstract get isLoading(): boolean;\n  public abstract get suggestions(): readonly ThreadSuggestion[];\n  public abstract get extras(): unknown;\n\n  public abstract get capabilities(): RuntimeCapabilities;\n  public abstract append(message: AppendMessage): void;\n  public abstract startRun(config: StartRunConfig): void;\n  public abstract resumeRun(config: ResumeRunConfig): void;\n  public abstract addToolResult(options: AddToolResultOptions): void;\n  public abstract cancelRun(): void;\n\n  public get messages() {\n    return this.repository.getMessages();\n  }\n\n  public get state() {\n    let mostRecentAssistantMessage;\n    for (const message of this.messages) {\n      if (message.role === \"assistant\") {\n        mostRecentAssistantMessage = message;\n        break;\n      }\n    }\n\n    return mostRecentAssistantMessage?.metadata.unstable_state ?? null;\n  }\n\n  public readonly composer = new DefaultThreadComposerRuntimeCore(this);\n\n  constructor(private readonly _contextProvider: ModelContextProvider) {}\n\n  public getModelContext() {\n    return this._contextProvider.getModelContext();\n  }\n\n  private _editComposers = new Map<string, DefaultEditComposerRuntimeCore>();\n  public getEditComposer(messageId: string) {\n    return this._editComposers.get(messageId);\n  }\n  public beginEdit(messageId: string) {\n    if (this._editComposers.has(messageId))\n      throw new Error(\"Edit already in progress\");\n\n    this._editComposers.set(\n      messageId,\n      new DefaultEditComposerRuntimeCore(\n        this,\n        () => this._editComposers.delete(messageId),\n        this.repository.getMessage(messageId),\n      ),\n    );\n    this._notifySubscribers();\n  }\n\n  public getMessageById(messageId: string) {\n    return this.repository.getMessage(messageId);\n  }\n\n  public getBranches(messageId: string): string[] {\n    return this.repository.getBranches(messageId);\n  }\n\n  public switchToBranch(branchId: string): void {\n    this.repository.switchToBranch(branchId);\n    this._notifySubscribers();\n  }\n\n  protected _notifySubscribers() {\n    for (const callback of this._subscriptions) callback();\n  }\n\n  public _notifyEventSubscribers(event: ThreadRuntimeEventType) {\n    const subscribers = this._eventSubscribers.get(event);\n    if (!subscribers) return;\n\n    for (const callback of subscribers) callback();\n  }\n\n  public subscribe(callback: () => void): Unsubscribe {\n    this._subscriptions.add(callback);\n    return () => this._subscriptions.delete(callback);\n  }\n\n  private _submittedFeedback: Record<string, SubmittedFeedback> = {};\n\n  public getSubmittedFeedback(messageId: string) {\n    return this._submittedFeedback[messageId];\n  }\n\n  public submitFeedback({ messageId, type }: SubmitFeedbackOptions) {\n    const adapter = this.adapters?.feedback;\n    if (!adapter) throw new Error(\"Feedback adapter not configured\");\n\n    const { message } = this.repository.getMessage(messageId);\n    adapter.submit({ message, type });\n\n    this._submittedFeedback[messageId] = { type };\n    this._notifySubscribers();\n  }\n\n  private _stopSpeaking: Unsubscribe | undefined;\n  public speech: SpeechState | undefined;\n\n  public speak(messageId: string) {\n    const adapter = this.adapters?.speech;\n    if (!adapter) throw new Error(\"Speech adapter not configured\");\n\n    const { message } = this.repository.getMessage(messageId);\n\n    this._stopSpeaking?.();\n\n    const utterance = adapter.speak(getThreadMessageText(message));\n    const unsub = utterance.subscribe(() => {\n      if (utterance.status.type === \"ended\") {\n        this._stopSpeaking = undefined;\n        this.speech = undefined;\n      } else {\n        this.speech = { messageId, status: utterance.status };\n      }\n      this._notifySubscribers();\n    });\n\n    this.speech = { messageId, status: utterance.status };\n    this._notifySubscribers();\n\n    this._stopSpeaking = () => {\n      utterance.cancel();\n      unsub();\n      this.speech = undefined;\n      this._stopSpeaking = undefined;\n    };\n  }\n\n  public stopSpeaking() {\n    if (!this._stopSpeaking) throw new Error(\"No message is being spoken\");\n    this._stopSpeaking();\n    this._notifySubscribers();\n  }\n\n  protected ensureInitialized() {\n    if (!this._isInitialized) {\n      this._isInitialized = true;\n      this._notifyEventSubscribers(\"initialize\");\n    }\n  }\n\n  // TODO import()/export() on external store doesn't make much sense\n  public export() {\n    return this.repository.export();\n  }\n\n  public import(data: ExportedMessageRepository) {\n    this.ensureInitialized();\n    this.repository.clear();\n    this.repository.import(data);\n    this._notifySubscribers();\n  }\n\n  public reset(initialMessages?: readonly ThreadMessageLike[]) {\n    this.import(ExportedMessageRepository.fromArray(initialMessages ?? []));\n  }\n\n  private _eventSubscribers = new Map<\n    ThreadRuntimeEventType,\n    Set<() => void>\n  >();\n\n  public unstable_on(event: ThreadRuntimeEventType, callback: () => void) {\n    if (event === \"model-context-update\") {\n      return this._contextProvider.subscribe?.(callback) ?? (() => {});\n    }\n\n    const subscribers = this._eventSubscribers.get(event);\n    if (!subscribers) {\n      this._eventSubscribers.set(event, new Set([callback]));\n    } else {\n      subscribers.add(callback);\n    }\n\n    return () => {\n      const subscribers = this._eventSubscribers.get(event)!;\n      subscribers.delete(callback);\n    };\n  }\n}\n"], "mappings": ";AACA;AAAA,EACE;AAAA,EACA;AAAA,OACK;AACP,SAAS,wCAAwC;AAajD,SAAS,sCAAsC;AAI/C,SAAS,4BAA4B;AAU9B,IAAe,wBAAf,MAAkE;AAAA,EAoCvE,YAA6B,kBAAwC;AAAxC;AAAA,EAAyC;AAAA,EAnC9D,iBAAiB,oBAAI,IAAgB;AAAA,EACrC,iBAAiB;AAAA,EAEN,aAAa,IAAI,kBAAkB;AAAA,EActD,IAAW,WAAW;AACpB,WAAO,KAAK,WAAW,YAAY;AAAA,EACrC;AAAA,EAEA,IAAW,QAAQ;AACjB,QAAI;AACJ,eAAW,WAAW,KAAK,UAAU;AACnC,UAAI,QAAQ,SAAS,aAAa;AAChC,qCAA6B;AAC7B;AAAA,MACF;AAAA,IACF;AAEA,WAAO,4BAA4B,SAAS,kBAAkB;AAAA,EAChE;AAAA,EAEgB,WAAW,IAAI,iCAAiC,IAAI;AAAA,EAI7D,kBAAkB;AACvB,WAAO,KAAK,iBAAiB,gBAAgB;AAAA,EAC/C;AAAA,EAEQ,iBAAiB,oBAAI,IAA4C;AAAA,EAClE,gBAAgB,WAAmB;AACxC,WAAO,KAAK,eAAe,IAAI,SAAS;AAAA,EAC1C;AAAA,EACO,UAAU,WAAmB;AAClC,QAAI,KAAK,eAAe,IAAI,SAAS;AACnC,YAAM,IAAI,MAAM,0BAA0B;AAE5C,SAAK,eAAe;AAAA,MAClB;AAAA,MACA,IAAI;AAAA,QACF;AAAA,QACA,MAAM,KAAK,eAAe,OAAO,SAAS;AAAA,QAC1C,KAAK,WAAW,WAAW,SAAS;AAAA,MACtC;AAAA,IACF;AACA,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EAEO,eAAe,WAAmB;AACvC,WAAO,KAAK,WAAW,WAAW,SAAS;AAAA,EAC7C;AAAA,EAEO,YAAY,WAA6B;AAC9C,WAAO,KAAK,WAAW,YAAY,SAAS;AAAA,EAC9C;AAAA,EAEO,eAAe,UAAwB;AAC5C,SAAK,WAAW,eAAe,QAAQ;AACvC,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EAEU,qBAAqB;AAC7B,eAAW,YAAY,KAAK,eAAgB,UAAS;AAAA,EACvD;AAAA,EAEO,wBAAwB,OAA+B;AAC5D,UAAM,cAAc,KAAK,kBAAkB,IAAI,KAAK;AACpD,QAAI,CAAC,YAAa;AAElB,eAAW,YAAY,YAAa,UAAS;AAAA,EAC/C;AAAA,EAEO,UAAU,UAAmC;AAClD,SAAK,eAAe,IAAI,QAAQ;AAChC,WAAO,MAAM,KAAK,eAAe,OAAO,QAAQ;AAAA,EAClD;AAAA,EAEQ,qBAAwD,CAAC;AAAA,EAE1D,qBAAqB,WAAmB;AAC7C,WAAO,KAAK,mBAAmB,SAAS;AAAA,EAC1C;AAAA,EAEO,eAAe,EAAE,WAAW,KAAK,GAA0B;AAChE,UAAM,UAAU,KAAK,UAAU;AAC/B,QAAI,CAAC,QAAS,OAAM,IAAI,MAAM,iCAAiC;AAE/D,UAAM,EAAE,QAAQ,IAAI,KAAK,WAAW,WAAW,SAAS;AACxD,YAAQ,OAAO,EAAE,SAAS,KAAK,CAAC;AAEhC,SAAK,mBAAmB,SAAS,IAAI,EAAE,KAAK;AAC5C,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EAEQ;AAAA,EACD;AAAA,EAEA,MAAM,WAAmB;AAC9B,UAAM,UAAU,KAAK,UAAU;AAC/B,QAAI,CAAC,QAAS,OAAM,IAAI,MAAM,+BAA+B;AAE7D,UAAM,EAAE,QAAQ,IAAI,KAAK,WAAW,WAAW,SAAS;AAExD,SAAK,gBAAgB;AAErB,UAAM,YAAY,QAAQ,MAAM,qBAAqB,OAAO,CAAC;AAC7D,UAAM,QAAQ,UAAU,UAAU,MAAM;AACtC,UAAI,UAAU,OAAO,SAAS,SAAS;AACrC,aAAK,gBAAgB;AACrB,aAAK,SAAS;AAAA,MAChB,OAAO;AACL,aAAK,SAAS,EAAE,WAAW,QAAQ,UAAU,OAAO;AAAA,MACtD;AACA,WAAK,mBAAmB;AAAA,IAC1B,CAAC;AAED,SAAK,SAAS,EAAE,WAAW,QAAQ,UAAU,OAAO;AACpD,SAAK,mBAAmB;AAExB,SAAK,gBAAgB,MAAM;AACzB,gBAAU,OAAO;AACjB,YAAM;AACN,WAAK,SAAS;AACd,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AAAA,EAEO,eAAe;AACpB,QAAI,CAAC,KAAK,cAAe,OAAM,IAAI,MAAM,4BAA4B;AACrE,SAAK,cAAc;AACnB,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EAEU,oBAAoB;AAC5B,QAAI,CAAC,KAAK,gBAAgB;AACxB,WAAK,iBAAiB;AACtB,WAAK,wBAAwB,YAAY;AAAA,IAC3C;AAAA,EACF;AAAA;AAAA,EAGO,SAAS;AACd,WAAO,KAAK,WAAW,OAAO;AAAA,EAChC;AAAA,EAEO,OAAO,MAAiC;AAC7C,SAAK,kBAAkB;AACvB,SAAK,WAAW,MAAM;AACtB,SAAK,WAAW,OAAO,IAAI;AAC3B,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EAEO,MAAM,iBAAgD;AAC3D,SAAK,OAAO,0BAA0B,UAAU,mBAAmB,CAAC,CAAC,CAAC;AAAA,EACxE;AAAA,EAEQ,oBAAoB,oBAAI,IAG9B;AAAA,EAEK,YAAY,OAA+B,UAAsB;AACtE,QAAI,UAAU,wBAAwB;AACpC,aAAO,KAAK,iBAAiB,YAAY,QAAQ,MAAM,MAAM;AAAA,MAAC;AAAA,IAChE;AAEA,UAAM,cAAc,KAAK,kBAAkB,IAAI,KAAK;AACpD,QAAI,CAAC,aAAa;AAChB,WAAK,kBAAkB,IAAI,OAAO,oBAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;AAAA,IACvD,OAAO;AACL,kBAAY,IAAI,QAAQ;AAAA,IAC1B;AAEA,WAAO,MAAM;AACX,YAAMA,eAAc,KAAK,kBAAkB,IAAI,KAAK;AACpD,MAAAA,aAAY,OAAO,QAAQ;AAAA,IAC7B;AAAA,EACF;AACF;", "names": ["subscribers"]}