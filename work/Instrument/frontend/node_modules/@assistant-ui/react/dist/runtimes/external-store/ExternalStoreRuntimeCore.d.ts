import { BaseAssistantRuntimeCore } from "../core/BaseAssistantRuntimeCore";
import { ExternalStoreThreadListRuntimeCore } from "./ExternalStoreThreadListRuntimeCore";
import { ExternalStoreAdapter } from "./ExternalStoreAdapter";
export declare class ExternalStoreRuntimeCore extends BaseAssistantRuntimeCore {
    readonly threads: ExternalStoreThreadListRuntimeCore;
    constructor(adapter: ExternalStoreAdapter<any>);
    setAdapter(adapter: ExternalStoreAdapter<any>): void;
}
//# sourceMappingURL=ExternalStoreRuntimeCore.d.ts.map