{"version": 3, "sources": ["../../../src/primitives/threadListItem/ThreadListItemTitle.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { FC, ReactNode } from \"react\";\nimport { useThreadListItem } from \"../../context/react/ThreadListItemContext\";\n\nexport namespace ThreadListItemPrimitiveTitle {\n  export type Props = {\n    fallback?: ReactNode;\n  };\n}\n\nexport const ThreadListItemPrimitiveTitle: FC<\n  ThreadListItemPrimitiveTitle.Props\n> = ({ fallback }) => {\n  const title = useThreadListItem((t) => t.title);\n  return <>{title || fallback}</>;\n};\n\nThreadListItemPrimitiveTitle.displayName = \"ThreadListItemPrimitive.Title\";\n"], "mappings": ";;;AAGA,SAAS,yBAAyB;AAYzB;AAJF,IAAM,+BAET,CAAC,EAAE,SAAS,MAAM;AACpB,QAAM,QAAQ,kBAAkB,CAAC,MAAM,EAAE,KAAK;AAC9C,SAAO,gCAAG,mBAAS,UAAS;AAC9B;AAEA,6BAA6B,cAAc;", "names": []}