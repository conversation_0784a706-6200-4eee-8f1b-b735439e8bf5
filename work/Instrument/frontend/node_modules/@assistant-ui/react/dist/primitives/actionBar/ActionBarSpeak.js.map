{"version": 3, "sources": ["../../../src/primitives/actionBar/ActionBarSpeak.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useCallback } from \"react\";\nimport { useMessage, useMessageRuntime } from \"../../context\";\nimport {\n  ActionButtonElement,\n  ActionButtonProps,\n  createActionButton,\n} from \"../../utils/createActionButton\";\n\nconst useActionBarSpeak = () => {\n  const messageRuntime = useMessageRuntime();\n  const callback = useCallback(async () => {\n    messageRuntime.speak();\n  }, [messageRuntime]);\n\n  const hasSpeakableContent = useMessage((m) => {\n    return (\n      (m.role !== \"assistant\" || m.status.type !== \"running\") &&\n      m.content.some((c) => c.type === \"text\" && c.text.length > 0)\n    );\n  });\n\n  if (!hasSpeakableContent) return null;\n  return callback;\n};\n\nexport namespace ActionBarPrimitiveSpeak {\n  export type Element = ActionButtonElement;\n  export type Props = ActionButtonProps<typeof useActionBarSpeak>;\n}\n\nexport const ActionBarPrimitiveSpeak = createActionButton(\n  \"ActionBarPrimitive.Speak\",\n  useActionBarSpeak,\n);\n"], "mappings": ";;;AAEA,SAAS,mBAAmB;AAC5B,SAAS,YAAY,yBAAyB;AAC9C;AAAA,EAGE;AAAA,OACK;AAEP,IAAM,oBAAoB,MAAM;AAC9B,QAAM,iBAAiB,kBAAkB;AACzC,QAAM,WAAW,YAAY,YAAY;AACvC,mBAAe,MAAM;AAAA,EACvB,GAAG,CAAC,cAAc,CAAC;AAEnB,QAAM,sBAAsB,WAAW,CAAC,MAAM;AAC5C,YACG,EAAE,SAAS,eAAe,EAAE,OAAO,SAAS,cAC7C,EAAE,QAAQ,KAAK,CAAC,MAAM,EAAE,SAAS,UAAU,EAAE,KAAK,SAAS,CAAC;AAAA,EAEhE,CAAC;AAED,MAAI,CAAC,oBAAqB,QAAO;AACjC,SAAO;AACT;AAOO,IAAM,0BAA0B;AAAA,EACrC;AAAA,EACA;AACF;", "names": []}