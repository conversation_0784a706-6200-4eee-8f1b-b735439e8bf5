{"version": 3, "sources": ["../../../src/primitives/thread/ThreadScrollToBottom.tsx"], "sourcesContent": ["\"use client\";\n\nimport {\n  ActionButtonElement,\n  ActionButtonProps,\n  createActionButton,\n} from \"../../utils/createActionButton\";\nimport { useCallback } from \"react\";\nimport {\n  useThreadViewport,\n  useThreadViewportStore,\n} from \"../../context/react/ThreadViewportContext\";\n\nconst useThreadScrollToBottom = () => {\n  const isAtBottom = useThreadViewport((s) => s.isAtBottom);\n\n  const threadViewportStore = useThreadViewportStore();\n\n  const handleScrollToBottom = useCallback(() => {\n    threadViewportStore.getState().scrollToBottom();\n  }, [threadViewportStore]);\n\n  if (isAtBottom) return null;\n  return handleScrollToBottom;\n};\n\nexport namespace ThreadPrimitiveScrollToBottom {\n  export type Element = ActionButtonElement;\n  export type Props = ActionButtonProps<typeof useThreadScrollToBottom>;\n}\n\nexport const ThreadPrimitiveScrollToBottom = createActionButton(\n  \"ThreadPrimitive.ScrollToBottom\",\n  useThreadScrollToBottom,\n);\n"], "mappings": ";;;AAEA;AAAA,EAGE;AAAA,OACK;AACP,SAAS,mBAAmB;AAC5B;AAAA,EACE;AAAA,EACA;AAAA,OACK;AAEP,IAAM,0BAA0B,MAAM;AACpC,QAAM,aAAa,kBAAkB,CAAC,MAAM,EAAE,UAAU;AAExD,QAAM,sBAAsB,uBAAuB;AAEnD,QAAM,uBAAuB,YAAY,MAAM;AAC7C,wBAAoB,SAAS,EAAE,eAAe;AAAA,EAChD,GAAG,CAAC,mBAAmB,CAAC;AAExB,MAAI,WAAY,QAAO;AACvB,SAAO;AACT;AAOO,IAAM,gCAAgC;AAAA,EAC3C;AAAA,EACA;AACF;", "names": []}