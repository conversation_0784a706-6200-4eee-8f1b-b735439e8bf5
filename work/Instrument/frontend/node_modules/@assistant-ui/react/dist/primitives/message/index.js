// src/primitives/message/index.ts
import { MessagePrimitiveRoot } from "./MessageRoot.js";
import { MessagePrimitiveParts } from "./MessageParts.js";
import { MessagePrimitivePartByIndex } from "./MessageParts.js";
import { MessagePrimitiveParts as MessagePrimitiveParts2 } from "./MessageParts.js";
import { MessagePrimitiveIf } from "./MessageIf.js";
import { MessagePrimitiveAttachments } from "./MessageAttachments.js";
import { MessagePrimitiveAttachmentByIndex } from "./MessageAttachments.js";
import { MessagePrimitiveError } from "./MessageError.js";
import {
  MessagePrimitiveUnstable_PartsGrouped,
  MessagePrimitiveUnstable_PartsGroupedByParentId
} from "./MessagePartsGrouped.js";
export {
  MessagePrimitiveAttachmentByIndex as AttachmentByIndex,
  MessagePrimitiveAttachments as Attachments,
  MessagePrimitiveParts2 as Content,
  MessagePrimitiveError as Error,
  MessagePrimitiveIf as If,
  MessagePrimitivePartByIndex as PartByIndex,
  MessagePrimitiveParts as Parts,
  MessagePrimitiveRoot as Root,
  MessagePrimitiveUnstable_PartsGrouped as Unstable_PartsGrouped,
  MessagePrimitiveUnstable_PartsGroupedByParentId as Unstable_PartsGroupedByParentId
};
//# sourceMappingURL=index.js.map