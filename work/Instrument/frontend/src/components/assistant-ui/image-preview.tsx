import { FC } from "react";

interface ImagePreviewProps {
  images: string[];
  className?: string;
}

export const ImagePreview: FC<ImagePreviewProps> = ({ images, className = "" }) => {
  if (images.length === 0) return null;

  return (
    <div className={`image-preview ${className}`}>
      <h3>Captured Images:</h3>
      <div className="image-grid">
        {images.slice(0, 3).map((image, index) => (
          <img
            key={index}
            src={`data:image/jpeg;base64,${image}`}
            alt={`Capture ${index + 1}`}
            className="preview-image"
          />
        ))}
        {images.length > 3 && (
          <div className="more-images">
            +{images.length - 3} more
          </div>
        )}
      </div>
    </div>
  );
};
