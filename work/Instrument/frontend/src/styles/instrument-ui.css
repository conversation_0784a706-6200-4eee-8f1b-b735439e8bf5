/* Import assistant-ui base styles */
@import "@assistant-ui/styles/index.css";
@import "@assistant-ui/styles/markdown.css";

/* The Instrument App Custom Styles */
.instrument-app {
  height: 100vh;
  width: 100vw;
  position: relative;
  background: linear-gradient(135deg, #1e1e2e 0%, #2d2d44 100%);
  color: #ffffff;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  overflow: hidden;
}

/* Status Bar */
.status-bar {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 10px;
  background: rgba(0, 0, 0, 0.7);
  padding: 8px 16px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.status.connected {
  color: #4ade80;
}

.status.disconnected {
  color: #f87171;
}

.device-info {
  color: #94a3b8;
  font-size: 0.9em;
}

/* Main Layout */
.instrument-layout {
  display: grid;
  grid-template-columns: 250px 1fr;
  height: 100vh;
  gap: 1rem;
  padding: 1rem;
}

/* Thread List Styling */
.aui-thread-list-root {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 1rem;
  overflow-y: auto;
}

.aui-thread-list-new {
  width: 100%;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 10px;
  padding: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.aui-thread-list-new:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.aui-thread-list-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: 8px;
  margin-bottom: 4px;
  transition: background-color 0.2s ease;
}

.aui-thread-list-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.aui-thread-list-item-trigger {
  flex: 1;
  text-align: left;
  background: none;
  border: none;
  color: inherit;
  padding: 4px 8px;
  border-radius: 4px;
}

.aui-thread-list-item-title {
  margin: 0;
  font-size: 0.9em;
  color: #e2e8f0;
}

/* Thread Styling */
.aui-thread-root {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.aui-thread-viewport {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.aui-thread-viewport-footer {
  padding: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Welcome Screen */
.aui-thread-welcome-root {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  text-align: center;
}

.aui-thread-welcome-center {
  margin-bottom: 2rem;
}

.aui-thread-welcome-message {
  font-size: 1.5em;
  font-weight: 600;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.aui-thread-welcome-submessage {
  color: #94a3b8;
  font-size: 1em;
  margin-bottom: 0.5rem;
}

.aui-thread-welcome-instructions {
  color: #94a3b8;
  font-size: 0.9em;
}

.aui-thread-welcome-suggestions {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  justify-content: center;
}

.aui-thread-welcome-suggestion {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 0.9em;
  color: #e2e8f0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.aui-thread-welcome-suggestion:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

/* Device Controls */
.device-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.5rem;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 10px;
}

.capture-control-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px;
  transition: all 0.3s ease;
}

.capture-control-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.send-control-btn {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px;
  transition: all 0.3s ease;
}

.send-control-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.device-status {
  margin-left: auto;
  font-size: 0.9em;
}

.status-connected {
  color: #4ade80;
}

.status-disconnected {
  color: #f87171;
}

/* Composer */
.aui-composer-root {
  display: flex;
  align-items: flex-end;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 0.5rem;
}

.aui-composer-input {
  flex: 1;
  background: transparent;
  border: none;
  color: white;
  font-size: 14px;
  resize: none;
  outline: none;
  min-height: 40px;
  padding: 8px;
}

.aui-composer-input::placeholder {
  color: #94a3b8;
}

.aui-composer-send,
.aui-composer-cancel {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px;
  transition: all 0.3s ease;
}

.aui-composer-send:hover,
.aui-composer-cancel:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
}

/* Messages */
.aui-user-message-root,
.aui-assistant-message-root {
  margin: 1rem;
  padding: 1rem;
  border-radius: 15px;
  position: relative;
}

.aui-user-message-root {
  background: rgba(59, 130, 246, 0.2);
  border-left: 4px solid #3b82f6;
  margin-left: 2rem;
}

.aui-assistant-message-root {
  background: rgba(16, 185, 129, 0.2);
  border-left: 4px solid #10b981;
  margin-right: 2rem;
}

.aui-user-message-content,
.aui-assistant-message-content {
  line-height: 1.6;
  word-wrap: break-word;
}

/* Action Bars */
.aui-user-action-bar-root,
.aui-assistant-action-bar-root {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  display: flex;
  gap: 0.25rem;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.aui-user-message-root:hover .aui-user-action-bar-root,
.aui-assistant-message-root:hover .aui-assistant-action-bar-root {
  opacity: 1;
}

/* Branch Picker */
.aui-branch-picker-root {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  font-size: 0.8em;
  color: #94a3b8;
}

/* Buttons */
.aui-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
  outline: none;
}

.aui-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.aui-button-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  padding: 8px 16px;
  border-radius: 8px;
}

.aui-button-outline {
  background: transparent;
  color: #e2e8f0;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 8px 16px;
  border-radius: 8px;
}

.aui-button-ghost {
  background: transparent;
  color: #e2e8f0;
  padding: 8px;
  border-radius: 6px;
}

.aui-button-ghost:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.1);
}

.aui-button-icon {
  padding: 8px;
  border-radius: 6px;
}

/* Tooltips */
.aui-tooltip-content {
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8em;
  z-index: 1000;
}

/* Scrollbars */
.aui-thread-list-root::-webkit-scrollbar,
.aui-thread-viewport::-webkit-scrollbar {
  width: 6px;
}

.aui-thread-list-root::-webkit-scrollbar-track,
.aui-thread-viewport::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.aui-thread-list-root::-webkit-scrollbar-thumb,
.aui-thread-viewport::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.aui-thread-list-root::-webkit-scrollbar-thumb:hover,
.aui-thread-viewport::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Screen reader only */
.aui-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Image Preview */
.image-preview {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.8);
  padding: 15px;
  border-radius: 15px;
  backdrop-filter: blur(10px);
  max-width: 300px;
  z-index: 100;
}

.image-preview h3 {
  margin: 0 0 10px 0;
  font-size: 1em;
  color: #94a3b8;
}

.image-grid {
  display: flex;
  gap: 8px;
  align-items: center;
}

.preview-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 8px;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.more-images {
  color: #94a3b8;
  font-size: 0.9em;
  padding: 0 8px;
}

/* Utility classes */
.absolute {
  position: absolute;
}

.bottom-4 {
  bottom: 1rem;
}

.left-4 {
  left: 1rem;
}

.max-w-xs {
  max-width: 20rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .instrument-layout {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
  }

  .aui-thread-list-root {
    max-height: 200px;
  }

  .image-preview {
    position: relative;
    bottom: auto;
    left: auto;
    margin-top: 20px;
  }
}
