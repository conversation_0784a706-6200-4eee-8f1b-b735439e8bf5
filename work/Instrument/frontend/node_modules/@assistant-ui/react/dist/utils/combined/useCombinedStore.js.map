{"version": 3, "sources": ["../../../src/utils/combined/useCombinedStore.ts"], "sourcesContent": ["\"use client\";\n\nimport { useMemo } from \"react\";\nimport {\n  type CombinedSelector,\n  createCombinedStore,\n  StoreOrRuntime,\n} from \"./createCombinedStore\";\n\nexport const useCombinedStore = <T extends Array<unknown>, R>(\n  stores: { [K in keyof T]: StoreOrRuntime<T[K]> },\n  selector: CombinedSelector<T, R>,\n): R => {\n  // eslint-disable-next-line react-hooks/exhaustive-deps -- shallow-compare the store array\n  const useCombined = useMemo(() => createCombinedStore<T, R>(stores), stores);\n  return useCombined(selector);\n};\n"], "mappings": ";;;AAEA,SAAS,eAAe;AACxB;AAAA,EAEE;AAAA,OAEK;AAEA,IAAM,mBAAmB,CAC9B,QACA,aACM;AAEN,QAAM,cAAc,QAAQ,MAAM,oBAA0B,MAAM,GAAG,MAAM;AAC3E,SAAO,YAAY,QAAQ;AAC7B;", "names": []}