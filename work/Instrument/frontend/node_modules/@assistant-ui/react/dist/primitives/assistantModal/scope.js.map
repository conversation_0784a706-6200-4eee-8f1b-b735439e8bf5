{"version": 3, "sources": ["../../../src/primitives/assistantModal/scope.tsx"], "sourcesContent": ["import * as PopoverPrimitive from \"@radix-ui/react-popover\";\nimport type { Scope } from \"@radix-ui/react-context\";\n\nexport const usePopoverScope = PopoverPrimitive.createPopoverScope();\nexport type ScopedProps<P> = P & { __scopeAssistantModal?: Scope };\n"], "mappings": ";AAAA,YAAY,sBAAsB;AAG3B,IAAM,kBAAmC,oCAAmB;", "names": []}