{"version": 3, "sources": ["../../../src/runtimes/local/index.ts"], "sourcesContent": ["export { useLocalRuntime, useLocalThreadRuntime } from \"./useLocalRuntime\";\nexport type { LocalRuntimeOptions } from \"./LocalRuntimeOptions\";\nexport type {\n  ChatModelAdapter,\n  ChatModelRunOptions,\n  ChatModelRunResult,\n  ChatModelRunUpdate,\n} from \"./ChatModelAdapter\";\n"], "mappings": ";AAAA,SAAS,iBAAiB,6BAA6B;", "names": []}