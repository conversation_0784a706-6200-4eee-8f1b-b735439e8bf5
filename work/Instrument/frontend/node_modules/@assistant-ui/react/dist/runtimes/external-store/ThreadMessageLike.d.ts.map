{"version": 3, "file": "ThreadMessageLike.d.ts", "sourceRoot": "", "sources": ["../../../src/runtimes/external-store/ThreadMessageLike.tsx"], "names": [], "mappings": "AAEA,OAAO,EACL,aAAa,EACb,eAAe,EACf,gBAAgB,EAChB,aAAa,EAMb,kBAAkB,EAClB,eAAe,EACf,yBAAyB,EAC1B,MAAM,aAAa,CAAC;AACrB,OAAO,EACL,oBAAoB,EACpB,iBAAiB,EACjB,UAAU,EACX,MAAM,4BAA4B,CAAC;AACpC,OAAO,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,MAAM,wBAAwB,CAAC;AAE/E,MAAM,MAAM,iBAAiB,GAAG;IAC9B,QAAQ,CAAC,IAAI,EAAE,WAAW,GAAG,MAAM,GAAG,QAAQ,CAAC;IAC/C,QAAQ,CAAC,OAAO,EACZ,MAAM,GACN,SAAS,CACL,eAAe,GACf,oBAAoB,GACpB,iBAAiB,GACjB,gBAAgB,GAChB,eAAe,GACf,yBAAyB,GACzB;QACE,QAAQ,CAAC,IAAI,EAAE,WAAW,CAAC;QAC3B,QAAQ,CAAC,UAAU,CAAC,EAAE,MAAM,CAAC;QAC7B,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC;QAC1B,QAAQ,CAAC,IAAI,CAAC,EAAE,kBAAkB,CAAC;QACnC,QAAQ,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC;QAC3B,QAAQ,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC;QACxB,QAAQ,CAAC,MAAM,CAAC,EAAE,GAAG,GAAG,SAAS,CAAC;QAClC,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;QACvC,QAAQ,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;KACxC,CACJ,EAAE,CAAC;IACR,QAAQ,CAAC,EAAE,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IACjC,QAAQ,CAAC,SAAS,CAAC,EAAE,IAAI,GAAG,SAAS,CAAC;IACtC,QAAQ,CAAC,MAAM,CAAC,EAAE,aAAa,GAAG,SAAS,CAAC;IAC5C,QAAQ,CAAC,WAAW,CAAC,EAAE,SAAS,kBAAkB,EAAE,GAAG,SAAS,CAAC;IACjE,QAAQ,CAAC,QAAQ,CAAC,EACd;QACE,QAAQ,CAAC,cAAc,CAAC,EAAE,iBAAiB,CAAC;QAC5C,QAAQ,CAAC,oBAAoB,CAAC,EAC1B,SAAS,iBAAiB,EAAE,GAC5B,SAAS,CAAC;QACd,QAAQ,CAAC,aAAa,CAAC,EAAE,SAAS,iBAAiB,EAAE,GAAG,SAAS,CAAC;QAClE,QAAQ,CAAC,KAAK,CAAC,EAAE,SAAS,UAAU,EAAE,GAAG,SAAS,CAAC;QACnD,QAAQ,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,SAAS,CAAC;KACvD,GACD,SAAS,CAAC;CACf,CAAC;AAEF,eAAO,MAAM,qBAAqB,GAChC,MAAM,iBAAiB,EACvB,YAAY,MAAM,EAClB,gBAAgB,aAAa,KAC5B,aAmJF,CAAC"}