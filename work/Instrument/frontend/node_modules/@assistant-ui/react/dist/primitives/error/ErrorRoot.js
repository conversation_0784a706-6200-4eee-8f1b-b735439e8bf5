"use client";

// src/primitives/error/ErrorRoot.tsx
import { Primitive } from "@radix-ui/react-primitive";
import { forwardRef } from "react";
import { jsx } from "react/jsx-runtime";
var ErrorPrimitiveRoot = forwardRef((props, forwardRef2) => {
  return /* @__PURE__ */ jsx(Primitive.div, { role: "alert", ...props, ref: forwardRef2 });
});
ErrorPrimitiveRoot.displayName = "ErrorPrimitive.Root";
export {
  ErrorPrimitiveRoot
};
//# sourceMappingURL=ErrorRoot.js.map