"use client";

// src/primitives/threadList/ThreadListRoot.tsx
import { Primitive } from "@radix-ui/react-primitive";
import { forwardRef } from "react";
import { jsx } from "react/jsx-runtime";
var ThreadListPrimitiveRoot = forwardRef((props, ref) => {
  return /* @__PURE__ */ jsx(Primitive.div, { ...props, ref });
});
ThreadListPrimitiveRoot.displayName = "ThreadListPrimitive.Root";
export {
  ThreadListPrimitiveRoot
};
//# sourceMappingURL=ThreadListRoot.js.map