{"version": 3, "sources": ["../../../src/primitives/thread/ThreadRoot.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { type ComponentRef, forwardRef, ComponentPropsWithoutRef } from \"react\";\n\nexport namespace ThreadPrimitiveRoot {\n  export type Element = ComponentRef<typeof Primitive.div>;\n  /**\n   * Props for the ThreadPrimitive.Root component.\n   * Accepts all standard div element props.\n   */\n  export type Props = ComponentPropsWithoutRef<typeof Primitive.div>;\n}\n\n/**\n * The root container component for a thread.\n *\n * This component serves as the foundational wrapper for all thread-related components.\n * It provides the basic structure and context needed for thread functionality.\n *\n * @example\n * ```tsx\n * <ThreadPrimitive.Root>\n *   <ThreadPrimitive.Viewport>\n *     <ThreadPrimitive.Messages components={{ Message: MyMessage }} />\n *   </ThreadPrimitive.Viewport>\n * </ThreadPrimitive.Root>\n * ```\n */\nexport const ThreadPrimitiveRoot = forwardRef<\n  ThreadPrimitiveRoot.Element,\n  ThreadPrimitiveRoot.Props\n>((props, ref) => {\n  return <Primitive.div {...props} ref={ref} />;\n});\n\nThreadPrimitiveRoot.displayName = \"ThreadPrimitive.Root\";\n"], "mappings": ";;;AAEA,SAAS,iBAAiB;AAC1B,SAA4B,kBAA4C;AA8B/D;AAJF,IAAM,sBAAsB,WAGjC,CAAC,OAAO,QAAQ;AAChB,SAAO,oBAAC,UAAU,KAAV,EAAe,GAAG,OAAO,KAAU;AAC7C,CAAC;AAED,oBAAoB,cAAc;", "names": []}