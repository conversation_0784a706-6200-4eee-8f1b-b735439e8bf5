{"version": 3, "sources": ["../../../src/primitives/assistantModal/AssistantModalRoot.tsx"], "sourcesContent": ["\"use client\";\n\nimport { FC, useEffect, useState } from \"react\";\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\";\nimport { ScopedProps, usePopoverScope } from \"./scope\";\nimport { useThreadRuntime } from \"../../context\";\n\nexport namespace AssistantModalPrimitiveRoot {\n  export type Props = PopoverPrimitive.PopoverProps & {\n    unstable_openOnRunStart?: boolean | undefined;\n  };\n}\n\nconst useAssistantModalOpenState = ({\n  defaultOpen = false,\n  unstable_openOnRunStart = true,\n}: {\n  defaultOpen?: boolean | undefined;\n  unstable_openOnRunStart?: boolean | undefined;\n}) => {\n  const state = useState(defaultOpen);\n\n  const [, setOpen] = state;\n  const threadRuntime = useThreadRuntime();\n  useEffect(() => {\n    if (!unstable_openOnRunStart) return undefined;\n\n    return threadRuntime.unstable_on(\"run-start\", () => {\n      setOpen(true);\n    });\n  }, [unstable_openOnRunStart, setOpen, threadRuntime]);\n\n  return state;\n};\n\nexport const AssistantModalPrimitiveRoot: FC<\n  AssistantModalPrimitiveRoot.Props\n> = ({\n  __scopeAssistantModal,\n  defaultOpen,\n  unstable_openOnRunStart,\n  open,\n  onOpenChange,\n  ...rest\n}: ScopedProps<AssistantModalPrimitiveRoot.Props>) => {\n  const scope = usePopoverScope(__scopeAssistantModal);\n\n  const [modalOpen, setOpen] = useAssistantModalOpenState({\n    defaultOpen,\n    unstable_openOnRunStart,\n  });\n\n  const openChangeHandler = (open: boolean) => {\n    onOpenChange?.(open);\n    setOpen(open);\n  };\n\n  return (\n    <PopoverPrimitive.Root\n      {...scope}\n      open={open === undefined ? modalOpen : open}\n      onOpenChange={openChangeHandler}\n      {...rest}\n    />\n  );\n};\n\nAssistantModalPrimitiveRoot.displayName = \"AssistantModalPrimitive.Root\";\n"], "mappings": ";;;AAEA,SAAa,WAAW,gBAAgB;AACxC,YAAY,sBAAsB;AAClC,SAAsB,uBAAuB;AAC7C,SAAS,wBAAwB;AAqD7B;AA7CJ,IAAM,6BAA6B,CAAC;AAAA,EAClC,cAAc;AAAA,EACd,0BAA0B;AAC5B,MAGM;AACJ,QAAM,QAAQ,SAAS,WAAW;AAElC,QAAM,CAAC,EAAE,OAAO,IAAI;AACpB,QAAM,gBAAgB,iBAAiB;AACvC,YAAU,MAAM;AACd,QAAI,CAAC,wBAAyB,QAAO;AAErC,WAAO,cAAc,YAAY,aAAa,MAAM;AAClD,cAAQ,IAAI;AAAA,IACd,CAAC;AAAA,EACH,GAAG,CAAC,yBAAyB,SAAS,aAAa,CAAC;AAEpD,SAAO;AACT;AAEO,IAAM,8BAET,CAAC;AAAA,EACH;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,GAAG;AACL,MAAsD;AACpD,QAAM,QAAQ,gBAAgB,qBAAqB;AAEnD,QAAM,CAAC,WAAW,OAAO,IAAI,2BAA2B;AAAA,IACtD;AAAA,IACA;AAAA,EACF,CAAC;AAED,QAAM,oBAAoB,CAACA,UAAkB;AAC3C,mBAAeA,KAAI;AACnB,YAAQA,KAAI;AAAA,EACd;AAEA,SACE;AAAA,IAAkB;AAAA,IAAjB;AAAA,MACE,GAAG;AAAA,MACJ,MAAM,SAAS,SAAY,YAAY;AAAA,MACvC,cAAc;AAAA,MACb,GAAG;AAAA;AAAA,EACN;AAEJ;AAEA,4BAA4B,cAAc;", "names": ["open"]}