{"version": 3, "sources": ["../../../src/runtimes/local/LocalThreadListRuntimeCore.tsx"], "sourcesContent": ["import { ThreadListRuntimeCore } from \"../core/ThreadListRuntimeCore\";\nimport { BaseSubscribable } from \"../remote-thread-list/BaseSubscribable\";\nimport { LocalThreadRuntimeCore } from \"./LocalThreadRuntimeCore\";\n\nexport type LocalThreadFactory = () => LocalThreadRuntimeCore;\n\nconst EMPTY_ARRAY = Object.freeze([]);\nexport class LocalThreadListRuntimeCore\n  extends BaseSubscribable\n  implements ThreadListRuntimeCore\n{\n  private _mainThread: LocalThreadRuntimeCore;\n  constructor(_threadFactory: LocalThreadFactory) {\n    super();\n\n    this._mainThread = _threadFactory();\n  }\n\n  public get isLoading() {\n    return false;\n  }\n\n  public getMainThreadRuntimeCore() {\n    return this._mainThread;\n  }\n\n  public get newThreadId(): string {\n    throw new Error(\"Method not implemented.\");\n  }\n\n  public get threadIds(): readonly string[] {\n    throw EMPTY_ARRAY;\n  }\n\n  public get archivedThreadIds(): readonly string[] {\n    throw EMPTY_ARRAY;\n  }\n\n  public get mainThreadId(): string {\n    return \"__DEFAULT_ID__\";\n  }\n\n  public getThreadRuntimeCore(): never {\n    throw new Error(\"Method not implemented.\");\n  }\n\n  public getLoadThreadsPromise(): Promise<void> {\n    throw new Error(\"Method not implemented.\");\n  }\n\n  public getItemById(threadId: string) {\n    if (threadId === this.mainThreadId) {\n      return {\n        status: \"regular\" as const,\n        threadId: this.mainThreadId,\n        remoteId: this.mainThreadId,\n        externalId: undefined,\n        title: undefined,\n        isMain: true,\n      };\n    }\n    throw new Error(\"Method not implemented\");\n  }\n\n  public async switchToThread(): Promise<void> {\n    throw new Error(\"Method not implemented.\");\n  }\n\n  public switchToNewThread(): Promise<void> {\n    throw new Error(\"Method not implemented.\");\n  }\n\n  public rename(): Promise<void> {\n    throw new Error(\"Method not implemented.\");\n  }\n\n  public archive(): Promise<void> {\n    throw new Error(\"Method not implemented.\");\n  }\n\n  public detach(): Promise<void> {\n    throw new Error(\"Method not implemented.\");\n  }\n\n  public unarchive(): Promise<void> {\n    throw new Error(\"Method not implemented.\");\n  }\n\n  public delete(): Promise<void> {\n    throw new Error(\"Method not implemented.\");\n  }\n\n  public initialize(): never {\n    throw new Error(\"Method not implemented.\");\n  }\n\n  public generateTitle(): never {\n    throw new Error(\"Method not implemented.\");\n  }\n}\n"], "mappings": ";AACA,SAAS,wBAAwB;AAKjC,IAAM,cAAc,OAAO,OAAO,CAAC,CAAC;AAC7B,IAAM,6BAAN,cACG,iBAEV;AAAA,EACU;AAAA,EACR,YAAY,gBAAoC;AAC9C,UAAM;AAEN,SAAK,cAAc,eAAe;AAAA,EACpC;AAAA,EAEA,IAAW,YAAY;AACrB,WAAO;AAAA,EACT;AAAA,EAEO,2BAA2B;AAChC,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAW,cAAsB;AAC/B,UAAM,IAAI,MAAM,yBAAyB;AAAA,EAC3C;AAAA,EAEA,IAAW,YAA+B;AACxC,UAAM;AAAA,EACR;AAAA,EAEA,IAAW,oBAAuC;AAChD,UAAM;AAAA,EACR;AAAA,EAEA,IAAW,eAAuB;AAChC,WAAO;AAAA,EACT;AAAA,EAEO,uBAA8B;AACnC,UAAM,IAAI,MAAM,yBAAyB;AAAA,EAC3C;AAAA,EAEO,wBAAuC;AAC5C,UAAM,IAAI,MAAM,yBAAyB;AAAA,EAC3C;AAAA,EAEO,YAAY,UAAkB;AACnC,QAAI,aAAa,KAAK,cAAc;AAClC,aAAO;AAAA,QACL,QAAQ;AAAA,QACR,UAAU,KAAK;AAAA,QACf,UAAU,KAAK;AAAA,QACf,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,QAAQ;AAAA,MACV;AAAA,IACF;AACA,UAAM,IAAI,MAAM,wBAAwB;AAAA,EAC1C;AAAA,EAEA,MAAa,iBAAgC;AAC3C,UAAM,IAAI,MAAM,yBAAyB;AAAA,EAC3C;AAAA,EAEO,oBAAmC;AACxC,UAAM,IAAI,MAAM,yBAAyB;AAAA,EAC3C;AAAA,EAEO,SAAwB;AAC7B,UAAM,IAAI,MAAM,yBAAyB;AAAA,EAC3C;AAAA,EAEO,UAAyB;AAC9B,UAAM,IAAI,MAAM,yBAAyB;AAAA,EAC3C;AAAA,EAEO,SAAwB;AAC7B,UAAM,IAAI,MAAM,yBAAyB;AAAA,EAC3C;AAAA,EAEO,YAA2B;AAChC,UAAM,IAAI,MAAM,yBAAyB;AAAA,EAC3C;AAAA,EAEO,SAAwB;AAC7B,UAAM,IAAI,MAAM,yBAAyB;AAAA,EAC3C;AAAA,EAEO,aAAoB;AACzB,UAAM,IAAI,MAAM,yBAAyB;AAAA,EAC3C;AAAA,EAEO,gBAAuB;AAC5B,UAAM,IAAI,MAAM,yBAAyB;AAAA,EAC3C;AACF;", "names": []}