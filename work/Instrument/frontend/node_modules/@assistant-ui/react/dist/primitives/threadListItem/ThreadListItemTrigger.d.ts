import { ActionButtonElement, ActionButtonProps } from "../../utils/createActionButton";
declare const useThreadListItemTrigger: () => () => void;
export declare namespace ThreadListItemPrimitiveTrigger {
    type Element = ActionButtonElement;
    type Props = ActionButtonProps<typeof useThreadListItemTrigger>;
}
export declare const ThreadListItemPrimitiveTrigger: import("react").ForwardRefExoticComponent<Omit<import("react").ClassAttributes<HTMLButtonElement> & import("react").ButtonHTMLAttributes<HTMLButtonElement> & {
    asChild?: boolean;
}, "ref"> & import("react").RefAttributes<HTMLButtonElement>>;
export {};
//# sourceMappingURL=ThreadListItemTrigger.d.ts.map