import { ThreadState } from "../../api";
import { ThreadMessage } from "../../types";
export declare const symbolInnerMessage: unique symbol;
/**
 * @deprecated Use `getExternalStoreMessages` (plural) instead. This function will be removed in 0.8.0.
 */
export declare const getExternalStoreMessage: <T>(input: ThreadMessage) => T | T[] | undefined;
export declare const getExternalStoreMessages: <T>(input: ThreadState | ThreadMessage | ThreadMessage["content"][number]) => T[];
//# sourceMappingURL=getExternalStoreMessage.d.ts.map