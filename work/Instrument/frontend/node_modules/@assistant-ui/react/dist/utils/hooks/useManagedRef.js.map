{"version": 3, "sources": ["../../../src/utils/hooks/useManagedRef.ts"], "sourcesContent": ["import { useCallback, useRef } from \"react\";\n\nexport const useManagedRef = <TNode>(\n  callback: (node: TNode) => (() => void) | void,\n) => {\n  const cleanupRef = useRef<(() => void) | void>(undefined);\n\n  const ref = useCallback(\n    (el: TNode | null) => {\n      // Call the previous cleanup function\n      if (cleanupRef.current) {\n        cleanupRef.current();\n      }\n\n      // Call the new callback and store its cleanup function\n      if (el) {\n        cleanupRef.current = callback(el);\n      }\n    },\n    [callback],\n  );\n\n  return ref;\n};\n"], "mappings": ";AAAA,SAAS,aAAa,cAAc;AAE7B,IAAM,gBAAgB,CAC3B,aACG;AACH,QAAM,aAAa,OAA4B,MAAS;AAExD,QAAM,MAAM;AAAA,IACV,CAAC,OAAqB;AAEpB,UAAI,WAAW,SAAS;AACtB,mBAAW,QAAQ;AAAA,MACrB;AAGA,UAAI,IAAI;AACN,mBAAW,UAAU,SAAS,EAAE;AAAA,MAClC;AAAA,IACF;AAAA,IACA,CAAC,QAAQ;AAAA,EACX;AAEA,SAAO;AACT;", "names": []}