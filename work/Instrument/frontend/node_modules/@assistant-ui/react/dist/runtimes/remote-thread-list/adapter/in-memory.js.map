{"version": 3, "sources": ["../../../../src/runtimes/remote-thread-list/adapter/in-memory.tsx"], "sourcesContent": ["import { Assistant<PERSON><PERSON><PERSON>, AssistantStreamChunk } from \"assistant-stream\";\nimport {\n  RemoteThreadInitializeResponse,\n  RemoteThreadListAdapter,\n  RemoteThreadListResponse,\n} from \"../types\";\n\nexport class InMemoryThreadListAdapter implements RemoteThreadListAdapter {\n  list(): Promise<RemoteThreadListResponse> {\n    return Promise.resolve({\n      threads: [],\n    });\n  }\n\n  rename(): Promise<void> {\n    return Promise.resolve();\n  }\n\n  archive(): Promise<void> {\n    return Promise.resolve();\n  }\n\n  unarchive(): Promise<void> {\n    return Promise.resolve();\n  }\n\n  delete(): Promise<void> {\n    return Promise.resolve();\n  }\n\n  initialize(threadId: string): Promise<RemoteThreadInitializeResponse> {\n    return Promise.resolve({ remoteId: threadId, externalId: undefined });\n  }\n\n  generateTitle(): Promise<AssistantStream> {\n    return Promise.resolve(new ReadableStream<AssistantStreamChunk>());\n  }\n}\n"], "mappings": ";AAOO,IAAM,4BAAN,MAAmE;AAAA,EACxE,OAA0C;AACxC,WAAO,QAAQ,QAAQ;AAAA,MACrB,SAAS,CAAC;AAAA,IACZ,CAAC;AAAA,EACH;AAAA,EAEA,SAAwB;AACtB,WAAO,QAAQ,QAAQ;AAAA,EACzB;AAAA,EAEA,UAAyB;AACvB,WAAO,QAAQ,QAAQ;AAAA,EACzB;AAAA,EAEA,YAA2B;AACzB,WAAO,QAAQ,QAAQ;AAAA,EACzB;AAAA,EAEA,SAAwB;AACtB,WAAO,QAAQ,QAAQ;AAAA,EACzB;AAAA,EAEA,WAAW,UAA2D;AACpE,WAAO,QAAQ,QAAQ,EAAE,UAAU,UAAU,YAAY,OAAU,CAAC;AAAA,EACtE;AAAA,EAEA,gBAA0C;AACxC,WAAO,QAAQ,QAAQ,IAAI,eAAqC,CAAC;AAAA,EACnE;AACF;", "names": []}