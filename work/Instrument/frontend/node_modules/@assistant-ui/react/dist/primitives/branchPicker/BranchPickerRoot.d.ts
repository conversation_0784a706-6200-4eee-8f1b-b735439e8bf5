import { Primitive } from "@radix-ui/react-primitive";
import { type ComponentRef, ComponentPropsWithoutRef } from "react";
export declare namespace BranchPickerPrimitiveRoot {
    type Element = ComponentRef<typeof Primitive.div>;
    type Props = ComponentPropsWithoutRef<typeof Primitive.div> & {
        /**
         * Whether to hide the branch picker when there's only one branch available.
         * When true, the component will only render when multiple branches exist.
         * @default false
         */
        hideWhenSingleBranch?: boolean | undefined;
    };
}
/**
 * The root container for branch picker components.
 *
 * This component provides a container for branch navigation controls,
 * with optional conditional rendering based on the number of available branches.
 * It integrates with the message branching system to allow users to navigate
 * between different response variations.
 *
 * @example
 * ```tsx
 * <BranchPickerPrimitive.Root hideWhenSingleBranch={true}>
 *   <BranchPickerPrimitive.Previous />
 *   <BranchPickerPrimitive.Count />
 *   <BranchPickerPrimitive.Next />
 * </BranchPickerPrimitive.Root>
 * ```
 */
export declare const BranchPickerPrimitiveRoot: import("react").ForwardRefExoticComponent<Omit<import("react").ClassAttributes<HTMLDivElement> & import("react").HTMLAttributes<HTMLDivElement> & {
    asChild?: boolean;
}, "ref"> & {
    /**
     * Whether to hide the branch picker when there's only one branch available.
     * When true, the component will only render when multiple branches exist.
     * @default false
     */
    hideWhenSingleBranch?: boolean | undefined;
} & import("react").RefAttributes<HTMLDivElement>>;
//# sourceMappingURL=BranchPickerRoot.d.ts.map